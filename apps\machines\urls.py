from django.urls import path

from .views import (
    DeviceTypesView, DeviceTypeDetailView,
    DevicesView, DeviceDetailView,
    FunctionsView, FunctionDetailView,
    MachinesView, MachineDetailView, MachineDetailByMNumberView,
    MachineReservationsView, MachineReservationDetailView, MachineReservationCancelView,
    MachineUsageRecordsView, MachineReservationTimelineView,
    MachineReservationCheckinView,
)

urlpatterns = [
    path("/device_types", DeviceTypesView.as_view()),
    path("/device_types/<int:pk>", DeviceTypeDetailView.as_view()),
    path("/devices", DevicesView.as_view()),
    path("/devices/<int:pk>", DeviceDetailView.as_view()),
    path("/functions", FunctionsView.as_view()),
    path("/functions/<int:pk>", FunctionDetailView.as_view()),
    path("/reservations/timeline", MachineReservationTimelineView.as_view()),
    path("/reservations/cu", MachineReservationsView.as_view()),
    path("/reservations/checkin", MachineReservationCheckinView.as_view()),
    path("/reservations", MachineReservationsView.as_view()),
    path("/reservations/<int:pk>", MachineReservationDetailView.as_view()),
    path("/reservations/<int:pk>/cancel", MachineReservationCancelView.as_view()),
    path("/usage_records", MachineUsageRecordsView.as_view()),
    path("", MachinesView.as_view()),
    path("/detail/by_number", MachineDetailByMNumberView.as_view()),
    path("/<int:pk>", MachineDetailView.as_view()),

]
