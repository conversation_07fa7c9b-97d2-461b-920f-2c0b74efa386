from django.urls import path

from .views import (
    InspecItemsView, InspecItemDetailView,
    InspecPersonsView, InspecPersonDetailView,
    InspecRecordsView, InspecRecordDetailView,
    DispatchTaskView, RecordsDownloadView,
)

urlpatterns = [
    path("/items", InspecItemsView.as_view()),

    path("/items/<int:pk>", InspecItemDetailView.as_view()),

    path("/persons", InspecPersonsView.as_view()),

    path("/persons/<int:pk>", InspecPersonDetailView.as_view()),

    path("/records", InspecRecordsView.as_view()),

    path("/records/<int:pk>", InspecRecordDetailView.as_view()),

    path("/tasks", DispatchTaskView.as_view()),

    path("/records/excel_report", RecordsDownloadView.as_view()),
]
