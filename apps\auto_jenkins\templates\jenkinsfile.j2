pipeline {
    {% if agent == 'any' %}
    agent any
    {% else %}
    agent { label '{{ agent }}' }
    {% endif %}

    tools {
        git 'Default'
    }

    parameters {
        string(name: 'task_id', defaultValue: '', description: 'build task id')
        {% if parameters %}
        {% for p in parameters %}
        {% if p.type == 'string' %}
        string(name: '{{ p.name }}', defaultValue: '{{ p.default_value | replace("\\", "\\\\") | replace("\"", "\\\"") }}', description: '{{ p.description | replace("\"", "\\\"") }}')
        {% elif p.type == 'choice' %}
        choice(name: '{{ p.name }}', choices: {{ p.choices | tojson }}, description: '{{ p.description | replace("\"", "\\\"") }}')
        {% elif p.type == 'boolean' %}
        booleanParam(name: '{{ p.name }}', defaultValue: {{ 'true' if p.default_value else 'false' }}, description: '{{ p.description | replace("\"", "\\\"") }}')
        {% endif %}
        {% endfor %}
        {% endif %}
    }

    environment {
        VERSION_FILE = "${env.WORKSPACE}\\version\\version.properties"
        {% if environment %}
        {% for k,v in environment.items() %}
        {{ k }} = "{{ v | replace('\\', '\\\\') | replace('"', '\\"') }}"
        {% endfor %}
        {% endif %}
        PATH = "C:\\Program Files\\Git\\bin;C:\\Windows\\System32;C:\\Windows;${env.PATH}"
        GIT_AUTHOR_NAME = "xingt1"
        GIT_AUTHOR_EMAIL = "<EMAIL>"
        GIT_COMMITTER_NAME = "xingt1"
        GIT_COMMITTER_EMAIL = "<EMAIL>"
    }

    stages {
        {% if git_url %}
        stage('Git Checkout') {
            steps {
                script {
                    // 清理工作目录
                    deleteDir()
                    
                    // 克隆 Git 仓库
                        script {
                            def originalUrl = '{{ git_url }}'
                            def gitUrl = originalUrl
                            echo "cred={{ credentials_id|default('NONE') }}"
                            {% if credentials_id and credentials_id.strip() %}
                            // 如果有凭据，保持原始URL格式（支持SSH）
                            echo "使用凭据克隆Git仓库: ${gitUrl}"
                            try {
                                git url: gitUrl, branch: '{{ branch | default("master") }}', credentialsId: '{{ credentials_id }}'
                            } catch (Exception e) {
                                echo "SSH克隆失败，尝试转换为HTTP: ${e.getMessage()}"
                                if (originalUrl.startsWith('git@')) {
                                    def parts = originalUrl.replace('git@', '').replace('.git', '').split(':')
                                    if (parts.length >= 2) {
                                        gitUrl = "http://${parts[0]}/${parts[1]}.git"
                                        echo "转换后的Git URL: ${gitUrl}"
                                        git url: gitUrl, branch: '{{ branch | default("master") }}', credentialsId: '{{ credentials_id }}'
                                    }
                                } else {
                                    throw e
                                }
                            }
                            {% else %}
                            // 如果没有凭据，尝试转换SSH为HTTP（兼容旧逻辑）
                        if (originalUrl.startsWith('git@')) {
                            def parts = originalUrl.replace('git@', '').replace('.git', '').split(':')
                            if (parts.length >= 2) {
                                gitUrl = "http://${parts[0]}/${parts[1]}.git"
                            }
                        }
                        echo "转换后的Git URL: ${gitUrl}"
                        git url: gitUrl, branch: '{{ branch | default("master") }}'
                        {% endif %}
                        env.GIT_COMMIT = powershell(returnStdout: true, script: 'git rev-parse HEAD').trim()   //把hash值  注入到这个环境变量中 供jenkins tool 获取 
                        echo "当前提交哈希: ${env.GIT_COMMIT}"   
                    }
                    
                    {% if engineering_path %}
                    // 如果指定了工程路径，切换到该目录
                    dir('{{ engineering_path }}') {
                        echo "当前工作目录: ${pwd()}"
                        
                        // 显示目录内容
                        if (isUnix()) {
                            sh 'ls -la'
                        } else {
                            bat 'dir'
                        }
                    }
                    {% else %}
                    echo "当前工作目录: ${pwd()}"
                    
                    // 显示目录内容
                    if (isUnix()) {
                        sh 'ls -la'
                    } else {
                        bat 'dir'
                    }
                    {% endif %}
                }
            }
        }
        {% endif %}

        stage('Load Properties') {
            steps {
                script {
                    if (fileExists(env.VERSION_FILE)) {
                        readFile(env.VERSION_FILE).split('\n').each { line ->
                            line = line.trim()
                            if (line && !line.startsWith('#') && line.contains('=')) {
                                def parts = line.split('=', 2)
                                if (parts.length == 2) {
                                    def key   = parts[0].trim()
                                    def value = parts[1].trim()
                                    env."${key}" = value
                                    echo "设置环境变量: ${key} = ${value}"
                                }
                            }
                        }
                    } else {
                         echo "⚠️ Properties 文件不存在，跳过加载: ${env.VERSION_FILE}"
                    }
                }
            }
        }

        {% for st in stages %}
        stage('{{ st.name }}') {
            steps {
                echo "执行Stage: {{ st.name }}"
                {% if environment %}
                {% for k,v in environment.items() %}
                echo "环境变量 {{ k }} = ${env.{{ k }}}"
                {% endfor %}
                {% endif %}
                {% if st.script_b64 and st.script_type == 'raw_script' %}
                script {
                    echo "执行Stage: {{ st.name }}"
                    
                    // 解码base64脚本并执行
                    def rawB64 = '{{ st.script_b64 }}'
                    def scriptContent = new String(java.util.Base64.getDecoder().decode(rawB64), 'UTF-8')
                    def scriptDir = "scripts"
                    def scriptFile = "${scriptDir}/stage_{{ loop.index }}.bat"
                    
                    // 创建脚本目录
                    if (isUnix()) {
                        sh "mkdir -p ${scriptDir}"
                    } else {
                        bat "if not exist ${scriptDir} mkdir ${scriptDir}"
                    }
                    writeFile file: scriptFile, text: scriptContent

                    // 执行脚本
                    if (isUnix()) {
                        sh "chmod +x ${scriptFile}"
                        sh "./${scriptFile}"
                    } else {
                        bat "\"%CD%\\${scriptFile}\""
                    }
                }
                {% elif st.script_block %}
                script {
                    echo "执行Stage: {{ st.name }}"
                    {{ st.script_block | safe }}
                }
                {% elif st.commands %}
                {% for c in st.commands %}
                {% if c.type == 'script' %}
                script {
                    {{ c.content | safe }} 
                }
                {% else %}
                bat """
                cd /d %WORKSPACE%
                {{ c.content | safe }}
                """
                {% endif %}
                {% endfor %} 
                {% else %}
                echo "Stage: {{ st.name }}"
                {% endif %}
            }
        }
        {% endfor %}

        {% if enable_git_tag %}          // Git Tag Stage - 可配置启用
        stage('Git Tag') {
            steps {
                script {
                    echo "执行Git Tag操作"

                    def tagName = env.MCU_Folder_Name
                    if (tagName && tagName.trim() != "") {
                        echo "创建Git Tag: ${tagName}"

                        bat """
                            cd /d %WORKSPACE%
                            git tag -a ${tagName} -m "release ${tagName}"
                            git push origin ${tagName}
                        """

                        echo "✅ Git Tag创建成功: ${tagName}"
                    } else {
                        echo "⚠️ MCU_Folder_Name 环境变量为空,跳过Git Tag创建"
                    }
                }
            }
        }
        {% endif %}

        {% if enable_hwupload %}   // HWUpload Stage - 可配置启用
        stage('HWUpload') {
            steps {
                script {
                    echo "执行HWUpload上传操作"

                    def hwuploadPath = "E:\\jenkins\\HWUpload.exe"  // 固定写死这个路径
                    def projectNumber = env.PROJECT_NUMBER
                    def folderName = env.MCU_Folder_Name
                    def versionFile = "${env.WORKSPACE}\\Version\\version.properties"
                    def buildPath = "${env.WORKSPACE}\\Version"
                    def softwareType = env.PROJECT_TYPE
                    def gitCommit = env.GIT_COMMIT
                    def gitUrl = env.GIT_URL
                    def branch = env.GIT_BRANCH

                    if (projectNumber && folderName && softwareType && gitUrl && branch) {
                        echo "HWUpload参数:"
                        echo "  项目编号: ${projectNumber}"
                        echo "  文件夹名: ${folderName}"
                        echo "  软件类型: ${softwareType}"
                        echo "  版本文件: ${versionFile}"
                        echo "  构建路径: ${buildPath}"
                        echo "  Git提交: ${gitCommit}"

                        bat """
                            cd /d %WORKSPACE%
                            "${hwuploadPath}" -p ${projectNumber} -f ${folderName} -v "${versionFile}" -d "${buildPath}" -t ${softwareType} -w False -i ${gitCommit} -g ${gitUrl} -b ${branch}
                        """

                        echo "✅ HWUpload上传完成"
                    } else {
                        echo "⚠️ 缺少必要的环境变量,跳过HWUpload上传"
                        echo "  PROJECT_NUMBER: ${projectNumber}"
                        echo "  MCU_Folder_Name: ${folderName}"
                        echo "  PROJECT_TYPE: ${softwareType}"
                    }
                }
            }
        }
        {% endif %}
    }

    post {
        always {
            script {
                // 构建完成回调
                def buildStatus = currentBuild.currentResult ?: 'UNKNOWN'
                def endTime = new Date().format("yyyy-MM-dd HH:mm:ss")
                def duration = currentBuild.duration ?: 0
                
                echo "构建完成，状态: ${buildStatus}"
                echo "结束时间: ${endTime}"
                echo "执行时长: ${duration}ms"
                
                // 回调Django API
                try {
                    echo "params: ${params}"
                    def taskId = params?.task_id ?: "{{ task_id | default('') }}"
                    echo "Resolved task_id: ${taskId}"
                    echo "当前 GIT_COMMIT = ${env.GIT_COMMIT}"
                    def callbackUrl = "{{ django_base_url | default('http://**********:9000') }}/auto_jenkins/jenkins/callback/"
                       // SRP: 专门的数据验证函数
                    def validateData = { value, defaultValue, name ->
                        if (value == null || value == "") {
                            echo "⚠️ ${name} 为空，使用默认值: ${defaultValue}"  
                            return defaultValue
                        }
                        return value
                    }
                if (taskId && taskId != "") {
                        // DRY: 统一的数据构建
                        def validatedData = [:]
                        validatedData.task_id = validateData(taskId, "UNKNOWN", "taskId")
                        validatedData.status = validateData(buildStatus, "UNKNOWN", "buildStatus")
                        validatedData.build_number = validateData(env.BUILD_NUMBER, "0", "BUILD_NUMBER")
                        validatedData.end_time = validateData(endTime, new Date().format("yyyy-MM-dd HH:mm:ss"), "endTime")
                        validatedData.duration = validateData(duration, 0, "duration")
                        validatedData.jenkins_url = validateData(env.BUILD_URL, "UNKNOWN", "BUILD_URL")
                        def jsonPayload = """{"task_id":"${taskId}","status":"${buildStatus}","build_number":"${env.BUILD_NUMBER}","end_time":"${endTime}","duration":${duration},"jenkins_url":"${env.BUILD_URL}"}"""


                        echo "生成的JSON: ${jsonPayload}"

                        writeFile file: 'jenkins_callback.json', text: jsonPayload


                        if (isUnix()) {
                            sh """
                                curl -X POST \\
                                    -H "Content-Type: application/json" \\
                                    -d '${jsonPayload}' \\
                                    "${callbackUrl}" \\
                                    --connect-timeout 10 \\
                                    --max-time 30
                            """
                        } else {
                            // Windows环境
                            bat """
                                curl -X POST ^
                                    -H "Content-Type: application/json" ^
                                    -d @jenkins_callback.json ^
                                    "${callbackUrl}" ^
                                    --connect-timeout 10 ^
                                    --max-time 30
                            """
                            echo "=== 清理临时文件 ==="
                            if (isUnix()) {
                                sh "rm -f jenkins_callback.json"
                            } else {
                                bat "del jenkins_callback.json"
                            }

                        }
                        echo "回调请求成功"
                    } else {
                        echo "⚠️ 未提供task_id,跳过回调"
                    }
                } catch (Exception e) {
                    echo "⚠️ 回调失败: ${e.getMessage()}"            // 回调失败不影响构建结果
                }
            }
        }
        {% if post_actions %}
            {% if post_actions.success %}
            success {
                {% for a in post_actions.success %}{{ a }}{% endfor %}
            }
            {% endif %}
            {% if post_actions.failure %}
            failure {
                {% for a in post_actions.failure %}{{ a }}{% endfor %}
            }
            {% endif %}
        {% else %}
        success { echo '✅ 构建成功' }
        failure { echo '❌ 构建失败' }
        {% endif %}
    }
}
