--测试计划表
CREATE TABLE IF NOT EXISTS public.test_plans_v2(
    id serial primary key, --id
    "name" varchar(255),  --名称,
    plan_use varchar(255),  --计划用途,
    plan_type int default 0, --计划类型, 0 对外发布计划 1 内部验证计划
    test_type varchar(255),  --测试类型,
    test_type_name varchar(255),  --测试类型名称,
    test_cases text, --测试用例集合
    test_case_extra_args text, --测试用例额外信息
    test_product_id int, --测试产品id
    project_name varchar(255),   --项目名称
    project_number varchar(255),   --项目编号
    project_id varchar(255),   --项目id
    m_version varchar(255),   --主版本
    sub_versions text,   --次级版本
    product_version text ,   --产品版本
    abnormal_stop bool,  --是否异常停止
    finish_notice bool, --测试完成通知
    "desc" text, --描述
    p_start_time date, --计划开始时间
    p_end_time date,  --计划结束时间
    pic_name varchar(255),   --责任人姓名
    pic_email varchar(255),   --责任人邮箱
    sample_information varchar(255),   --样品信息
    status varchar(255), --状态 DEBUGGING 调试中 REVIEWING 评审中 APPROVED 评审通过 REJECTED 评审不通过 RUNNING 执行中 COMPLETED 已完成
    exec_number int default 1, --执行编号
    creator_name varchar(255),   --创建人姓名
    creator_email varchar(255),   --创建人邮箱
    create_time timestamp, --创建时间
    update_time timestamp, --更新时间
    unique (project_number, "name")
    result_two int,  --测试用例结果2
);
ALTER TABLE public.test_plans_v2 OWNER TO atpms;

CREATE TABLE IF NOT EXISTS public.test_case_result(
    id serial primary key, --id
    test_plan_id int, --测试计划id
    test_case_id int, --测试用例id
    test_case_version varchar(255), --测试用例版本
    exec_id varchar(255), --执行id
    result bool,  --测试用例结果
    result_two int,  --测试用例结果2
    "value" text,  --结果实际值
    remark text, --备注
    generation_mode int, --结果生成方式 0 自动 1 半自动 2 手动
    file varchar(255), --附件
    creator_name varchar(255),   --创建人姓名
    creator_email varchar(255),   --创建人邮箱
    create_time timestamp, --创建时间
    update_time timestamp, --更新时间
    unique (test_plan_id, test_case_id)
);
ALTER TABLE public.test_case_result OWNER TO atpms;


CREATE TABLE IF NOT EXISTS public.at_file (
  id SERIAL PRIMARY KEY,
  pathname VARCHAR(255) NOT NULL DEFAULT '',
  relative_pathname VARCHAR(255) NOT NULL DEFAULT '',
  remote_pathname VARCHAR(255) NOT NULL DEFAULT '',
  title VARCHAR(255) NOT NULL DEFAULT '',
  extension VARCHAR(30) NOT NULL DEFAULT '',
  "size" INTEGER NOT NULL DEFAULT 0,
  "object_type" VARCHAR(30) NOT NULL DEFAULT '',
  "object_id" INTEGER NOT NULL DEFAULT 0,
  gid VARCHAR(48) NOT NULL DEFAULT '',
  "added_by" VARCHAR(30) NOT NULL DEFAULT '',
  "added_date" TIMESTAMP,
  downloads INTEGER NOT NULL DEFAULT 0,
  extra VARCHAR(255) NOT NULL DEFAULT '',
  deleted BOOLEAN NOT NULL DEFAULT FALSE
);
ALTER TABLE public.at_file OWNER TO atpms;