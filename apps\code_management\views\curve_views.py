import logging
import traceback
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db import transaction
from django.db.models.query import QuerySet
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.cache import cache
from django.core.serializers import serialize
import json
import os
import shutil
import time
from pathlib import Path

logger = logging.getLogger("code_management")
from apps.code_management.grpc.host_machine_grpc import host_machine_client
from apps.code_management.grpc.git_grpc import git_client
from apps.code_management.models.project_models import ProjectInfoSerializer, CodeProjectInfo, CodeBranchInfo, BranchInfoSerializer
from apps.code_management.models.config_models import NodeTree, NodeTreeSerializer
from apps.code_management.models.config_models import ParamOriginLocation, BranchDefinedValue
from apps.code_management.models.luminance_models import LuminancecCure, LuminanceDefindValue
from users.models import UserFSInfo
from utils.fs_service import FSService
from utils.fs_app import fs_app


local_path = "/home/<USER>/hwcp"
config_path = "/home/<USER>/hwcp/gRPC_Server/HwcpTest"
config_url = "http://*********/python-team/HwcpTest.git"
config_branch = "main"
sdk_git_path = "http://*********/"



class LumminanceViews(APIView):
    """亮度曲线数据接口视图类"""

    def post(self, request):
        # 初始化变量，避免未定义错误
        x_record = None
        y_record = None
        try:
            # 1. 接收并校验请求参数（增加参数完整性日志）
            params = request.data.get('params', {})
            logger.info("=== 亮度曲线接口 - 接收请求参数 ===")
            logger.info(f"原始请求params: {json.dumps(params, ensure_ascii=False)}")  # 格式化打印字典
            logger.info(f"请求参数是否为空: {not bool(params)}")

            # 提取核心参数（必传参数校验）
            required_params = ["nodeName", "node_level", "project_code",
                               "project_name", "project_gitlab", "project_branch"]
            logger.info(f"必传参数列表: {required_params}")

            missing_params = [p for p in required_params if p not in params or not params[p]]
            if missing_params:
                raise ValueError(f"请求参数缺失或为空: {', '.join(missing_params)}")

            node_name = params["nodeName"]
            node_level = params["node_level"]
            project_params = {
                "project_code": params["project_code"],
                "project_name": params["project_name"],
                "project_gitlab": params["project_gitlab"],
                "project_branch": params["project_branch"],

            }
            logger.info("=== 亮度曲线接口 - 核心参数提取完成 ===")
            logger.info(f"节点信息: node_name={node_name}, node_level={node_level}")
            logger.info(f"项目信息: {json.dumps(project_params, ensure_ascii=False)}")

            # 2. 查询基础亮度曲线数据（X/Y轴）- 修复QuerySet类型注解
            logger.info("=== 亮度曲线接口 - 开始查询基础亮度数据 ===")
            x_record, y_record = self._get_base_luminance_data(node_name, node_level)
            # 增加基础数据详情日志
            logger.info(f"基础数据详情 - X轴: id={x_record.id}, name={x_record.name}, "
                        f"coordinate_type={x_record.coordinate_type}, default={x_record.default[:50]}...")  # 截取前50字符避免日志过长
            logger.info(f"基础数据详情 - Y轴: id={y_record.id}, name={y_record.name}, "
                        f"coordinate_type={y_record.coordinate_type}, default={y_record.default[:50]}...")

            # 3. 查询分支数据并覆盖默认值（增加分支数据预处理日志）
            logger.info("=== 亮度曲线接口 - 开始查询分支自定义数据 ===")
            logger.info(f"分支查询条件: 项目={project_params['project_code']}/{project_params['project_branch']}, "
                        f"节点={node_name}/{node_level}")
            x_data, y_data = self._override_luminance_data_with_branch(x_record, y_record, project_params)
            # 增加覆盖后数据日志
            logger.info(f"分支数据覆盖后 - X轴数据长度: {len(x_data)}, 数据: {x_data}")  # 打印前10条避免日志过长
            logger.info(f"分支数据覆盖后 - Y轴数据长度: {len(y_data)}, 数据: {y_data}")

            # 4. 转换数据类型（字符串→数值）- 增加转换前后对比日志
            logger.info("=== 亮度曲线接口 - 开始数据类型转换 ===")
            logger.info(
                f"转换前 - X轴数据类型: {type(x_data[0]) if x_data else '空'}, Y轴数据类型: {type(y_data[0]) if y_data else '空'}")
            conver_x_data = self._convert_str_list_to_numeric(x_data)
            conver_y_data = self._convert_str_list_to_numeric(y_data)
            logger.info(f"转换后 - X轴数据类型: {type(conver_x_data[0]) if conver_x_data else '空'}, "
                        f"Y轴数据类型: {type(conver_y_data[0]) if conver_y_data else '空'}")
            logger.info(f"转换后 - X轴数据: {conver_x_data}, Y轴数据: {conver_y_data}")  # 截取前10条

            # 5. 构建并返回成功响应（增加响应数据日志）
            logger.info("=== 亮度曲线接口 - 准备返回成功响应 ===")
            response = self._build_luminance_response(
                status=1,
                message="亮度曲线数据获取成功",
                x_record=x_record,
                y_record=y_record,
                x_data=conver_x_data,
                y_data=conver_y_data
            )
            logger.info(f"成功响应数据: {json.dumps(response.data, ensure_ascii=False)}")
            return response

        # 业务逻辑错误（如参数缺失、数据未找到）
        except ValueError as ve:
            logger.error("=== 亮度曲线接口 - 业务逻辑错误 ===", exc_info=True)
            logger.error(f"错误详情: {str(ve)}")
            return self._build_luminance_response(
                status=0,
                message=str(ve),
                x_record=x_record,
                y_record=y_record
            )

        # 其他未知异常
        except Exception as e:
            logger.error("=== 亮度曲线接口 - 内部未知异常 ===", exc_info=True)
            logger.error(f"异常类型: {type(e).__name__}, 异常详情: {str(e)}")
            return self._build_luminance_response(
                status=0,
                message="服务器内部错误",
                x_record=x_record,
                y_record=y_record
            )

    def _parse_default_luminance_data(self, default_str: str) -> list:
        """解析亮度曲线的默认数据（增加解析前后日志）"""
        logger.info(f"=== 解析默认数据 - 原始数据: {default_str[:50]}... ===")  # 截取前50字符
        try:
            json_compatible_str = default_str.replace('{', '[').replace('}', ']')
            logger.info(f"替换后JSON兼容格式: {json_compatible_str[:50]}...")
            parsed_data = json.loads(json_compatible_str)
            logger.info(f"解析成功 - 数据长度: {len(parsed_data)}, 数据类型: {type(parsed_data)}")
            return parsed_data
        except json.JSONDecodeError as e:
            error_msg = f"解析亮度曲线默认数据失败: {str(e)}，原始数据: {default_str[:100]}..."  # 截取前100字符
            logger.error(error_msg)
            raise ValueError(error_msg)
        except Exception as e:
            error_msg = f"处理默认数据时发生未知错误: {str(e)}，原始数据: {default_str[:100]}..."
            logger.error(error_msg)
            raise ValueError(error_msg)

    def _convert_str_list_to_numeric(self, str_list: list) -> list:
        """将字符串列表转换为数值列表（增加转换过程日志）"""
        logger.info(f"=== 数据类型转换 - 待转换列表长度: {len(str_list)} ===")
        numeric_list = []
        for idx, item in enumerate(str_list):  # 只打印前10条的转换过程
            if not isinstance(item, str):
                raise ValueError(f"待转换数据不是字符串类型: 索引{idx}={item}（类型: {type(item)}）")

            try:
                if "." in item:
                    numeric_item = float(item)
                    logger.debug(f"转换索引{idx}: '{item}' → float={numeric_item}")
                else:
                    numeric_item = int(item)
                    logger.debug(f"转换索引{idx}: '{item}' → int={numeric_item}")
                numeric_list.append(numeric_item)
            except (ValueError, TypeError) as e:
                error_msg = f"字符串'{item}'（索引{idx}）无法转换为数值: {str(e)}"
                logger.error(error_msg)
                raise ValueError(error_msg)
        if len(str_list) > 10:
            logger.debug(f"剩余 {len(str_list) - 10} 条数据转换完成（未逐行打印）")
        # 关键日志：确认转换后的数据长度（与原始长度一致）
        logger.info(f"数据转换完成 - 原始长度: {len(str_list)}, 转换后长度: {len(numeric_list)}")
        return numeric_list

    def _get_base_luminance_data(self, node_name: str, node_level: str):
        """从hwcp数据库查询亮度曲线的X轴和Y轴基础默认数据（修复QuerySet类型注解）"""
        # 修复1: 明确导入QuerySet并规范类型注解，避免"未被引用"错误
        lum_queryset: QuerySet[LuminancecCure] = LuminancecCure.objects.using("hwcp").filter(
            var_name=node_name,
            node_level=node_level
        )
        # 修复2: 显式使用QuerySet（如调用count()/first()），避免IDE提示"未被引用"
        total_count = lum_queryset.count()
        logger.info(f"基础数据查询结果总数: {total_count}（查询条件: var_name={node_name}, node_level={node_level}）")

        x_record = lum_queryset.filter(coordinate_type='X').first()
        y_record = lum_queryset.filter(coordinate_type='Y').first()
        logger.info(f"X轴数据查询结果: {'存在' if x_record else '不存在'}")
        logger.info(f"Y轴数据查询结果: {'存在' if y_record else '不存在'}")

        if not x_record:
            raise ValueError(f"未查询到X轴默认数据（node_name={node_name}, node_level={node_level}）")
        if not y_record:
            raise ValueError(f"未查询到Y轴默认数据（node_name={node_name}, node_level={node_level}）")

        logger.info(f"基础数据查询完成 - X轴name={x_record.name}, Y轴name={y_record.name}")
        return x_record, y_record

    def _override_luminance_data_with_branch(self, x_record, y_record, project_params: dict):
        """查询分支自定义数据，覆盖X/Y轴的默认数据并返回解析后的列表（增加分支数据日志）"""
        project_code = project_params.get('project_code')
        project_name = project_params.get('project_name')
        project_gitlab = project_params.get('project_gitlab')
        project_branch = project_params.get('project_branch')

        # 增加分支查询条件日志
        logger.info(f"分支数据查询条件: project_code={project_code}, project_branch={project_branch}, "
                    f"var_name={x_record.var_name}, node_level={x_record.node_level}")

        # 1. 优先查询LuminanceDefindValue的分支数据
        lum_def_data = LuminanceDefindValue.objects.using("hwcp").filter(
            project_code=project_params['project_code'],
            project_name=project_params['project_name'],
            project_gitlab=project_params['project_gitlab'],
            project_branch=project_params['project_branch'],
            node_level=x_record.node_level,
            var_name=x_record.var_name
        ).first()

        # 如果查询到LuminanceDefindValue数据，直接使用
        if lum_def_data and lum_def_data.tempre_map and lum_def_data.temparray_line:
            logger.info(f"使用LuminanceDefindValue的分支数据: {x_record.var_name}")
            return lum_def_data.tempre_map, lum_def_data.temparray_line

        # 2. 未查询到LuminanceDefindValue数据，使用原有逻辑
        logger.info(f"LuminanceDefindValue无数据，使用原有分支逻辑: {x_record.var_name}")

        branch_queryset: QuerySet[BranchDefinedValue] = BranchDefinedValue.objects.using("hwcp").filter(
            project_code=project_code,
            project_name=project_name,
            project_gitlab=project_gitlab,
            project_branch=project_branch,
            var_name=x_record.var_name,
            node_level=x_record.node_level
        ).values_list('arrays', flat=True)

        branch_count = branch_queryset.count()
        logger.info(f"分支数据查询结果总数: {branch_count}")
        if branch_count == 0:
            logger.warning("未查询到任何分支自定义数据，将使用基础默认数据")

        # 初始解析默认数据
        x_data = self._parse_default_luminance_data(x_record.default)
        y_data = self._parse_default_luminance_data(y_record.default)
        logger.info(f"初始解析完成 - X轴默认数据长度: {len(x_data)}, Y轴默认数据长度: {len(y_data)}")

        # 遍历分支数据，覆盖默认值
        if branch_queryset:
            for idx, array_str in enumerate(branch_queryset):
                logger.info(f"=== 处理第{idx + 1}条分支数据 ===")
                if not array_str:
                    logger.warning(f"第{idx + 1}条分支数据的arrays字段为空，跳过处理")
                    continue

                try:
                    logger.info(f"第{idx + 1}条分支arrays原始数据: {array_str[:100]}...")  # 截取前100字符
                    array_data = json.loads(array_str)
                    logger.info(
                        f"第{idx + 1}条分支数据解析结果: 类型={type(array_data)}, 键列表={list(array_data.keys())[:5]}...")  # 打印前5个键

                    if not isinstance(array_data, dict):
                        logger.warning(f"第{idx + 1}条分支arrays数据不是字典格式（类型: {type(array_data)}），跳过处理")
                        continue

                    # 覆盖X轴数据
                    if x_record.params_key in array_data:
                        new_x_default = array_data[x_record.params_key]
                        logger.info(
                            f"第{idx + 1}条分支数据匹配X轴params_key: {x_record.params_key}，新默认值: {new_x_default[:50]}...")
                        x_record.default = new_x_default
                        x_data = self._parse_default_luminance_data(new_x_default)
                        logger.info(f"X轴数据更新后长度: {len(x_data)}")

                    # 覆盖Y轴数据
                    if y_record.params_key in array_data:
                        new_y_default = array_data[y_record.params_key]
                        logger.info(
                            f"第{idx + 1}条分支数据匹配Y轴params_key: {y_record.params_key}，新默认值: {new_y_default[:50]}...")
                        y_record.default = new_y_default
                        y_data = self._parse_default_luminance_data(new_y_default)
                        logger.info(f"Y轴数据更新后长度: {len(y_data)}")

                except json.JSONDecodeError as e:
                    logger.error(f"第{idx + 1}条分支数据解析失败: {str(e)}，原始数据: {array_str[:100]}...")
                except Exception as e:
                    logger.error(f"第{idx + 1}条分支数据处理出错: {str(e)}", exc_info=True)

        logger.info("=== 分支数据处理完成 ===")
        return x_data, y_data

    def _build_luminance_response(self, status: int, message: str,
                                  x_record=None, y_record=None,
                                  x_data=None, y_data=None) -> Response:
        """构建亮度曲线接口的响应对象（增加响应构建日志）"""
        logger.info(f"=== 构建响应 - 状态: {status}, 消息: {message} ===")
        response_body = {"status": status, "message": message}

        # 成功时补充数据字段（同时校验y_record是否存在）
        if status == 1 and all([x_record, y_record, y_data, x_data]):
            response_body["data"] = {
                "points": [{"name": x_record.name, "data": x_data}],
                "series": [{"name": y_record.name, "data": y_data}]
            }
            logger.info(f"响应数据详情 - points名称: {x_record.name}, 数据长度: {len(x_data)}")
            logger.info(f"响应数据详情 - series名称: {y_record.name}, 数据长度: {len(y_data)}")
        else:
            logger.info(f"响应无数据体（状态非成功或数据缺失）")

        return Response(response_body)


class CurveView(APIView):

    def insert_num(self, belong, info, label, config_value):
        try:
            # 尝试查询记录
            defind_result = BranchDefinedValue.objects.using("hwcp").get(
                project_code=info['project_code'],
                project_name=info['project_name'],
                project_gitlab=info['project_gitlab'],
                project_branch=info['project_branch'],
                node_level=info['node_level'],
                var_name=info['nodeName']
            )

            if not defind_result.variables:  # 覆盖None、空字典、空列表
                defind_result.variables = {label: config_value}
                # 如果是字典
            elif isinstance(defind_result.variables, dict):
                logger.info("修改的值是个字典")
                if label in defind_result.variables:
                    del defind_result.variables[label]
                defind_result.variables[label] = config_value
                # 如果是列表
            elif isinstance(defind_result.variables, list):
                logger.info("修改的值是个列表")
                for item in defind_result.variables:
                    if isinstance(item, dict) and label in item:
                        del item[label]
                defind_result.variables.append({label: config_value})
                # 处理异常类型（强制转为字典）
            else:
                logger.warning(f"variables类型异常: {type(defind_result.variables)}，强制转为字典")
                defind_result.variables = {label: config_value}
            defind_result.save()
            logger.info("defind_result.variables:%s", defind_result.variables)

        except BranchDefinedValue.DoesNotExist:
            if belong == "variables":
                BranchDefinedValue.objects.using("hwcp").create(
                    project_code=info['project_code'],
                    project_name=info['project_name'],
                    project_gitlab=info['project_gitlab'],
                    project_branch=info['project_branch'],
                    node_level=info['node_level'],
                    var_name=info['nodeName'],
                    variables={label: config_value}
                )




    def insert_curve(self, data, info):
        """处理数组并正确插入PostgreSQL数组类型字段"""

        def to_json_list(seq):
            # 把所有元素都转成字符串，保证是 "99" 这种格式
            return [str(x) for x in seq]

        # 尝试查询记录，不存在则创建（确保关键信息一致）
        defind_result, created = LuminanceDefindValue.objects.using("hwcp").get_or_create(
            # 查询条件：唯一标识一条记录的字段组合
            project_code=info['project_code'],
            project_name=info['project_name'],
            project_gitlab=info['project_gitlab'],
            project_branch=info['project_branch'],
            node_level=info['node_level'],
            var_name=info['nodeName'],
            # 创建新记录时的初始值（仅在创建新记录时生效）
            defaults={
                'tempre_map': to_json_list(data["points"]),
                'temparray_line': to_json_list(data["series"])
            }
        )
        # 无论记录是查询到的还是新创建的，都更新这两个字段
        defind_result.tempre_map = to_json_list(data["points"])
        defind_result.temparray_line = to_json_list(data["series"])
        defind_result.save(using="hwcp")

        # 可选：打印操作结果
        if created:
            logger.info(f"创建新记录并更新字段：{info['nodeName']}")
        else:
            logger.info(f"更新已有记录：{info['nodeName']}")




    # def send_temperature_grpc(self):



    def updata_curve(self, data, info):
        try:
        # 温度
            if len(data['points']) == len(data['series']):
                logger.info("温度和亮度的个数相等")
                label = "TempRemapLength"
                config_value = len(data['points'])
                logger.info("config_value:%s", config_value)
                # 修改亮度曲线个数
                belong_query = ParamOriginLocation.objects.using("hwcp").filter(
                    param_key=label # 修正为 param_key（根据您的模型定义）
                ).values_list('belong', flat=True).first()
                if belong_query:
                    belong = belong_query

                    logger.info(f"修改的字段属于 {belong} 模块")
                    if belong == "variables":
                        # 使用数据库事务包裹操作
                        with transaction.atomic(using="hwcp"):
                            # 插入TempRemapLength
                            self.insert_num(belong, info, label, config_value)
                            # 插入curve
                            self.insert_curve(data, info)
                            # 调用grpc服务
                            self.send_temperature_grpc()

                    else:
                        raise ValueError("查找出的TempRemapLength不属于variables，请查看数据库数据")

        except Exception as e:
            logger.info("插入TempRemapLength的数据异常：%s", e)
            return False




    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("receive info: %s", params)
            """
                workspace_path: props.workspacePath,
                branch_status: props.branchStatus,
                project_code: props.project_code,
                project_name: props.project_name,
                project_gitlab: props.project_gitlab,
                project_branch: props.project_branch,
                node_level: props.node_level,
                nodeName: props.nodeName
            """
            value = params.get('value')
            workspace_path = params.get('workspace_path')
            branch_status = params.get('branch_status')
            project_code = params.get('project_code')
            project_name = params.get('project_name')
            project_gitlab = params.get('project_gitlab')
            project_branch = params.get('project_branch')
            node_level = params.get("node_level")
            nodeName = params.get("nodeName")

            num_result = self.updata_curve(value, params)
            return Response({"status": 1, "message": "配置提交成功"})




        except Exception as e:
            logger.info("Exception: %s", e)
            return Response({"status": 0, "message": str(e)})
