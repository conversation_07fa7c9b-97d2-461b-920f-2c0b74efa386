import requests
import openpyxl

workbook = openpyxl.load_workbook('NIO企标用例库.xlsx')


def unmerge_sheet_cells(sheet):
    # 获取所有合并单元格的范围列表
    merged_cells = list(sheet.merged_cells.ranges)

    # 遍历每个合并单元格的范围
    for merged_cell in merged_cells:
        # 获取合并单元格的范围坐标
        min_col, min_row, max_col, max_row = merged_cell.bounds
        # 获取合并单元格左上角的值
        value = sheet.cell(row=min_row, column=min_col).value
        # 拆分合并单元格
        sheet.unmerge_cells(str(merged_cell))
        # 将值填充到原合并范围的每个单元格
        for row in range(min_row, max_row + 1):
            for col in range(min_col, max_col + 1):
                sheet.cell(row=row, column=col).value = value


for sheet in workbook.worksheets:
    # if sheet.title.strip() not in ["DTC Test", "Diagnostic DID Test", "NT3 CAN_FD TPLayerTest",
    #                                "CAN NT1_2_CAN_FDTPLayerTest", "LINMasterNodeDiagnosticTestCase",
    #                                "LINSlaveBasicDiagnoticTest"]:
    #     continue

    unmerge_sheet_cells(sheet)

    count = 0
    for row in sheet.iter_rows(values_only=True):
        count += 1

        if count < 3:
            continue

        if not row[2]:
            print(count, row)
            continue

        id_ = row[2]
        id_ = id_.replace("[", "").replace("]", "")
        test_category = row[1]
        name = row[3]
        test_steps = row[4]
        expected_result = row[5]
        priority = row[6]
        software_iteration = row[7]
        config_changes = row[8]
        boot_iteration = row[9]
        hardware_iteration = row[10]
        test_result = row[11]
        test_data = row[12]
        remarks = row[13]

        data = {
            "id": id_,
            "test_category": test_category,
            "name": name,
            "test_steps": test_steps,
            "expected_result": expected_result,
            "priority": priority,
            "software_iteration": software_iteration,
            "config_changes": config_changes,
            "boot_iteration": boot_iteration,
            "hardware_iteration": hardware_iteration,
            "test_result": test_result,
            "test_data": test_data,
            "remarks": remarks,
        }

        r = requests.post("http://127.0.0.1:9091/es_test_cases/nio", json=data)
        r = r.json()
        print(r)

        if r.get("err_code") != 0:
            print(sheet.title, count)

