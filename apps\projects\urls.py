from django.urls import path

from .views import (
    ProjectsView, ProjectDetailView, Projects2View, ProjectDetail2View,
    ProjectsAllView, ProjectExtraInfoByNumberView, ProjectDetailByNumberView,
    ProjectConfigView, TestCaseStatsView, TestCaseActionTypeModuleStatsView,
    ProjectModulesView, ProjectInspectionItemsView, ProjectProductVersionTRView,
    TestCaseActionTypeIssueStatsView,
)

urlpatterns = [
    path("", ProjectsView.as_view()),

    path("/config", ProjectConfigView.as_view()),

    path("/test_case_stats", TestCaseStatsView.as_view()),

    path("/test_case_action_type_module_stats", TestCaseActionTypeModuleStatsView.as_view()),

    path("/test_case_action_type_issue_stats", TestCaseActionTypeIssueStatsView.as_view()),

    path("/detail/by_number", ProjectDetailByNumberView.as_view()),

    path("/<int:pk>", ProjectDetailView.as_view()),

    path("/extra_info/by_number", ProjectExtraInfoByNumberView.as_view()),

    path("/p", Projects2View.as_view()),

    path("/p/all", ProjectsAllView.as_view()),

    path("/p/<int:pk>", ProjectDetail2View.as_view()),

    path("/modules", ProjectModulesView.as_view()),

    path("/inspection_items", ProjectInspectionItemsView.as_view()),

    path("/pv_tr", ProjectProductVersionTRView.as_view()),
]
