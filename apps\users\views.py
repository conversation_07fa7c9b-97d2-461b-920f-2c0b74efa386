import operator
import traceback
import logging
import datetime

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.pagination import PageNumberPagination

from utils.fs_service import FSService
from .serializers import (
    UserLoginSerializer, UserLogin2Serializer, UserListSerializer,
    FSOrganizationSerializer, FSUserSearchSerializer
)
from .models import User, UserFSInfo

logger = logging.getLogger("user")


class UsersView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = UserListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            queryset = User.objects.all()

            page = serializer.validated_data.get("page")
            pagesize = serializer.validated_data.get("pagesize")

            paginator = PageNumberPagination()
            paginator.page = page
            paginator.page_size = pagesize

            page_records = paginator.paginate_queryset(queryset, request)

            content = {
                "count": User.objects.count(),
                "results": [],
                "previous": "",
                "next": ""
            }
            for user in page_records:
                content["results"].append({
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "employee_number": user.employee_number,
                    "phone_number": user.phone_number,
                    "job_title": user.job_title,
                    "job_level": user.job_level
                })
            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            try:
                user = User.objects.get(id=pk)
            except User.DoesNotExist:
                return Response({"err_code": 1, "msg": "该用户不存在。"}, status.HTTP_400_BAD_REQUEST)
            content = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "employee_number": user.employee_number,
                "phone_number": user.phone_number,
                "job_title": user.job_title,
                "job_level": user.job_level
            }
            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class LoginView(APIView):
    def post(self, request):
        try:
            serializer = UserLoginSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)
            code = serializer.validated_data.get("code")
            model = serializer.validated_data.get("model")
            product_code = serializer.validated_data.get("product_code", "")
            auth_code = serializer.validated_data.get("auth_code", "")
            fs = FSService()
            if operator.eq("", product_code) or operator.eq("", auth_code):
                f, data = fs.login1(code=code, model=model)
            else:
                # 非产品测试平台的其他应用
                f, data = fs.login1(code=code, model=model, product_code=product_code, auth_code=auth_code)

                if f:
                    u_data = data.get("data", {})
                    user_info = u_data.get("userInfo", {})
                    username = user_info.get("username")
                    f, data = fs.login2(username=username, password="hwtc@666")

            if not f:
                return Response({"err_code": 3, "msg": data.get("message", "")}, status.HTTP_200_OK)
            else:
                u_data = data.get("data", {})

                access_token = u_data.get("access_token")
                refresh_token = u_data.get("refresh_token")
                token_type = u_data.get("token_type")
                expires_in = u_data.get("expires_in")
                scope = u_data.get("scope")
                jti = u_data.get("jti")

                user_info = u_data.get("userInfo", {})
                username = user_info.get("realName")
                employee_number = user_info.get("employeeNo")
                phone_number = user_info.get("mobile")
                job_title = user_info.get("jobTitle")
                job_level = user_info.get("jobLevel")
                open_id = user_info.get("openId")
                email = user_info.get("username")
                avatar = user_info.get("avatar")

                try:
                    user, _ = User.objects.update_or_create(
                        employee_number=employee_number,
                        defaults={
                            "username": username,
                            "phone_number": phone_number,
                            "job_title": job_title,
                            "job_level": job_level,
                            "open_id": open_id,
                            "email": email,
                            "is_staff": True,
                            "avatar": avatar
                        }
                    )

                    UserFSInfo.objects.update_or_create(
                        employee_number=employee_number,
                        defaults={
                            "access_token": access_token,
                            "refresh_token": refresh_token,
                            "token_type": token_type,
                            "expires_in": expires_in,
                            "scope": scope,
                            "jti": jti,
                            "update_time": datetime.datetime.now(),
                        }
                    )

                except Exception:
                    logger.error("用户创建或更新失败。%s", traceback.format_exc())
                    return Response({"err_code": 2, "err_msg": "用户创建或更新失败."},
                                    status.HTTP_500_INTERNAL_SERVER_ERROR)

                refresh = RefreshToken.for_user(user)
                content = {
                    "access_token": str(refresh.access_token),
                    "refresh_token": str(refresh),
                    "user_info": {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "employee_number": user.employee_number,
                        "phone_number": user.phone_number,
                        "job_title": user.job_title,
                        "is_admin": user.is_superuser,
                        "avatar": user.avatar,
                        "open_id": user.open_id,
                    }
                }
                return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class Login2View(APIView):
    def post(self, request):
        try:
            serializer = UserLogin2Serializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)
            username = serializer.validated_data.get("username")
            password = serializer.validated_data.get("password")
            fs = FSService()
            f, data = fs.login2(username=username, password=password)
            if not f:
                return Response({"err_code": 3, "msg": data.get("message", "")}, status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                u_data = data.get("data", {})

                access_token = u_data.get("access_token")
                refresh_token = u_data.get("refresh_token")
                token_type = u_data.get("token_type")
                expires_in = u_data.get("expires_in")
                scope = u_data.get("scope")
                jti = u_data.get("jti")

                user_info = u_data.get("userInfo", {})
                username = user_info.get("realName")
                employee_number = user_info.get("employeeNo")
                phone_number = user_info.get("mobile")
                job_title = user_info.get("jobTitle")
                job_level = user_info.get("jobLevel")
                open_id = user_info.get("openId")
                email = user_info.get("username")
                avatar = user_info.get("avatar")

                try:
                    user, _ = User.objects.update_or_create(
                        employee_number=employee_number,
                        defaults={
                            "username": username,
                            "phone_number": phone_number,
                            "job_title": job_title,
                            "job_level": job_level,
                            "open_id": open_id,
                            "email": email,
                            "is_staff": True,
                            "avatar": avatar
                        }
                    )

                    UserFSInfo.objects.update_or_create(
                        employee_number=employee_number,
                        defaults={
                            "access_token": access_token,
                            "refresh_token": refresh_token,
                            "token_type": token_type,
                            "expires_in": expires_in,
                            "scope": scope,
                            "jti": jti,
                            "update_time": datetime.datetime.now(),
                        }
                    )

                except Exception:
                    logger.error("用户创建或更新失败。%s", traceback.format_exc())
                    return Response({"err_code": 2, "err_msg": "用户创建或更新失败."},
                                    status.HTTP_500_INTERNAL_SERVER_ERROR)

                refresh = RefreshToken.for_user(user)
                content = {
                    "access_token": str(refresh.access_token),
                    "refresh_token": str(refresh),
                    "user_info": {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "employee_number": user.employee_number,
                        "phone_number": user.phone_number,
                        "job_title": user.job_title,
                        "is_admin": user.is_superuser,
                        "avatar": user.avatar,
                        "open_id": user.open_id,
                    }
                }
                return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class FSOrganizationView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = FSOrganizationSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)
            department_id = serializer.validated_data.get("department_id")

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)

            fs = FSService()
            f, data = fs.get_organization(
                token=user_fs_info.access_token,
                department_id=department_id)

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            content = data.get("data", {})

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class FSUserInfoView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)

            fs = FSService()
            f, data = fs.get_user_info(
                token=user_fs_info.access_token,
                open_id=user.open_id)

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            content = data.get("data", {})

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class FSUserSearchView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = FSUserSearchSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)
            name = serializer.validated_data.get("name")

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)

            fs = FSService()
            f, data = fs.search_user(
                token=user_fs_info.access_token,
                name=name)

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            content = data.get("data", {})

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
