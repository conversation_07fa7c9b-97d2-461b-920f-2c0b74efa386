CREATE TABLE public.chip (
    id SERIAL PRIMARY KEY,             -- 自增主键
    pin_id INTEGER,                   -- 引脚号
    pin_name VARCHAR(50),             -- 引脚名称
    chip VARCHAR(50),                 -- 芯片名
    model VARCHAR(20),                -- 当前配置类型
    module VARCHAR(50)[],             -- 功能类型，数组
    alt_value JSONB,                  -- 备用值，json格式
    status BOOLEAN DEFAULT FALSE      -- 配置状态
);

ALTER TABLE public.chip
RENAME COLUMN alt_value TO alt_values;