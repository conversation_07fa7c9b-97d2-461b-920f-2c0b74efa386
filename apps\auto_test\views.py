import traceback
import logging
import datetime
import json

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from utils.fs_service import FSService
from users.models import UserFSInfo

from utils.fs_app import fs_app
from .serializers import (
    SendMsgSerializer, SendTestExceptionMsgSerializer, SendMachineStorageAlarmMsgSerializer,
    SendTestCompletedMsgSerializer, AccessStatSerializer, GetProjectTestersSerializer,
)
from projects.models import ProjectModel

logger = logging.getLogger("auto_test")

fs_service = FSService()


class SendMsgView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = SendMsgSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            receive_email = serializer.validated_data.get("receive_email")
            project_number = serializer.validated_data.get("project_number")
            msg = serializer.validated_data.get("msg")

            if not receive_email:
                if not project_number:
                    return Response({"err_code": 1, "msg": "receive_email字段为空时，project_number不能为空"},
                                    status.HTTP_400_BAD_REQUEST)
                r = ProjectModel().list(number=project_number, pagesize=1)
                if not r.get("results"):
                    return Response({"err_code": 1, "msg": "项目编号对应的项目未配置"},
                                    status.HTTP_400_BAD_REQUEST)
                p = r.get("results")[0]
                related_people = p.get("related_people")
                start_time = p.get("msg_effective_time_start")
                end_time = p.get("msg_effective_time_end")
                if not related_people:
                    return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                    status.HTTP_400_BAD_REQUEST)
                related_people = json.loads(related_people)
                if not related_people:
                    return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                    status.HTTP_400_BAD_REQUEST)
                related_people = [i.get("email") for i in related_people]
                now = datetime.datetime.now().time()
                if start_time and now < start_time:
                    return Response({"err_code": 0, "msg": "不在配置时间段内"}, status.HTTP_200_OK)
                if end_time and now > end_time:
                    return Response({"err_code": 0, "msg": "不在配置时间段内"}, status.HTTP_200_OK)

                for i in related_people:
                    f, data = fs_app.send_msg(i, msg)
                    if not f:
                        return Response({"err_code": 3, "msg": data.get("msg")}, status.HTTP_200_OK)
            else:
                f, data = fs_app.send_msg(receive_email, msg)
                if not f:
                    return Response({"err_code": 3, "msg": data.get("msg")}, status.HTTP_200_OK)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class SendTestExceptionMsgView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = SendTestExceptionMsgSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            project_number = serializer.validated_data.get("project_number")
            plan_name = serializer.validated_data.get("plan_name")
            tester = serializer.validated_data.get("tester")
            test_case_total = serializer.validated_data.get("test_case_total")
            test_case_exec = serializer.validated_data.get("test_case_exec")
            test_case_ng = serializer.validated_data.get("test_case_ng")
            test_record_item_id = serializer.validated_data.get("test_record_item_id")

            r = ProjectModel().list(number=project_number, pagesize=1)
            if not r.get("results"):
                return Response({"err_code": 1, "msg": "项目编号对应的项目未配置"},
                                status.HTTP_400_BAD_REQUEST)
            p = r.get("results")[0]
            related_people = p.get("related_people")
            start_time = p.get("msg_effective_time_start")
            end_time = p.get("msg_effective_time_end")
            if not related_people:
                return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                status.HTTP_400_BAD_REQUEST)
            related_people = json.loads(related_people)
            if not related_people:
                return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                status.HTTP_400_BAD_REQUEST)
            related_people = [i.get("email") for i in related_people]
            now = datetime.datetime.now().time()
            if start_time and now < start_time:
                return Response({"err_code": 0, "msg": "不在配置时间段内"}, status.HTTP_200_OK)
            if end_time and now > end_time:
                return Response({"err_code": 0, "msg": "不在配置时间段内"}, status.HTTP_200_OK)

            err_msg = []
            for i in related_people:
                f, data = fs_app.send_test_exception_msg(
                    receive_email=i, project=f"{p.get('name')}({p.get('number')})",
                    test_plan=plan_name, tester=tester, test_case_total=test_case_total,
                    test_case_exec=test_case_exec, test_case_ng=test_case_ng,
                    test_record_item_id=test_record_item_id
                )
                if not f:
                    err_msg.append(data.get("msg"))

            if err_msg:
                return Response({"err_code": 4, "msg": "发送失败", "err_msg": err_msg},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({"err_code": 0, "msg": "ok", "err_msg": err_msg}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class SendMachineStorageAlarmMsgView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = SendMachineStorageAlarmMsgSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            project_number = serializer.validated_data.get("project_number")
            machine = serializer.validated_data.get("machine")
            tester = serializer.validated_data.get("tester")
            cwd = serializer.validated_data.get("cwd")
            remain_capacity = serializer.validated_data.get("remain_capacity")
            tip = serializer.validated_data.get("tip")

            r = ProjectModel().list(number=project_number, pagesize=1)
            if not r.get("results"):
                return Response({"err_code": 1, "msg": "项目编号对应的项目未配置"},
                                status.HTTP_400_BAD_REQUEST)
            p = r.get("results")[0]
            related_people = p.get("related_people")
            start_time = p.get("msg_effective_time_start")
            end_time = p.get("msg_effective_time_end")
            if not related_people:
                return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                status.HTTP_400_BAD_REQUEST)
            related_people = json.loads(related_people)
            if not related_people:
                return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                status.HTTP_400_BAD_REQUEST)
            related_people = [i.get("email") for i in related_people]
            now = datetime.datetime.now().time()
            if start_time and now < start_time:
                return Response({"err_code": 0, "msg": "不在配置时间段内"}, status.HTTP_200_OK)
            if end_time and now > end_time:
                return Response({"err_code": 0, "msg": "不在配置时间段内"}, status.HTTP_200_OK)

            err_msg = []
            for i in related_people:
                f, data = fs_app.send_machine_storage_alarm_msg(
                    receive_email=i, project=f"{p.get('name')}({p.get('number')})",
                    machine=machine, tester=tester, cwd=cwd,
                    remain_capacity=remain_capacity, tip=tip
                )
                if not f:
                    err_msg.append(data.get("msg"))

            return Response({"err_code": 0, "msg": "ok", "err_msg": err_msg}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class SendTestCompletedMsgView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = SendTestCompletedMsgSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            project_number = serializer.validated_data.get("project_number")
            plan_name = serializer.validated_data.get("plan_name")
            tester = serializer.validated_data.get("tester")
            test_case_total = serializer.validated_data.get("test_case_total")
            test_case_exec = serializer.validated_data.get("test_case_exec")
            test_case_ng = serializer.validated_data.get("test_case_ng")

            r = ProjectModel().list(number=project_number, pagesize=1)
            if not r.get("results"):
                return Response({"err_code": 1, "msg": "项目编号对应的项目未配置"},
                                status.HTTP_400_BAD_REQUEST)
            p = r.get("results")[0]
            related_people = p.get("related_people")
            start_time = p.get("msg_effective_time_start")
            end_time = p.get("msg_effective_time_end")
            if not related_people:
                return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                status.HTTP_400_BAD_REQUEST)
            related_people = json.loads(related_people)
            if not related_people:
                return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                status.HTTP_400_BAD_REQUEST)
            related_people = [i.get("email") for i in related_people]
            now = datetime.datetime.now().time()
            if start_time and now < start_time:
                return Response({"err_code": 0, "msg": "不在配置时间段内"}, status.HTTP_200_OK)
            if end_time and now > end_time:
                return Response({"err_code": 0, "msg": "不在配置时间段内"}, status.HTTP_200_OK)

            err_msg = []
            for i in related_people:
                f, data = fs_app.send_test_completed_msg(
                    receive_email=i, project=f"{p.get('name')}({p.get('number')})",
                    test_plan=plan_name, tester=tester, test_case_total=test_case_total,
                    test_case_exec=test_case_exec, test_case_ng=test_case_ng
                )
                if not f:
                    err_msg.append(data.get("msg"))

            return Response({"err_code": 0, "msg": "ok", "err_msg": err_msg}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class AccessStatView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = AccessStatSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            page_url = serializer.validated_data.get("page_url")
            page_title = serializer.validated_data.get("page_title")
            user_id = serializer.validated_data.get("user_id")

            f, data = fs_service.access_stat(page_url, page_title, user_id)

            if not f:
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetProjectTestersView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = GetProjectTestersSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            project_number = serializer.validated_data.get("project_number")

            r = ProjectModel().list(number=project_number, pagesize=1)
            if not r.get("results"):
                return Response({"err_code": 1, "msg": "项目编号对应的项目未配置"},
                                status.HTTP_400_BAD_REQUEST)
            p = r.get("results")[0]
            related_people = p.get("related_people")
            if not related_people:
                return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                status.HTTP_400_BAD_REQUEST)
            related_people = json.loads(related_people)
            if not related_people:
                return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                status.HTTP_400_BAD_REQUEST)

            return Response({"err_code": 0, "msg": "ok", "data": related_people}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
