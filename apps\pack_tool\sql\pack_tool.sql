CREATE TABLE IF NOT EXISTS public.pack_tool_usage_records(
    id serial primary key, --id
    project_name varchar(255),
    project_number varchar(255),

    component_code varchar(255),
    software_version varchar(255),
    screen_touchpad_version varchar(255),
    pmic_flag int,
    encrypt int,
    encrypt_file varchar(255),
    output_type varchar(255),
    srecord_type varchar(255),

    input_files jsonb,
    packer_version varchar(255),
    output_file_name varchar(255),
    output_file_md5 varchar(255),
    output_file_url varchar(255),
    operator_name VARCHAR(255),
    operator_email VARCHAR(255),
    create_time timestamp --创建时间
    package_status int4 DEFAULT 0,
	pack_input_file_type varchar(255),
	pack_old_package_name varchar(255),
	pack_old_package_version varchar(255),
	pack_old_package_md5 varchar(255),
	pack_new_package_name varchar(255),
	pack_new_package_version varchar,
	pack_new_package_md5 varchar,
);
ALTER TABLE public.pack_tool_usage_records OWNER TO atpms;


CREATE TABLE IF NOT EXISTS public.package_share_urls(
    id serial primary key, --id
    pack_record_id int,
    token varchar(255),
    expiration_time int,
    creator_name VARCHAR(255),
    creator_email VARCHAR(255),
    create_time timestamp --创建时间
);
ALTER TABLE public.package_share_urls OWNER TO atpms;