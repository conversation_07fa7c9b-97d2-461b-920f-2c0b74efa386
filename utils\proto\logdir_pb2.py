# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: logdir.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'logdir.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0clogdir.proto\x12\x06logdir\"M\n\x13\x43reateBranchRequest\x12\x14\n\x0cproject_path\x18\x01 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x02 \x01(\t\x12\x0b\n\x03ref\x18\x03 \x01(\t\"q\n\x14\x43reateBranchResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\x05\x12\x13\n\x0b\x62ranch_name\x18\x04 \x01(\t\x12\x14\n\x0cproject_path\x18\x05 \x01(\t\"r\n\x17\x43reateRepositoryRequest\x12\x19\n\x11\x66ull_project_name\x18\x01 \x01(\t\x12\x12\n\ngroup_path\x18\x02 \x01(\t\x12\x18\n\x0b\x64\x65scription\x18\x03 \x01(\tH\x00\x88\x01\x01\x42\x0e\n\x0c_description\"\x90\x01\n\x18\x43reateRepositoryResponse\x12\x12\n\nproject_id\x18\x01 \x01(\x05\x12\x14\n\x0cproject_path\x18\x02 \x01(\t\x12\x13\n\x0bproject_url\x18\x03 \x01(\t\x12\x16\n\x0e\x64\x65\x66\x61ult_branch\x18\x04 \x01(\t\x12\x0f\n\x07message\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\x05\"H\n\x17GetSubgroupPathsRequest\x12\x12\n\ngroup_path\x18\x01 \x01(\t\x12\x19\n\x11\x66ull_project_name\x18\x02 \x01(\t\"\x9c\x01\n\x18GetSubgroupPathsResponse\x12\x19\n\x11parent_group_path\x18\x01 \x01(\t\x12\x19\n\x11parent_group_name\x18\x02 \x01(\t\x12\x16\n\x0esubgroup_paths\x18\x03 \x03(\t\x12\x13\n\x0btotal_count\x18\x04 \x01(\x05\x12\x0f\n\x07message\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\x05\"<\n\x14QueryMRStatusRequest\x12\x14\n\x0cproject_path\x18\x01 \x01(\t\x12\x0e\n\x06mr_iid\x18\x02 \x01(\x05\"k\n\x15QueryMRStatusResponse\x12\x0e\n\x06mr_iid\x18\x01 \x01(\x05\x12\x14\n\x0cmerge_status\x18\x02 \x01(\t\x12\r\n\x05state\x18\x03 \x01(\t\x12\x0f\n\x07message\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\x05\"6\n\x0eMergeMRRequest\x12\x14\n\x0cproject_path\x18\x01 \x01(\t\x12\x0e\n\x06mr_iid\x18\x02 \x01(\x05\"b\n\x0fMergeMRResponse\x12\x0e\n\x06mr_iid\x18\x01 \x01(\x05\x12\r\n\x05state\x18\x02 \x01(\t\x12\x0f\n\x07message\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\x05\x12\x11\n\tcommit_id\x18\x05 \x01(\t\"d\n\x10GitCommitRequest\x12\x14\n\x0cproject_path\x18\x01 \x01(\t\x12\x0e\n\x06\x62ranch\x18\x02 \x01(\t\x12\x16\n\x0e\x63ommit_message\x18\x03 \x01(\t\x12\x12\n\nfile_paths\x18\x04 \x03(\t\"V\n\x11GitCommitResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x11\n\tcommit_id\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\x05\"6\n\x0eGitPushRequest\x12\x14\n\x0cproject_path\x18\x01 \x01(\t\x12\x0e\n\x06\x62ranch\x18\x02 \x01(\t\"T\n\x0fGitPushResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x11\n\tcommit_id\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\x05\"h\n\x1e\x43reateOrGetMergeRequestRequest\x12\x18\n\x10\x65ngineering_path\x18\x01 \x01(\t\x12\x15\n\rsource_branch\x18\x02 \x01(\t\x12\x15\n\rtarget_branch\x18\x03 \x01(\t\"\x98\x01\n\x1f\x43reateOrGetMergeRequestResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x0e\n\x06mr_iid\x18\x02 \x01(\x05\x12\x14\n\x0cproject_path\x18\x03 \x01(\t\x12\x14\n\x0cmerge_status\x18\x04 \x01(\t\x12\x18\n\x10grpc_status_code\x18\x05 \x01(\x05\x12\x0e\n\x06mr_url\x18\x06 \x01(\t\"\x07\n\x05\x45mpty\"*\n\x12\x43odeAnalyzeRequest\x12\x14\n\x0cproject_name\x18\x01 \x01(\t\"Q\n\rParameterInfo\x12\x0f\n\x07io_type\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\"\xc0\x02\n\x0c\x46unctionInfo\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x15\n\rfunction_name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x13\n\x0b\x63onstraints\x18\x04 \x01(\t\x12\x15\n\rexample_usage\x18\x05 \x01(\t\x12\x1b\n\x13validation_criteria\x18\x06 \x01(\t\x12)\n\nparameters\x18\x07 \x03(\x0b\x32\x15.logdir.ParameterInfo\x12,\n\rreturn_values\x18\x08 \x03(\x0b\x32\x15.logdir.ParameterInfo\x12*\n\x0bglobal_vars\x18\t \x03(\x0b\x32\x15.logdir.ParameterInfo\x12*\n\x0bstatic_vars\x18\n \x03(\x0b\x32\x15.logdir.ParameterInfo\"w\n\x12\x43odeAnalysisResult\x12\x11\n\tfile_name\x18\x01 \x01(\t\x12\x11\n\tfile_path\x18\x02 \x01(\t\x12\x12\n\nheadr_file\x18\x03 \x01(\t\x12\'\n\tfunctions\x18\x04 \x03(\x0b\x32\x14.logdir.FunctionInfo\"m\n\x13\x43odeAnalyzeResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x34\n\x10\x61nalysis_results\x18\x03 \x03(\x0b\x32\x1a.logdir.CodeAnalysisResult\"*\n\x12ProjectScanRequest\x12\x14\n\x0cproject_name\x18\x01 \x01(\t\"Y\n\x13ProjectScanResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x03 \x01(\x05\x12\x12\n\nfile_count\x18\x04 \x01(\x05\"\x1f\n\x07Project\x12\x14\n\x0cproject_name\x18\x01 \x01(\t\"6\n\x12ProjectSyncRequest\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x0f\n\x07success\x18\x02 \x01(\x08\"\x88\x01\n\x0bProjectInfo\x12\x12\n\nproject_id\x18\x01 \x01(\x05\x12\x14\n\x0cproject_name\x18\x02 \x01(\t\x12\x13\n\x0bproject_url\x18\x03 \x01(\t\x12\x1b\n\x13project_description\x18\x04 \x01(\t\x12\x1d\n\x15project_last_activity\x18\x05 \x01(\t\"b\n\x17ListAllProjectsResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12%\n\x08projects\x18\x02 \x03(\x0b\x32\x13.logdir.ProjectInfo\x12\x0f\n\x07success\x18\x03 \x01(\x08\"1\n\x19GetProjectBranchesRequest\x12\x14\n\x0cproject_name\x18\x01 \x01(\t\"@\n\nBranchInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tcommit_id\x18\x02 \x01(\t\x12\x11\n\tprotected\x18\x03 \x01(\x08\"P\n\x1aGetProjectBranchesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x10\n\x08\x62ranches\x18\x03 \x03(\t\"]\n\x16\x43loneRepositoryRequest\x12\x10\n\x08repo_url\x18\x01 \x01(\t\x12\x14\n\x0cproject_path\x18\x02 \x01(\t\x12\x0e\n\x06\x62ranch\x18\x03 \x01(\t\x12\x0b\n\x03tag\x18\x04 \x01(\t\"O\n\x17\x43loneRepositoryResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\nlocal_path\x18\x03 \x01(\t\"\x1f\n\x0eLogDirResponse\x12\r\n\x05\x64\x61tes\x18\x01 \x03(\t\"\x1e\n\rLogDirRequest\x12\r\n\x05\x64\x61tes\x18\x01 \x03(\t\"_\n\x0fOperationStatus\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rdeleted_count\x18\x02 \x01(\x05\x12\x14\n\x0c\x64\x65leted_dirs\x18\x03 \x03(\t\x12\x0e\n\x06\x65rrors\x18\x04 \x03(\t\"J\n\x15GetProjectTagsRequest\x12\x14\n\x0cproject_path\x18\x01 \x01(\t\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0c\n\x04sort\x18\x03 \x01(\x08\"\x90\x01\n\x07TagInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x11\n\tcommit_id\x18\x03 \x01(\t\x12\x16\n\x0e\x63ommit_message\x18\x04 \x01(\t\x12\x12\n\ncreated_at\x18\x05 \x01(\t\x12\x14\n\x0c\x63reator_name\x18\x06 \x01(\t\x12\x11\n\tprotected\x18\x07 \x01(\x08\"|\n\x16GetProjectTagsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\x05\x12\x1d\n\x04tags\x18\x04 \x03(\x0b\x32\x0f.logdir.TagInfo\x12\x13\n\x0btotal_count\x18\x05 \x01(\x05\"7\n\x14\x43reateWebhookRequest\x12\x0f\n\x07git_url\x18\x01 \x01(\t\x12\x0e\n\x06\x62ranch\x18\x02 \x01(\t\"p\n\x15\x43reateWebhookResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\x05\x12\x12\n\nwebhook_id\x18\x04 \x01(\x05\x12\x13\n\x0bwebhook_url\x18\x05 \x01(\t2\x8e\x01\n\rLogDirService\x12<\n\x13GenerateDatesToKeep\x12\r.logdir.Empty\x1a\x16.logdir.LogDirResponse\x12?\n\rDeleteOldDirs\x12\x15.logdir.LogDirRequest\x1a\x17.logdir.OperationStatus2\xcc\x08\n\rGitlabService\x12\x41\n\x0fListAllProjects\x12\r.logdir.Empty\x1a\x1f.logdir.ListAllProjectsResponse\x12;\n\x0cSyncProjects\x12\x0f.logdir.Project\x1a\x1a.logdir.ProjectSyncRequest\x12j\n\x17\x43reateOrGetMergeRequest\x12&.logdir.CreateOrGetMergeRequestRequest\x1a\'.logdir.CreateOrGetMergeRequestResponse\x12L\n\rQueryMRStatus\x12\x1c.logdir.QueryMRStatusRequest\x1a\x1d.logdir.QueryMRStatusResponse\x12:\n\x07MergeMR\x12\x16.logdir.MergeMRRequest\x1a\x17.logdir.MergeMRResponse\x12@\n\tGitCommit\x12\x18.logdir.GitCommitRequest\x1a\x19.logdir.GitCommitResponse\x12:\n\x07GitPush\x12\x16.logdir.GitPushRequest\x1a\x17.logdir.GitPushResponse\x12[\n\x12GetProjectBranches\x12!.logdir.GetProjectBranchesRequest\x1a\".logdir.GetProjectBranchesResponse\x12R\n\x0f\x43loneRepository\x12\x1e.logdir.CloneRepositoryRequest\x1a\x1f.logdir.CloneRepositoryResponse\x12U\n\x10GetSubgroupPaths\x12\x1f.logdir.GetSubgroupPathsRequest\x1a .logdir.GetSubgroupPathsResponse\x12U\n\x10\x43reateRepository\x12\x1f.logdir.CreateRepositoryRequest\x1a .logdir.CreateRepositoryResponse\x12I\n\x0c\x43reateBranch\x12\x1b.logdir.CreateBranchRequest\x1a\x1c.logdir.CreateBranchResponse\x12O\n\x0eGetProjectTags\x12\x1d.logdir.GetProjectTagsRequest\x1a\x1e.logdir.GetProjectTagsResponse\x12L\n\rCreateWebhook\x12\x1c.logdir.CreateWebhookRequest\x1a\x1d.logdir.CreateWebhookResponse2\xa7\x01\n\x15ProjectScannerService\x12\x46\n\x0bScanProject\x12\x1a.logdir.ProjectScanRequest\x1a\x1b.logdir.ProjectScanResponse\x12\x46\n\x0b\x41nalyzeCode\x12\x1a.logdir.CodeAnalyzeRequest\x1a\x1b.logdir.CodeAnalyzeResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'logdir_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_CREATEBRANCHREQUEST']._serialized_start=24
  _globals['_CREATEBRANCHREQUEST']._serialized_end=101
  _globals['_CREATEBRANCHRESPONSE']._serialized_start=103
  _globals['_CREATEBRANCHRESPONSE']._serialized_end=216
  _globals['_CREATEREPOSITORYREQUEST']._serialized_start=218
  _globals['_CREATEREPOSITORYREQUEST']._serialized_end=332
  _globals['_CREATEREPOSITORYRESPONSE']._serialized_start=335
  _globals['_CREATEREPOSITORYRESPONSE']._serialized_end=479
  _globals['_GETSUBGROUPPATHSREQUEST']._serialized_start=481
  _globals['_GETSUBGROUPPATHSREQUEST']._serialized_end=553
  _globals['_GETSUBGROUPPATHSRESPONSE']._serialized_start=556
  _globals['_GETSUBGROUPPATHSRESPONSE']._serialized_end=712
  _globals['_QUERYMRSTATUSREQUEST']._serialized_start=714
  _globals['_QUERYMRSTATUSREQUEST']._serialized_end=774
  _globals['_QUERYMRSTATUSRESPONSE']._serialized_start=776
  _globals['_QUERYMRSTATUSRESPONSE']._serialized_end=883
  _globals['_MERGEMRREQUEST']._serialized_start=885
  _globals['_MERGEMRREQUEST']._serialized_end=939
  _globals['_MERGEMRRESPONSE']._serialized_start=941
  _globals['_MERGEMRRESPONSE']._serialized_end=1039
  _globals['_GITCOMMITREQUEST']._serialized_start=1041
  _globals['_GITCOMMITREQUEST']._serialized_end=1141
  _globals['_GITCOMMITRESPONSE']._serialized_start=1143
  _globals['_GITCOMMITRESPONSE']._serialized_end=1229
  _globals['_GITPUSHREQUEST']._serialized_start=1231
  _globals['_GITPUSHREQUEST']._serialized_end=1285
  _globals['_GITPUSHRESPONSE']._serialized_start=1287
  _globals['_GITPUSHRESPONSE']._serialized_end=1371
  _globals['_CREATEORGETMERGEREQUESTREQUEST']._serialized_start=1373
  _globals['_CREATEORGETMERGEREQUESTREQUEST']._serialized_end=1477
  _globals['_CREATEORGETMERGEREQUESTRESPONSE']._serialized_start=1480
  _globals['_CREATEORGETMERGEREQUESTRESPONSE']._serialized_end=1632
  _globals['_EMPTY']._serialized_start=1634
  _globals['_EMPTY']._serialized_end=1641
  _globals['_CODEANALYZEREQUEST']._serialized_start=1643
  _globals['_CODEANALYZEREQUEST']._serialized_end=1685
  _globals['_PARAMETERINFO']._serialized_start=1687
  _globals['_PARAMETERINFO']._serialized_end=1768
  _globals['_FUNCTIONINFO']._serialized_start=1771
  _globals['_FUNCTIONINFO']._serialized_end=2091
  _globals['_CODEANALYSISRESULT']._serialized_start=2093
  _globals['_CODEANALYSISRESULT']._serialized_end=2212
  _globals['_CODEANALYZERESPONSE']._serialized_start=2214
  _globals['_CODEANALYZERESPONSE']._serialized_end=2323
  _globals['_PROJECTSCANREQUEST']._serialized_start=2325
  _globals['_PROJECTSCANREQUEST']._serialized_end=2367
  _globals['_PROJECTSCANRESPONSE']._serialized_start=2369
  _globals['_PROJECTSCANRESPONSE']._serialized_end=2458
  _globals['_PROJECT']._serialized_start=2460
  _globals['_PROJECT']._serialized_end=2491
  _globals['_PROJECTSYNCREQUEST']._serialized_start=2493
  _globals['_PROJECTSYNCREQUEST']._serialized_end=2547
  _globals['_PROJECTINFO']._serialized_start=2550
  _globals['_PROJECTINFO']._serialized_end=2686
  _globals['_LISTALLPROJECTSRESPONSE']._serialized_start=2688
  _globals['_LISTALLPROJECTSRESPONSE']._serialized_end=2786
  _globals['_GETPROJECTBRANCHESREQUEST']._serialized_start=2788
  _globals['_GETPROJECTBRANCHESREQUEST']._serialized_end=2837
  _globals['_BRANCHINFO']._serialized_start=2839
  _globals['_BRANCHINFO']._serialized_end=2903
  _globals['_GETPROJECTBRANCHESRESPONSE']._serialized_start=2905
  _globals['_GETPROJECTBRANCHESRESPONSE']._serialized_end=2985
  _globals['_CLONEREPOSITORYREQUEST']._serialized_start=2987
  _globals['_CLONEREPOSITORYREQUEST']._serialized_end=3080
  _globals['_CLONEREPOSITORYRESPONSE']._serialized_start=3082
  _globals['_CLONEREPOSITORYRESPONSE']._serialized_end=3161
  _globals['_LOGDIRRESPONSE']._serialized_start=3163
  _globals['_LOGDIRRESPONSE']._serialized_end=3194
  _globals['_LOGDIRREQUEST']._serialized_start=3196
  _globals['_LOGDIRREQUEST']._serialized_end=3226
  _globals['_OPERATIONSTATUS']._serialized_start=3228
  _globals['_OPERATIONSTATUS']._serialized_end=3323
  _globals['_GETPROJECTTAGSREQUEST']._serialized_start=3325
  _globals['_GETPROJECTTAGSREQUEST']._serialized_end=3399
  _globals['_TAGINFO']._serialized_start=3402
  _globals['_TAGINFO']._serialized_end=3546
  _globals['_GETPROJECTTAGSRESPONSE']._serialized_start=3548
  _globals['_GETPROJECTTAGSRESPONSE']._serialized_end=3672
  _globals['_CREATEWEBHOOKREQUEST']._serialized_start=3674
  _globals['_CREATEWEBHOOKREQUEST']._serialized_end=3729
  _globals['_CREATEWEBHOOKRESPONSE']._serialized_start=3731
  _globals['_CREATEWEBHOOKRESPONSE']._serialized_end=3843
  _globals['_LOGDIRSERVICE']._serialized_start=3846
  _globals['_LOGDIRSERVICE']._serialized_end=3988
  _globals['_GITLABSERVICE']._serialized_start=3991
  _globals['_GITLABSERVICE']._serialized_end=5091
  _globals['_PROJECTSCANNERSERVICE']._serialized_start=5094
  _globals['_PROJECTSCANNERSERVICE']._serialized_end=5261
# @@protoc_insertion_point(module_scope)
