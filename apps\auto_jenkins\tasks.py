from celery import shared_task
from .jenkins_service import JenkinsService
from .webhook_utils import create_webhook
import logging
from utils.fs_app import FSApp
from .generatior import JenkinsfileGenerator
from .webhook_utils import generate_build_id
from django.utils import timezone
from .models import PipelineBuild,PipelineProject
logger = logging.getLogger("celery")

fs = FSApp()

@shared_task(bind=True,max_retries=3)
def provision_jenkins_project(self, project_name, jenkinsfile_content, git_url, pipeline_config, branch,
                               engineering_path, engineering_group, enable_webhook): 
    """
    负责更新或者创建Jenkins job  和添加GitLab 的webhook事件
    使用消息队列进行异步 自带三次重试机制
    """   
    try:
        task_id = self.request.id
        logger.info(f"Task started - Task ID: {task_id}, Project: {project_name}")
        jenkins_service = JenkinsService()
        jenkins_success = jenkins_service.create_or_update_pipeline_job(
                project_name, 
                jenkinsfile_content,
                git_url,
                pipeline_config,
                branch,
                engineering_path,
                engineering_group
            )
        if not jenkins_success:
            raise Exception(f"Failed to create Jenkins job for project: {project_name}")
        if enable_webhook and git_url:
            logger.info(f"为项目 '{project_name}' 创建 GitLab Webhook...")
            webhook_success = create_webhook(git_url)
            if webhook_success:
                logger.info(f"project '{project_name}' webhook created successfully.")
            else:
                logger.warning(f"Failed to create webhook for project: {project_name}, but the process will continue.")
        else:
            logger.info(f"Webhook is not enabled for project: {project_name}, skipping creation.")
        logger.info(f"Task completed successfully - Task ID: {task_id}, Project: {project_name}")
        return   {                                           # 返回英文信息 能够直接被redis的结果记录
            "status": "SUCCESS",  
            "project_name": project_name,
            "jenkins_job_created": True,
            "webhook_created": enable_webhook and webhook_success, 
            "message": f"Project '{project_name}' provisioned successfully"
        }
    except Exception as exc:
        logger.error(f"Task failed - Task ID: {task_id}, Project: {project_name}, Error: {str(exc)}")
        if self.request.retries < self.max_retries:
            logger.info(f"Task retrying - Task ID: {task_id}, Attempt: {self.request.retries + 1}")
            raise self.retry(exc=exc, countdown=30)
        else:
            logger.error(f"Task failed, no more retries - Task ID: {task_id}, Project: {project_name}")
            raise


@shared_task(bind=True,max_retries=3)
def send_feishu_notification_task(self, creator_email, project_name, branch, message, creator):
    """
    异步发送飞书通知,任务开始
    """
    try:
        task_id = self.request.id
        logger.info(f"Task started - Task ID: {task_id}, Project: {project_name}")
        result,response = fs.send_jenkins_task_start_msg(creator_email, project_name, branch, message, creator)
        if not result:
            logger.error(f"Task failed - Task ID: {task_id}, Project: {project_name}, Error: {response}")
            raise Exception(f"Failed to send feishu notification - Task ID: {task_id}, Project: {project_name}, Error: {response}")
        logger.info(f"send_jenkins_task_start_msg success - Task ID: {task_id}, Project: {project_name}")
        return {
            "status": "SUCCESS",
            "task_id": task_id,
            "project_name": project_name,
            "message": "Feishu notification sent successfully"
        }
    except Exception as e:
        logger.error(f"Task failed - Task ID: {task_id}, Project: {project_name}, Error: {str(e)}")
        if self.request.retries < self.max_retries:
            logger.info(f"Task retrying - Task ID: {task_id}, Attempt: {self.request.retries + 1}")
            raise self.retry(exc=e, countdown=30)
        else:
            logger.error(f"Task failed, no more retries - Task ID: {task_id}, Project: {project_name}")
            raise          

@shared_task(bind=True,max_retries=3)
def send_feishu_notification_task_result(self, creator_email, project_name, branch, message, creator,total_time,upload_url):
    """
    异步发送飞书通知,任务结果
    """
    try:
        task_id = self.request.id
        logger.info(f"Task started - Task ID: {task_id}, Project: {project_name}")
        result,response = fs.send_jenkins_task_result_msg(creator_email, project_name, branch, message, creator,total_time,upload_url)
        if not result:
            logger.error(f"Task failed - Task ID: {task_id}, Project: {project_name}, Error: {response}")   
            raise Exception(f"Failed to send feishu notification - Task ID: {task_id}, Project: {project_name}, Error: {response}")
        logger.info(f"send_jenkins_task_result_msg success - Task ID: {task_id}, Project: {project_name}")
        return {
            "status": "SUCCESS",
            "task_id": task_id,
            "project_name": project_name,
            "message": "Feishu notification sent successfully"
        }
    except Exception as e:
        logger.error(f"Task failed - Task ID: {task_id}, Project: {project_name}, Error: {str(e)}")
        if self.request.retries < self.max_retries:
            logger.info(f"Task retrying - Task ID: {task_id}, Attempt: {self.request.retries + 1}")
            raise self.retry(exc=e, countdown=30)
        else:
            logger.error(f"Task failed, no more retries - Task ID: {task_id}, Project: {project_name}")
            raise           

@shared_task(bind=True,max_retries=3)
def process_webhook_build_task(self, project_id,branch):
    """
    GitLab的 webhook  异步触发Jenkins构建
    """
    try:
        # 从数据库获取项目对象
        project = PipelineProject.objects.get(id=project_id)
        logger.info(f"Task started - Task ID: {self.request.id}, Project: {project.project_name}")

        jenkins_service = JenkinsService()
        build_id = generate_build_id(project.project_name)
        pipeline_config = project.pipeline_config.copy()
        pipeline_config.update({
            'task_id': build_id,
            'django_base_url': 'http://**********:9000'
        })
        generator = JenkinsfileGenerator()
        jf_res = generator.generate(pipeline_config)
        if not jf_res['success']:
                raise Exception(f"generate jenkinsfile failed: {jf_res['errors']}")
        jenkins_success = jenkins_service.create_or_update_pipeline_job(
                project.jenkins_job_name,
                jf_res['jenkinsfile'],
                project.git_url,
                pipeline_config,
                project.branch,
                project.engineering_path,
                project.engineering_group
            )
        if not jenkins_success:
                raise Exception("更新Jenkins Job配置失败")
        job_full_name = project.jenkins_job_name if not project.engineering_group else f"{project.engineering_group}/{project.jenkins_job_name}"
        j_task_id, queue_id = jenkins_service.start_jenkins_job(job_full_name, {'task_id': build_id})
        try:
            build_record = PipelineBuild.objects.create(
            project=project,
            task_id=build_id,  #build id 唯一值 
            status='running',  # 已经在Jenkins中运行
            start_time=timezone.now(),
            jenkins_url='',  # 初始为空，回调时更新
            upload_url='',   # 初始为空
            created_by= '自动触发',
            creator_email = project.creator_email  # 默认发创建这个项目email
            )                
            logger.info(f"构建记录已创建: {build_record.task_id}")
            fs.send_jenkins_task_start_msg(project.creator_email, project.project_name, project.branch, '开始执行任务',"自动触发") 
        except Exception as e:
                    logger.error(f"创建构建记录失败: {str(e)}")
        return {
            "status": "SUCCESS",
            "task_id": self.request.id,
            "queue_id": queue_id,
            "jenkins_job_name": job_full_name
        }
    except Exception as e:
        logger.error(f"WebHook异步构建失败: {e}")
        raise self.retry(exc=e, countdown=30)
    
