import json
import logging
import os
import traceback
import urllib.parse
from io import BytesIO

import pandas as pd
from django.conf import settings
from django.http import HttpResponse
from openpyxl import load_workbook
from openpyxl.styles import Font, Alignment
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
import requests

from functions.models import FunctionModel
from .models import TestReportModel, plan_stats
from .serializers import TestReportsListSerializer
from utils.fs_service import FSService
from users.models import UserFSInfo
from utils.fs_app import fs_app

fs_service = FSService()

logger = logging.getLogger("machine")

source_map = {
    "TASK_CHANGE": "用例库沿用",
    "TASK_AFFIRM": "需求分析",
    "NEW_PROJECT": "需求变更",
    "HORIZONTAL_SCALING": "横向扩展"
}

type_map = {
    "DURABLE_TEST": "耐久测试",
    "PERFORMANCE_TEST": "性能测试",
    "FUNCTION_TEST": "功能测试",
    "PROTOCOL_STACK_TEST": "协议栈测试"
}

generation_method_map = {
    "BOUNDARY_VALUE_METHOD": "边界值法",
    "FRUIT_GRAPH_METHOD": "因果法",
    "DECISION_TABLE_DRIVE": "判定表驱法",
    "FUNCTION_DIAGRAM_METHOD": "功能图法",
    "SCENE_METHOD": "场景法",
    "EQUIVALENCE_CLASS": "等价类",
    "FIELD_EXPERIENCE_ANALYSIS": "现场经验分析",
    "EXTERNAL_AND_INTERNAL_INTERFACE_ANALYSIS": "外部和内部接口分析法",
    "PROCESS_ANALYSIS": "流程分析法",
    "BACKWARD_ANALYSIS": "反向分析",
    "FORWARD_ANALYSIS": "正向分析",
    "ENVIRONMENTAL_CONDITIONS_AND_OPERATIONAL_USE_CASE_ANALYSIS": "环境条件和操作用例分析",
    "MISGUESS": "错误猜错法",
    "SEQUENCE_AND_SOURCE_ANALYSIS": "序列和来源的分析",
    "COMMON_LIMIT_CONDITIONS_4_DEPENDENCE": "相依性的常见极限条件",
    "ANALYSIS_OPERATING_CONDITIONS_USE_CASES": "用例的运行条件分析",
    "DEMAND_ANALYSIS": "基于需求分析",
}

function_safe_attrib_map = {
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AA": "ASIL A",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AD": "ASIL D",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQD": "ASIL QM(D)",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQC": "ASIL QM(C)",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_NA": "N/A",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQB": "ASIL QM(B)",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQA": "ASIL QM(A)",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AB": "ASIL B",
}

test_method_map = {
    "PRESSURE_TEST": "压力测试",
    "RESOURCE_USAGE_TESTING": "资源使用情况测试",
    "INTERACTION_COMMUNICATION_TESTING": "互动/沟通测试",
    "INTERFACE_CONSISTENCY_CHECK": "接口一致性检查",
    "TESTS_BASED_ON_FIELD_EXPERIENCE": "根据现场经验进行的测试",
    "FALSE_GUESS_TEST": "错误猜测测试",
    "PERFORMANCE_TEST": "性能测试",
    "FAULT_INJECTION_TEST": "故障注入测试",
    "BACK_TO_BACK_TESTING": "背靠背测试",
    "INTERFACE_TEST": "基于接口测试",
    "DEMAND_TEST": "基于需求测试",
}

execute_mode_map = {
    "AUTOMATED_EXECUTION": "自动化测试",
    "MANUAL_EXECUTION": "手动测试",
    "SEMI_AUTOMATED_EXECUTION": "半自动化测试",
}

priority_map = {
    "HIGH": "高",
    "MIDDLE": "中",
    "LOW": "低",
}


class TestReportsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestReportsListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestReportModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)

        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestReportDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = TestReportModel()
            content = model.retrieve(pk)

            if content:
                plan_stats(content)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)

        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


def parse_requirements(item):
    # reqs =  ",".join(item.get("remote_requirements"))

    reqs = item.get("requirements")

    reqs = [i.get("requirement_number") for i in reqs]

    reqs = ", ".join(reqs)

    return reqs


def clean_illegal_chars(text):
    """
    清理 Excel 单元格中不允许使用的非法字符
    """
    if not isinstance(text, str):
        text = str(text)

    # 替换所有控制字符(ASCII值小于32的字符，除了\t, \n, \r)
    result = ""
    for char in text:
        if ord(char) < 32 and char not in '\t\n\r':
            continue
        result += char

    # 处理包含十六进制数据的特殊情况
    if '(' in result and ')' in result and any(x in result for x in ['\\x', '0x']):
        # 尝试提取有意义的部分或简单地替换为说明性文本
        return "[包含二进制数据 - 已省略]"

    return result


class TestReportDownloadView(APIView):
    def get(self, request, pk=None):
        try:
            model = TestReportModel()
            plan = model.retrieve(pk)

            if plan:
                plan_stats(plan)
            else:
                return Response({"err_code": 1, "msg": ""}, status.HTTP_400_BAD_REQUEST)

            # user_fs_info = UserFSInfo.objects.order_by("-update_time").first()
            # token = user_fs_info.access_token
            # tc = [i.get("number") for i in plan.get("test_cases")]
            # f, data = fs_service.get_requirements_by_case_numbers(token, plan.get("project_id"), plan.get("test_type"), tc)
            # if not f:
            #     return HttpResponse("Invalid request", status=400)
            # requirements = {}
            # for k in data.get("data"):
            #     reqs = data.get("data").get(k)
            #     requirements[k] = []
            #     for req in reqs:
            #         module = req.get("module")
            #         num = req.get("num")
            #         version = req.get("version")
            #         num = num.split("-")
            #         num.insert(-1, module)
            #         num = "-".join(num)
            #         # num = num + "(" + version + ")"
            #         requirements[k].append(num)
            # for i in plan.get("test_cases"):
            #     i["remote_requirements"] = requirements.get(i.get("number"), [])

            base_dir = settings.BASE_DIR
            file_path = os.path.join(base_dir, "config", "测试报告模板.xlsx")

            wb = load_workbook(file_path)

            ws = wb["封面"]

            ws.cell(row=7, column=4, value=plan.get("project_number"))

            ws = wb["总结"]
            stats = plan.get("stats")

            tester = plan.get("pic_name")

            ws.cell(row=2, column=2, value=plan.get("sample_information"))
            ws.cell(row=3, column=2, value=plan.get("m_version"))
            ws.cell(row=4, column=2, value="通过" if stats.get("result") else "不通过")
            ws.cell(row=5, column=2, value=tester)
            ws.cell(row=6, column=2, value=plan.get("p_start_time"))
            ws.cell(row=7, column=2, value=plan.get("p_end_time"))

            # for pv in plan.get("product_version"):
            #     ws.insert_rows(4)
            #     cell = ws.cell(row=4, column=1, value=pv.get("type_name"))
            #     cell._style = deepcopy(ws.cell(row=3, column=1)._style)
            #     cell = ws.cell(row=4, column=2, value=pv.get("name"))
            #     cell._style = deepcopy(ws.cell(row=3, column=2)._style)

            row_index = 0
            for index, row in enumerate(ws.iter_rows()):
                if row[0].value == "测试用例汇总分析":
                    row_index = index + 1
                    break
            row_index += 2

            data = FunctionModel().list().get("results")
            module_map = {"合计": "合计"}
            for i in data:
                module_map[i["number"]] = i["name"]
                if i.get("children"):
                    for j in i["children"]:
                        module_map["{}-{}".format(i["number"], j["number"])] = j["name"]
                        if j.get("children"):
                            for k in j["children"]:
                                module_map["{}-{}-{}".format(i["number"], j["number"], k["number"])] = k["name"]

            count = 0
            for i in stats.get("m_stats"):
                ws.cell(row=row_index + count, column=1, value=module_map.get(i.get("module")))
                ws.cell(row=row_index + count, column=2, value=i.get("total"))
                ws.cell(row=row_index + count, column=3, value=i.get("non_exec"))
                ws.cell(row=row_index + count, column=4, value=i.get("pass"))
                ws.cell(row=row_index + count, column=5, value=i.get("ng"))
                ws.cell(row=row_index + count, column=6, value=i.get("pass_rate"))
                ws.cell(row=row_index + count, column=11, value="通过" if i.get("result") else "不通过")

                count += 1

            # 创建新表格
            ws2 = wb["测试用例执行结果"]
            ws2.cell(row=1, column=1, value="序号")
            ws2.cell(row=1, column=2, value="需求ID")
            ws2.cell(row=1, column=3, value="用例ID")
            ws2.cell(row=1, column=4, value="用例名称")
            ws2.cell(row=1, column=5, value="用例来源")
            ws2.cell(row=1, column=6, value="用例类型")
            ws2.cell(row=1, column=7, value="用例生成方法")
            ws2.cell(row=1, column=8, value="测试方法")
            ws2.cell(row=1, column=9, value="用例版本")
            ws2.cell(row=1, column=10, value="优先级")
            ws2.cell(row=1, column=11, value="执行方式")
            ws2.cell(row=1, column=12, value="前提条件")
            ws2.cell(row=1, column=13, value="操作步骤")
            ws2.cell(row=1, column=14, value="预期结果")
            ws2.cell(row=1, column=15, value="测试值")
            ws2.cell(row=1, column=16, value="测试结果")

            for cell in ws2["1:1"]:
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='left', vertical='center')

            for index, testcase in enumerate(plan.get("test_cases")):
                test_methods = []
                index_cell = ws2.cell(row=index + 2, column=1, value=index + 1)
                index_cell.alignment = Alignment(horizontal='left', vertical='center')
                ws2.cell(row=index + 2, column=2, value=parse_requirements(testcase))
                ws2.cell(row=index + 2, column=3, value=testcase.get("number"))
                name_cell = ws2.cell(row=index + 2, column=4, value=testcase.get("name"))
                name_cell.alignment = Alignment(horizontal='left', vertical='center', wrapText=True)
                ws2.cell(row=index + 2, column=5, value=source_map.get(testcase.get("source"), ""))
                ws2.cell(row=index + 2, column=6, value=type_map.get(testcase.get("type"), ""))
                ws2.cell(row=index + 2, column=7,
                         value=generation_method_map.get(testcase.get("generation_method"), ""))
                for test_method in testcase.get("test_method"):
                    test_methods.append(test_method_map.get(test_method))
                ws2.cell(row=index + 2, column=8, value="\n".join(test_methods))
                version = f'V{round((float(testcase.get("result").get("test_case_version"))), 2)}'
                ws2.cell(row=index + 2, column=9, value=version)
                ws2.cell(row=index + 2, column=10, value=priority_map.get(testcase.get("priority"), ""))
                ws2.cell(row=index + 2, column=11, value=execute_mode_map.get(testcase.get("execute_mode"), ""))
                preconditions_cell = ws2.cell(row=index + 2, column=12, value=testcase.get("preconditions"))
                preconditions_cell.alignment = Alignment(horizontal='left', vertical='top', wrapText=True)
                temp_steps = []
                temp_expectations = []
                for i in range(len(testcase.get("test_steps"))):
                    desc = testcase.get("test_steps")[i].get("desc")
                    temp_steps.append(f"{i + 1}.{desc}")
                    expectation = testcase.get("test_steps")[i].get("expectation")
                    temp_expectations.append(f"{i + 1}.{expectation}")
                steps = "\n".join(temp_steps)
                expectations = "\n".join(temp_expectations)
                steps_cell = ws2.cell(row=index + 2, column=13, value=steps)
                steps_cell.alignment = Alignment(horizontal='left', vertical='top', wrapText=True)
                expectations_cell = ws2.cell(row=index + 2, column=14, value=expectations)
                expectations_cell.alignment = Alignment(horizontal='left', vertical='top', wrapText=True)
                value = testcase.get("result").get("value")
                result_two = testcase.get("result").get("result_two")

                # 假设 value 是包含字符串 "\n" 的值
                value = value.replace("\\n", "\n")  # 将字符串 "\n" 替换为真正的换行符
                value = clean_illegal_chars(value)

                # 写入测试值
                value_cell = ws2.cell(row=index + 2, column=15, value=value)
                value_cell.alignment = Alignment(horizontal='left', vertical='top', wrapText=True)  # 设置自动换行

                # 写入测试结果
                result_cell = ws2.cell(row=index + 2, column=16,
                                       value="PASS" if result_two == 1 else "NG" if result_two == 0 else "NA" if result_two == 2 else "NT")

                # 根据 result_two 的值设置单元格样式
                if result_two == 1:  # PASS
                    result_cell.font = Font(color="008000", bold=True)  # 绿色字体，加粗
                    result_cell.alignment = Alignment(horizontal='center', vertical='center')  # 水平和垂直居中
                elif result_two == 0:  # NG
                    result_cell.font = Font(color="FF0000", bold=True)  # 红色字体，加粗
                    result_cell.alignment = Alignment(horizontal='center', vertical='center')  # 水平和垂直居中
                elif result_two == 2:  # NA
                    result_cell.font = Font(color="FFA500", bold=True)  # 橙色字体，加粗
                    result_cell.alignment = Alignment(horizontal='center', vertical='center')  # 水平和垂直居中
                elif result_two == 3:  # NT
                    result_cell.font = Font(color="808080", bold=True)  # 灰色字体，加粗
                    result_cell.alignment = Alignment(horizontal='center', vertical='center')  # 水平和垂直居中

            output = BytesIO()
            wb.save(output)
            output.seek(0)

            with pd.ExcelWriter(output, engine='openpyxl', mode="a") as writer:
                pass

            output.seek(0)
            response = HttpResponse(
                output,
                content_type="application/octet-stream"
            )
            filename = "测试报告.xlsx"
            filename = urllib.parse.quote(filename)
            response['Content-Disposition'] = f'attachment; filename={filename}'

            return response

        except Exception:
            print(testcase)
            print(value, type(value))
            logger.error(traceback.format_exc())
            return HttpResponse("Invalid request", status=400)


class SoftwareReleaseApprovalCreateView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            params = {}
            for k in request.data:
                params[k] = request.data.get(k)

            params["versions"] = json.loads(params.get("versions"))
            params["ota_validate"] = json.loads(params.get("ota_validate"))
            params["tester"] = json.loads(params.get("tester"))
            params["hardware_developer"] = json.loads(params.get("hardware_developer"))
            params["software_developer"] = json.loads(params.get("software_developer"))
            params["pm"] = json.loads(params.get("pm"))
            params["npi"] = json.loads(params.get("npi"))
            params["release_reason"] = json.loads(params.get("release_reason"))

            user = request.user

            f, data = fs_app.upload_file(
                file_name=params["soft_report"].name,
                file_type="attachment",
                content=params["soft_report"],
            )
            if not f:
                return Response({"err_code": 1, "msg": data}, status.HTTP_400_BAD_REQUEST)
            params["soft_report"] = [data.get("data").get("code")]

            f, data = fs_app.upload_file(
                file_name=params["soft_review_minutes"].name,
                file_type="attachment",
                content=params["soft_review_minutes"],
            )
            if not f:
                return Response({"err_code": 1, "msg": data}, status.HTTP_400_BAD_REQUEST)
            params["soft_review_minutes"] = [data.get("data").get("code")]

            f, data = fs_app.create_software_release_approval(user.open_id, **params)

            if not f:
                return Response({"err_code": 1, "msg": data}, status.HTTP_400_BAD_REQUEST)

            return Response({"err_code": 0, "data": data, "msg": "ok"}, status.HTTP_200_OK)

        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ApprovalHelperView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            params = request.data

            method = params.get("method")

            if method == "get_project_names":
                res = requests.post(
                    "http://portal.hwtc.com.cn/fitem/batch_get",
                    json={"token": "hw-kqTpHBWqGXWXqIpwOMJJeoImoDtreEpt"}
                )
                res = res.json()
                return Response(res, status.HTTP_200_OK)
            elif method == "get_customer_names":
                res = requests.post(
                    "http://portal.hwtc.com.cn/customer/batch_get",
                    json={"token": "hw-kqTpHBWqGXWXqIpwOMJJeoImoDtreEpt"}
                )
                res = res.json()
                return Response(res, status.HTTP_200_OK)
            elif method == "get_soft_types":
                res = requests.post(
                    "https://ipd.hiway.com:10002/system-api/system/dict/public/fs/list",
                    json={"token": "PROJECT_VERSION"}
                )
                res = res.json()
                return Response(res, status.HTTP_200_OK)

            return Response({"err_code": 0, "msg": "method 不支持"}, status.HTTP_400_BAD_REQUEST)

        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
