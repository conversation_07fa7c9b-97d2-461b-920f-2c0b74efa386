import json
import traceback
import logging
import os
from io import BytesIO
from datetime import datetime

from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import J<PERSON>TAuthentication
from openpyxl import load_workbook
from django.http import HttpResponse
import urllib.parse
from openpyxl.styles import Alignment

from .serializers import (
    TestCaseListSerializer, TestCaseSerializer, TestCaseUpdateSerializer,
    PublicTestCaseListSerializer, PublicTestCaseSerializer, TestCaseMoveSerializer,
    TestCaseVersionUpdateSerializer,
    TestCaseArchiveListSerializer,
    PublicTestCaseUpdateSerializer,
    TestCasePatchSyncSerializer,
    TestCasePatchImportSerializer,
    ProjectTestCaseCopySerializer, TestCaseUpdateStepsSerializer,
    TestCaseRelateRequirementsSerializer,
)
from .models import TestCaseModel, PublicTestCaseModel, TestCaseArchiveModel, TestCaseRequirementMap
from utils.fs_service import FSService
from users.models import UserFSInfo
from .signals import test_case_review_signal, test_case_reviewed_signal
from functions.models import FunctionModel

logger = logging.getLogger("machine")

fs_service = FSService()


class TestCasesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestCaseListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = TestCaseSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user

            model = TestCaseModel()
            ts_id = model.create(
                **serializer.validated_data,
                creator_email=user.email, creator_name=user.username, creator_employee_number=user.employee_number
            )

            return Response({"err_code": 0, "data": {"id": ts_id}, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = TestCaseModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            user = request.user

            model = TestCaseModel()

            r = model.retrieve(pk=pk)
            if not r:
                return Response({"err_code": 1, "msg": "无此项记录"}, status.HTTP_400_BAD_REQUEST)

            # if r.get("creator_employee_number") != user.employee_number:
            #     return Response({"err_code": 1, "msg": "用例创建人不符"}, status.HTTP_400_BAD_REQUEST)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            # if user.email not in members:
            #     return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            serializer = TestCaseUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            status_ = r.get("status")
            if status_ == "REVIEWING":
                return Response({"err_code": 1, "msg": "评审中的用例不允许修改！"}, status.HTTP_400_BAD_REQUEST)

            version = int(r.get("version"))
            if status_ == "APPROVED":
                version += 1
                if r.get("related_requirements"):
                    for i in r.get("requirements"):
                        TestCaseRequirementMap.objects.create(
                            test_case_id=r.get("id"),
                            test_case_version=str(version),
                            requirement_id=i.get("requirement_id"),
                            requirement_version=i.get("requirement_version"),
                            requirement_number=i.get("requirement_number"),
                        )

            model = TestCaseModel()
            model.update(pk=pk, version=str(version), status="PENDING", **serializer.data)

            return Response({"err_code": 0, "data": {"id": pk}, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            user = request.user

            model = TestCaseModel()

            r = model.retrieve(pk=pk)
            if not r:
                return Response({"err_code": 1, "msg": "无此项记录"}, status.HTTP_400_BAD_REQUEST)

            # if r.get("creator_employee_number") != user.employee_number:
            #     return Response({"err_code": 1, "msg": "用例创建人不符"}, status.HTTP_400_BAD_REQUEST)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            # if user.email not in members:
            #     return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            if r.get("status") == "REVIEWING":
                return Response({"err_code": 1, "msg": "评审中的用例不能删除"}, status.HTTP_400_BAD_REQUEST)

            if r.get("status") == "APPROVED":
                return Response({"err_code": 1, "msg": "评审通过的用例不能删除"}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseModel()
            model.delete(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseByNumberView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            number = request.query_params.get("number")

            model = TestCaseModel()
            result = model.retrieve_by_number(number=number)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseDiscardView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        try:
            user = request.user

            model = TestCaseModel()

            r = model.retrieve(pk=pk)

            if not r:
                return Response({"err_code": 1, "msg": "无此项记录"}, status.HTTP_400_BAD_REQUEST)

            # if r.get("creator_employee_number") != user.employee_number:
            #     return Response({"err_code": 1, "msg": "用例创建人不符"}, status.HTTP_400_BAD_REQUEST)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            if r.get("status") in ("REVIEWING", "DEPRECATED"):
                return Response({"err_code": 1, "msg": "该状态下的用例不能废弃"}, status.HTTP_400_BAD_REQUEST)

            model.update(status="DEPRECATED", cached_status=r.get("status"), pk=pk)

            # 通知产品开发平台废弃用例
            case_number = r.get("number")
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            fs_service.push_case_discard(token=user_fs_info.access_token,
                                         project_code=project_number,
                                         use_state="DISABLE",
                                         number_list=[case_number])

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseRecoverView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        try:
            user = request.user

            model = TestCaseModel()

            r = model.retrieve(pk=pk)
            if not r:
                return Response({"err_code": 1, "msg": "无此项记录"}, status.HTTP_400_BAD_REQUEST)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            model.update(status=r.get("cached_status"), pk=pk)

            # 通知产品开发平台废弃用例
            case_number = r.get("number")
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            fs_service.push_case_discard(token=user_fs_info.access_token,
                                         project_code=project_number,
                                         use_state="ENABLE",
                                         number_list=[case_number])

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseMoveView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        try:
            serializer = TestCaseMoveSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user

            model = TestCaseModel()

            r = model.retrieve(pk=pk)
            if not r:
                return Response({"err_code": 1, "msg": "无此项记录"}, status.HTTP_400_BAD_REQUEST)
            #
            # if r.get("creator_employee_number") != user.employee_number:
            #     return Response({"err_code": 1, "msg": "用例创建人不符"}, status.HTTP_400_BAD_REQUEST)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            model.move(**serializer.validated_data, pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class PublicTestCasesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = PublicTestCaseListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = PublicTestCaseModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = PublicTestCaseSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user

            model = PublicTestCaseModel()
            model.create(
                **serializer.validated_data,
                creator_email=user.email, creator_name=user.username, creator_employee_number=user.employee_number,
                status="PENDING"
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class PublicTestCaseDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = PublicTestCaseModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = PublicTestCaseUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = PublicTestCaseModel()
            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            user = request.user

            model = PublicTestCaseModel()

            r = model.retrieve(pk=pk)
            if not r:
                return Response({"err_code": 1, "msg": "无此项记录"}, status.HTTP_400_BAD_REQUEST)

            if r.get("creator_employee_number") != user.employee_number:
                return Response({"err_code": 1, "msg": "用例创建人不符"}, status.HTTP_400_BAD_REQUEST)

            model = PublicTestCaseModel()
            model.delete(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseSyncView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            t_id = request.data.get("id")
            if t_id is None:
                return Response({"err_code": 1, "msg": "id不能为空！"}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            model = TestCaseModel()
            t = model.retrieve(pk=t_id)
            test_cases = [t]
            if not t:
                return Response({"err_code": 1, "msg": "id无效，对应用例不存在！"}, status.HTTP_400_BAD_REQUEST)

            version = int(t.get("version"))

            f, data = fs_service.sync_test_case(token, t, user.email, version)

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            test_case_review_signal.send(
                self.__class__,
                test_cases=test_cases,
            )

            model.update(pk=t_id, status="REVIEWING")

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCasePatchSyncView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = TestCasePatchSyncSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            project_number = serializer.validated_data.get("project_number")
            category = serializer.validated_data.get("category")
            ids = serializer.validated_data.get("ids")

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            model = TestCaseModel()
            test_cases = []
            for i in ids:
                test_case = model.retrieve(pk=i)

                if not test_case:
                    return Response({"err_code": 1, "msg": "id无效，对应用例不存在！"}, status.HTTP_400_BAD_REQUEST)

                test_cases.append(test_case)

            f, data = fs_service.patch_sync_test_case(token, project_number, category, user.email, test_cases)

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            test_case_review_signal.send(
                self.__class__,
                test_cases=test_cases,
            )

            for i in ids:
                model.update(pk=i, status="REVIEWING")

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseVersionUpdateView(APIView):
    def post(self, request):
        try:
            logger.info("request.data %s", request.data)

            serializer = TestCaseVersionUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            logger.info("serializer.validated_data %s", serializer.validated_data)

            model = TestCaseModel()

            data = serializer.validated_data.get("datas")
            test_cases = []
            for i in data:
                project_number = i.get("projectCode")
                num = i.get("num")
                version = i.get("version")
                review_status = i.get("reviewStatus")

                r = model.list(project_number=project_number, number_c=num, version=str(version), pagesize=1)
                if r.get("results"):
                    tc = r.get("results")[0]
                    test_case_id = tc.get("id")

                    if review_status == "ACCEPT":
                        model.update_version(test_case_id, "APPROVED")
                        test_cases.append(tc)

                    elif review_status == "NOT_PASS":
                        model.update_version(test_case_id, "REJECTED")

                    elif review_status == "CANCEL":
                        model.update_version(test_case_id, "CANCEL")

            if test_cases:
                test_case_reviewed_signal.send(
                    self.__class__,
                    test_cases=test_cases,
                )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseArchivesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestCaseArchiveListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseArchiveModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseArchiveDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = TestCaseModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestTypesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            project_number = request.query_params.get("project_number")

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            f, data = fs_service.get_test_types(token=token, project_number=project_number)
            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            content = data.get("data")

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCasePatchImportView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = TestCasePatchImportSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            project_number = serializer.validated_data.get("project_number")
            project_id = serializer.validated_data.get("project_id")
            project_name = serializer.validated_data.get("project_name")
            product_type_id = serializer.validated_data.get("product_type_id")
            test_case_ids = serializer.validated_data.get("test_case_ids")

            user = request.user

            p_model = PublicTestCaseModel()
            model = TestCaseModel()

            success_ids = []
            failed_ids = []
            for id_ in test_case_ids:
                tc = p_model.retrieve(pk=id_)

                module = tc.get("module")
                module_2level = tc.get("module_2level")
                module_3level = tc.get("module_3level")

                module = ", ".join([i for i in [module, module_2level, module_3level] if i])

                test_steps = []
                for j in tc.get("test_steps"):
                    test_steps.append({
                        "type": j.get("type"),
                        "params": j.get("params"),
                        "desc": j.get("desc"),
                        "expectation": j.get("expectation"),
                    })

                try:
                    model.create(
                        project_name=project_name, project_number=project_number, project_id=project_id,
                        creator_email=user.email, creator_name=user.username,
                        creator_employee_number=user.employee_number,
                        name=tc.get("name"),
                        module=module,
                        type=tc.get("type"),
                        action_type=tc.get("action_type"),
                        priority=tc.get("priority"),
                        related_requirements=tc.get("related_requirements"),
                        source=tc.get("source"),
                        generation_method=tc.get("generation_method"),
                        test_method=tc.get("test_method"),
                        execute_mode=tc.get("execute_mode"),
                        function_safe_attrib=tc.get("function_safe_attrib"),
                        preconditions=tc.get("preconditions"),
                        steps=tc.get("steps"),
                        expected=tc.get("expected"),
                        remark=tc.get("remark"),
                        cycle=tc.get("cycle"),
                        es_source=tc.get("es_source"),
                        es_id=tc.get("es_id"),
                        product_type_id=product_type_id,
                        public_test_case_id=tc.get("id"),
                        test_steps=test_steps,
                        status="PENDING"
                    )
                    success_ids.append(id_)
                except Exception:
                    failed_ids.append(id_)
                    logger.error(traceback.format_exc())

            return Response(
                {"err_code": 0, "failed_ids": failed_ids, "success_ids": success_ids, "msg": "ok"},
                status.HTTP_200_OK
            )

        except Exception:
            logger.error(traceback.format_exc())
        return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProjectTestCaseCopyView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = ProjectTestCaseCopySerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user

            model = TestCaseModel()

            source_project_number = serializer.validated_data.get("source_project_number")
            target_project_name = serializer.validated_data.get("target_project_name")
            target_project_number = serializer.validated_data.get("target_project_number")
            target_project_id = serializer.validated_data.get("target_project_id")
            f, data = fs_service.get_project_members(project_code=target_project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            model.copy_by_project(
                source_project_number=source_project_number,
                target_project_name=target_project_name,
                target_project_number=target_project_number,
                target_project_id=target_project_id,
                user=user
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseDownloadView(APIView):
    def get(self, request):
        try:
            serializer = TestCaseListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseModel()
            content = model.list2(**serializer.validated_data)

            data = FunctionModel().list().get("results")
            module_map = {}
            for i in data:
                module_map[i["number"]] = "/" + i["name"]
                if i.get("children"):
                    for j in i["children"]:
                        module_map["{}-{}".format(i["number"], j["number"])] = "/{}/{}".format(i["name"], j["name"])
                        if j.get("children"):
                            for k in j["children"]:
                                module_map[
                                    "{}-{}-{}".format(i["number"], j["number"], k["number"])] = "/{}/{}/{}".format(
                                    i["name"], j["name"], k["name"])
            status_map = {
                "PENDING": "待评审",
                "REVIEWING": "评审中",
                "APPROVED": "评审通过",
                "REJECTED": "评审不通过",
                "DEPRECATED": "废弃",
            }

            action_type_map = {
                "SAT": "系统合格性测试",
                "IST": "系统集成测试",
                "SQT": "软件合格性测试",
                "SIT": "软件集成测试",
                "SUT": "软件单元测试",
                "HT": "硬件测试",
            }

            test_type_map = {
                "DURABLE_TEST": "耐久测试",
                "PERFORMANCE_TEST": "性能测试",
                "FUNCTION_TEST": "功能测试",
                "PROTOCOL_STACK_TEST": "协议栈测试"
            }

            source_map = {
                "TASK_CHANGE": "用例库沿用",
                "TASK_AFFIRM": "需求分析",
                "NEW_PROJECT": "需求变更",
                "HORIZONTAL_SCALING": "横向扩展"
            }

            generation_method_map = {
                "BOUNDARY_VALUE_METHOD": "边界值法",
                "FRUIT_GRAPH_METHOD": "因果法",
                "DECISION_TABLE_DRIVE": "判定表驱法",
                "FUNCTION_DIAGRAM_METHOD": "功能图法",
                "SCENE_METHOD": "场景法",
                "EQUIVALENCE_CLASS": "等价类",
                "FIELD_EXPERIENCE_ANALYSIS": "现场经验分析",
                "EXTERNAL_AND_INTERNAL_INTERFACE_ANALYSIS": "外部和内部接口分析法",
                "PROCESS_ANALYSIS": "流程分析法",
                "BACKWARD_ANALYSIS": "反向分析",
                "FORWARD_ANALYSIS": "正向分析",
                "ENVIRONMENTAL_CONDITIONS_AND_OPERATIONAL_USE_CASE_ANALYSIS": "环境条件和操作用例分析",
                "MISGUESS": "错误猜错法",
                "SEQUENCE_AND_SOURCE_ANALYSIS": "序列和来源的分析",
                "COMMON_LIMIT_CONDITIONS_4_DEPENDENCE": "相依性的常见极限条件",
                "ANALYSIS_OPERATING_CONDITIONS_USE_CASES": "用例的运行条件分析",
                "DEMAND_ANALYSIS": "基于需求分析",
            }

            priority_map = {
                "HIGH": "高",
                "MIDDLE": "中",
                "LOW": "低",
            }

            execute_mode_map = {
                "AUTOMATED_EXECUTION": "自动化测试",
                "MANUAL_EXECUTION": "手动测试",
                "SEMI_AUTOMATED_EXECUTION": "半自动化测试",
            }

            function_safe_attrib_map = {
                "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AA": "ASIL A",
                "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AD": "ASIL D",
                "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQD": "ASIL QM(D)",
                "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQC": "ASIL QM(C)",
                "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_NA": "N/A",
                "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQB": "ASIL QM(B)",
                "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQA": "ASIL QM(A)",
                "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AB": "ASIL B",
            }

            test_method_map = {
                "PRESSURE_TEST": "压力测试",
                "RESOURCE_USAGE_TESTING": "资源使用情况测试",
                "INTERACTION_COMMUNICATION_TESTING": "互动/沟通测试",
                "INTERFACE_CONSISTENCY_CHECK": "接口一致性检查",
                "TESTS_BASED_ON_FIELD_EXPERIENCE": "根据现场经验进行的测试",
                "FALSE_GUESS_TEST": "错误猜测测试",
                "PERFORMANCE_TEST": "性能测试",
                "FAULT_INJECTION_TEST": "故障注入测试",
                "BACK_TO_BACK_TESTING": "背靠背测试",
                "INTERFACE_TEST": "基于接口测试",
                "DEMAND_TEST": "基于需求测试",
            }

            base_dir = settings.BASE_DIR
            file_path = os.path.join(base_dir, "config", "测试用例模板.xlsx")

            wb = load_workbook(file_path)

            sh = wb["用例"]

            rows = [
                ("用例ID", "number"),
                ("用例名称", "name"),
                ("项目", "project"),
                ("模块", "module"),
                ("用例活动类型", "action_type"),
                ("用例类型", "type"),
                ("版本", "version"),
                ("状态", "status"),
                ("优先级", "priority"),
                ("来源", "source"),
                ("用例生成方法", "generation_method"),
                ("测试方法", "test_method"),
                ("执行方式", "execute_mode"),
                ("功能安全属性", "function_safe_attrib"),
                ("前置条件", "preconditions"),
                ("测试步骤", "steps"),
                ("期望结果", "expected"),
                ("备注", "remark"),
                ("创建人", "creator_name"),
                ("创建时间", "create_time"),
            ]

            sh.column_dimensions["A"].width = 20
            sh.column_dimensions["B"].width = 30
            sh.column_dimensions["C"].width = 30
            sh.column_dimensions["D"].width = 20
            sh.column_dimensions["E"].width = 20
            sh.column_dimensions["F"].width = 20
            sh.column_dimensions["G"].width = 10
            sh.column_dimensions["H"].width = 20
            sh.column_dimensions["I"].width = 10
            sh.column_dimensions["J"].width = 20
            sh.column_dimensions["K"].width = 20
            sh.column_dimensions["L"].width = 20
            sh.column_dimensions["M"].width = 20
            sh.column_dimensions["N"].width = 20
            sh.column_dimensions["O"].width = 40
            sh.column_dimensions["P"].width = 40
            sh.column_dimensions["Q"].width = 40
            sh.column_dimensions["R"].width = 30
            sh.column_dimensions["S"].width = 20
            sh.column_dimensions["T"].width = 30
            sh.row_dimensions[1].height = 25

            alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

            for i, v in enumerate(rows):
                v = v[0]
                sh.cell(row=1, column=i + 1, value=v)

            for i1, v1 in enumerate(content):
                sh.row_dimensions[i1 + 2].height = 60

                for i2, v2 in enumerate(rows):
                    v2 = v2[1]

                    if v2 == "module":
                        l = [v1.get("module"), v1.get("module_2level"), v1.get("module_3level")]
                        value = "-".join([i for i in l if i])
                        value = module_map.get(value)
                    elif v2 == "status":
                        value = status_map.get(v1.get(v2), v1.get(v2))
                    elif v2 == "project":
                        value = f"{v1.get('project_name')}({v1.get('project_number')})"
                    elif v2 == "action_type":
                        value = action_type_map.get(v1.get(v2), v1.get(v2))
                    elif v2 == "type":
                        value = test_type_map.get(v1.get(v2), v1.get(v2))
                    elif v2 == "version":
                        value = f"V{v1.get('version')}.0"
                    elif v2 == "source":
                        value = source_map.get(v1.get(v2), v1.get(v2))
                    elif v2 == "priority":
                        value = priority_map.get(v1.get(v2), v1.get(v2))
                    elif v2 == "generation_method":
                        value = generation_method_map.get(v1.get(v2), v1.get(v2))
                    elif v2 == "execute_mode":
                        value = execute_mode_map.get(v1.get(v2), v1.get(v2))
                    elif v2 == "function_safe_attrib":
                        value = function_safe_attrib_map.get(v1.get(v2), v1.get(v2))
                    elif v2 == "test_method":
                        value = [test_method_map.get(i, i) for i in v1.get(v2)]
                        value = ", ".join(value)
                    else:
                        value = v1.get(v2)

                    sh.cell(row=i1 + 2, column=i2 + 1, value=value).alignment = alignment

            output = BytesIO()
            wb.save(output)
            output.seek(0)

            response = HttpResponse(
                output,
                content_type="application/octet-stream"
            )
            filename = "测试用例.xlsx"
            filename = urllib.parse.quote(filename)
            response['Content-Disposition'] = f'attachment; filename={filename}'

            return response

        except Exception:
            logger.error(traceback.format_exc())
            return HttpResponse("Server error", status=500)


class TestCaseStepsUpdateView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, pk=None):
        try:
            user = request.user

            model = TestCaseModel()

            r = model.retrieve(pk=pk)
            if not r:
                return Response({"err_code": 1, "msg": "无此项记录"}, status.HTTP_400_BAD_REQUEST)

            # if r.get("creator_employee_number") != user.employee_number:
            #     return Response({"err_code": 1, "msg": "用例创建人不符"}, status.HTTP_400_BAD_REQUEST)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            serializer = TestCaseUpdateStepsSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseModel()
            model.update_steps(pk=pk, **serializer.data)

            return Response({"err_code": 0, "data": {"id": pk}, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class RelateRequirementsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = TestCaseRelateRequirementsSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            test_case_id = serializer.validated_data.get("test_case_id")
            test_case_version = serializer.validated_data.get("test_case_version")
            requirements = serializer.validated_data.get("requirements")

            for r in requirements:
                TestCaseRequirementMap.objects.get_or_create(
                    test_case_id=test_case_id,
                    test_case_version=test_case_version,
                    requirement_id=r.get("id"),
                    requirement_version=r.get("version"),
                    defaults={
                        "requirement_number": r.get("number"),
                        'create_time': datetime.now()
                    }
                )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class RemoveRequirementsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = TestCaseRelateRequirementsSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            test_case_id = serializer.validated_data.get("test_case_id")
            test_case_version = serializer.validated_data.get("test_case_version")
            requirements = serializer.validated_data.get("requirements")

            for r in requirements:
                TestCaseRequirementMap.objects.filter(
                    test_case_id=test_case_id,
                    test_case_version=test_case_version,
                    requirement_id=r.get("id"),
                    requirement_version=r.get("version"),
                ).delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

