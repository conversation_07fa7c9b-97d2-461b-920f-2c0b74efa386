[uwsgi]
# Django-related settings
# the base directory (full path)
project = atpms
base = /data/atpms

# Django's wsgi file
module = atpms.wsgi

;# the virtualenv (full path)
;home = /path/to/your/virtualenv

# process-related settings
# master
master = true
# maximum number of worker processes
processes = 10
# the socket (use the full path to be safe
;socket = /tmp/atpms.sock
socket = 127.0.0.1:9001
# ... with appropriate permissions - may be needed
;chmod-socket = 664
# clear environment on exit
vacuum = true

#daemonize = /dev/null
daemonize = /home/<USER>/atpms/uwsgi.log

pidfile = /data/atpms/uwsgi.pid