import datetime

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone, sql_insert_many
from django.db import transaction


class DiffPackageGenerationRecordsModel:
    def __init__(self):
        self.table_name = "public.diff_tool_usage_records"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            params = {
                "project_name": kwargs.get("project_name"),
                "project_number": kwargs.get("project_number"),
                "input_file_type": kwargs.get("input_file_type"),
                "old_package_name": kwargs.get("old_package_name"),
                "old_package_version": kwargs.get("old_package_version"),
                "old_package_md5": kwargs.get("old_package_md5"),
                "new_package_name": kwargs.get("new_package_name"),
                "new_package_version": kwargs.get("new_package_version"),
                "new_package_md5": kwargs.get("new_package_md5"),
                "diff_package_name": kwargs.get("diff_package_name"),
                "diff_package_md5": kwargs.get("diff_package_md5"),
                "diff_package_path": kwargs.get("diff_package_path"),
                "diff_package_version": kwargs.get("diff_package_version"),
                "operator_name": kwargs.get("operator_name"),
                "operator_email": kwargs.get("operator_email"),
                "create_time": now,
            }

            sql = """
                INSERT INTO {table_name} (project_name, project_number, input_file_type, old_package_name, old_package_version,
                    old_package_md5, new_package_name, new_package_version, new_package_md5, diff_package_name, diff_package_md5, 
                    diff_package_path, diff_package_version, operator_name, operator_email, create_time) 
                VALUES (%(project_name)s, %(project_number)s, %(input_file_type)s, %(old_package_name)s, %(old_package_version)s,
                    %(old_package_md5)s, %(new_package_name)s, %(new_package_version)s, %(new_package_md5)s, %(diff_package_name)s,
                    %(diff_package_md5)s, %(diff_package_path)s, %(diff_package_version)s, %(operator_name)s, 
                    %(operator_email)s, %(create_time)s)
                ;
            """.format(table_name=self.table_name)

            sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        diff_package_md5 = kwargs.get("diff_package_md5")
        if diff_package_md5 is not None and diff_package_md5 != '':
            sql_where_list.append("diff_package_md5 = %(diff_package_md5)s")
            params["diff_package_md5"] = diff_package_md5

        diff_package_name_re = kwargs.get("diff_package_name_re")
        if diff_package_name_re is not None and diff_package_name_re != '':
            sql_where_list.append("diff_package_name ~* %(diff_package_name_re)s")
            params["diff_package_name_re"] = diff_package_name_re

        diff_package_version_re = kwargs.get("diff_package_version_re")
        if diff_package_version_re is not None and diff_package_version_re != '':
            sql_where_list.append("diff_package_version ~* %(diff_package_version_re)s")
            params["diff_package_version_re"] = diff_package_version_re

        diff_package_md5_re = kwargs.get("diff_package_md5_re")
        if diff_package_md5_re is not None and diff_package_md5_re != '':
            sql_where_list.append("diff_package_md5 ~* %(diff_package_md5_re)s")
            params["diff_package_md5_re"] = diff_package_md5_re

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY create_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, project_name, project_number, input_file_type, old_package_name, old_package_version, old_package_md5,
                new_package_name, new_package_version, new_package_md5, diff_package_name, diff_package_md5, diff_package_path,
                diff_package_version, diff_package_status, operator_name, operator_email, create_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        for result in content["results"]:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, project_name, project_number, input_file_type, old_package_name, old_package_version, old_package_md5,
                new_package_name, new_package_version, new_package_md5, diff_package_name, diff_package_md5, diff_package_path,
                diff_package_version, diff_package_status, operator_name, operator_email, create_time
            FROM {table_name}
            WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def update_verify_status(self, id, diff_package_status):
        params = {
            "id": id,
            "diff_package_status": diff_package_status,
        }

        sql = """
            UPDATE {table_name}     
                SET 
                diff_package_status = %(diff_package_status)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
        )

        sql_execute(sql, params)

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result


class DiffPackageShareUrlsModel:
    def __init__(self):
        self.table_name = "public.diff_package_share_urls"

    def create(self, **kwargs):
        now = datetime.datetime.now()

        params = {
            "diff_record_id": kwargs.get("diff_record_id"),
            "token": kwargs.get("token"),
            "expiration_time": kwargs.get("expiration_time"),
            "creator_name": kwargs.get("creator_name"),
            "creator_email": kwargs.get("creator_email"),
            "create_time": now,
        }

        sql = """
            INSERT INTO {table_name} (diff_record_id, token, expiration_time, creator_name, creator_email,
                create_time) 
            VALUES (%(diff_record_id)s, %(token)s, %(expiration_time)s, %(creator_name)s, %(creator_email)s,
                %(create_time)s)
            ;
        """.format(table_name=self.table_name)

        sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        diff_package_md5 = kwargs.get("diff_package_md5")
        if diff_package_md5 is not None and diff_package_md5 != '':
            sql_where_list.append("diff_package_md5 = %(diff_package_md5)s")
            params["diff_package_md5"] = diff_package_md5

        diff_package_name_re = kwargs.get("diff_package_name_re")
        if diff_package_name_re is not None and diff_package_name_re != '':
            sql_where_list.append("diff_package_name ~* %(diff_package_name_re)s")
            params["diff_package_name_re"] = diff_package_name_re

        diff_package_version_re = kwargs.get("diff_package_version_re")
        if diff_package_version_re is not None and diff_package_version_re != '':
            sql_where_list.append("diff_package_version ~* %(diff_package_version_re)s")
            params["diff_package_version_re"] = diff_package_version_re

        diff_package_md5_re = kwargs.get("diff_package_md5_re")
        if diff_package_md5_re is not None and diff_package_md5_re != '':
            sql_where_list.append("diff_package_md5 ~* %(diff_package_md5_re)s")
            params["diff_package_md5_re"] = diff_package_md5_re

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY create_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, project_name, project_number, input_file_type, old_package_name, old_package_version, old_package_md5,
                new_package_name, new_package_version, new_package_md5, diff_package_name, diff_package_md5, diff_package_path,
                diff_package_version, diff_package_status, operator_name, operator_email, create_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        for result in content["results"]:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, token):
        params = {
            "token": token,
        }
        sql = """
            SELECT url.id, url.expiration_time, url.create_time, url.creator_name, url.creator_email,
                ur.project_name, ur.project_number, ur.old_package_version, ur.new_package_version, ur.operator_name,
                ur.diff_package_status, ur.diff_package_version,
                ur.diff_package_path, ur.diff_package_name, ur.diff_package_md5
            FROM {table_name} as url left join public.diff_tool_usage_records as ur
            on url.diff_record_id = ur.id
            WHERE url.token = %(token)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result["create_time"] + datetime.timedelta(seconds=result["expiration_time"]) < datetime.datetime.now():
            result = None

        if result:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def update_verify_status(self, id, diff_package_status):
        params = {
            "id": id,
            "diff_package_status": diff_package_status,
        }

        sql = """
            UPDATE {table_name}     
                SET 
                diff_package_status = %(diff_package_status)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
        )

        sql_execute(sql, params)

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result
