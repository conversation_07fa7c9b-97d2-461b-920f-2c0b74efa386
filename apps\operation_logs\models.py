from django.db import models


class OperationLog(models.Model):
    user_email = models.Char<PERSON><PERSON>(max_length=255, null=True, blank=True)
    user_name = models.CharField(max_length=255, null=True, blank=True)
    action = models.Char<PERSON>ield(max_length=255)
    method = models.Char<PERSON>ield(max_length=10)
    path = models.CharField(max_length=255)
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    status_code = models.IntegerField()
    response_time = models.FloatField()
    is_success = models.BooleanField()
    exception = models.TextField(null=True, blank=True)
    user_agent = models.CharField(max_length=255, null=True, blank=True)
    request_data = models.TextField(null=True, blank=True)
    response_data = models.TextField(null=True, blank=True)

    class Meta:
        managed = False
        app_label = 'operation_logs'
        db_table = 'operation_logs'

    def __str__(self):
        return f'{self.user_name} - {self.action} - {self.timestamp}'
