CREATE TABLE public.luminance_cure (
    id INT PRIMARY KEY,
    group_name VARCHAR(50),
    group_type VARCHAR(30),
    "desc" VARCHAR(255),
    coordinate_type VARCHAR(40),
    "default" VARCHAR(100),
    "min" VARCHAR(50),
    "max" VARCHAR(50),
    type VARCHAR(20),
    name VARCHAR(50),
    params_key VARCHAR(50) NOT NULL
);

-- 为字段添加注释
COMMENT ON COLUMN public.luminance_cure.id IS '自增主键ID';
COMMENT ON COLUMN public.luminance_cure.group_name IS '所属分组名称';
COMMENT ON COLUMN public.luminance_cure.group_type IS '分组类型（如：系统默认、用户自定义等）';
COMMENT ON COLUMN public.luminance_cure."desc" IS '描述/注释信息';
COMMENT ON COLUMN public.luminance_cure.coordinate_type IS '坐标类型（如：WGS84、GCJ02、平面坐标等）';
COMMENT ON COLUMN public.luminance_cure."default" IS '默认值（支持多类型，以字符串存储）';
COMMENT ON COLUMN public.luminance_cure."min" IS '最小值（数值类型）';
COMMENT ON COLUMN public.luminance_cure."max" IS '最大值（数值类型）';
COMMENT ON COLUMN public.luminance_cure.type IS '数据类型（如：int、varchar、float、datetime等）';
COMMENT ON COLUMN public.luminance_cure.name IS '名称（字段/参数的显示名称）';
COMMENT ON COLUMN public.luminance_cure.params_key IS '键名（唯一标识参数的键，如接口参数名、配置项键等）';


ALTER TABLE public.luminance_cure
ALTER COLUMN "default" TYPE VARCHAR(255);

ALTER TABLE public.luminance_cure
add COLUMN var_name varchar(255);
ALTER TABLE public.luminance_cure
add COLUMN node_level varchar(255);



CREATE TABLE public.luminance_defind_value (
    id SERIAL PRIMARY KEY,
    -- 公共字段（与表一一致，确保关联一致性）
    project_code VARCHAR(100) NOT NULL,
    project_name VARCHAR(200) NOT NULL,
    project_gitlab VARCHAR(255) NOT NULL,
    project_branch VARCHAR(100) NOT NULL,
    node_level VARCHAR(100) NOT NULL,
    var_name VARCHAR(100) NOT NULL,
    tempre_map text[] NOT NULL,
    temparray_line text[] NOT NULL
);
