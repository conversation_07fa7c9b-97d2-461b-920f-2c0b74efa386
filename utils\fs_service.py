import json
import logging
import traceback

from django.conf import settings
import requests

logger = logging.getLogger("fs")

FS_CONF = settings.CONF.get("fs")

BASE_URL = FS_CONF.get("url")
PRODUCT_CODE = FS_CONF.get("product_code")
AUTH_CODE = FS_CONF.get("auth_code")
TIMEOUT = FS_CONF.get("timeout")


class FSService:

    def login1(self, code, model, product_code=PRODUCT_CODE, auth_code=AUTH_CODE):
        api = "auth/fs/login"

        url = BASE_URL + api

        data = {
            "code": code,
            "model": model,
            "productCode": product_code
        }

        headers = {
            "Authorization": f"Basic {auth_code}"
        }

        try:
            logger.info("登入 url: %s", url)
            response = requests.post(url, json=data, headers=headers, timeout=TIMEOUT)
            logger.info("登入 response: %s", response.text)
            if response.status_code != 200:
                return False, {"message": f"登入出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("登入出错！\n%s", traceback.format_exc())
            return False, {"message": f"登入出错。{traceback.format_exc()}"}

        return True, response

    def login2(self, username, password, auth_code=AUTH_CODE):
        api = "auth/user/login"

        url = BASE_URL + api

        data = {
            "username": username,
            "password": password
        }

        headers = {
            "Authorization": f"Basic {auth_code}"
        }

        try:
            logger.info("登入 url: %s", url)
            response = requests.post(url, json=data, headers=headers, timeout=TIMEOUT)
            logger.info("登入 response: %s", response.text)
            if response.status_code != 200:
                return False, {"message": f"登入出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("登入出错！\n%s", traceback.format_exc())
            return False, {"message": f"登入出错。{traceback.format_exc()}"}

        return True, response

    def get_organization(self, token, department_id="0", product_code=PRODUCT_CODE):
        api = "system/fs/group/list"

        url = BASE_URL + api

        params = {
            "productCode": product_code,
            "departmentId": department_id
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("获取组织结构 url: %s", url)
            response = requests.get(url, params=params, headers=headers, timeout=TIMEOUT)
            logger.info("获取组织结构 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"获取组织结构。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("获取组织结构出错！\n%s", traceback.format_exc())
            return False, {"message": f"获取组织结构出错。{traceback.format_exc()}"}

        return True, response

    def get_user_info(self, token, open_id, product_code=PRODUCT_CODE):
        api = "system/fs/user/openid"

        url = BASE_URL + api

        params = {
            "openId": open_id,
            "productCode": product_code
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("获取用户信息 url: %s", url)
            response = requests.get(url, params=params, headers=headers, timeout=TIMEOUT)
            logger.info("获取用户信息 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"获取用户信息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("获取用户信息出错！\n%s", traceback.format_exc())
            return False, {"message": f"获取用户信息出错。{traceback.format_exc()}"}

        return True, response

    def get_user_info_by_e_no(self, token, e_no, product_code=PRODUCT_CODE):
        api = "system/api/feign/user/employeeNo"

        url = BASE_URL + api

        params = {
            "employeeNo": e_no,
            "productCode": product_code
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("用工号获取用户信息 url: %s", url)
            response = requests.get(url, params=params, headers=headers, timeout=TIMEOUT)
            logger.info("用工号获取用户信息 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"用工号获取用户信息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            # if response.get("code") != 20000:
            #     return False, response
        except Exception:
            logger.error("用工号获取用户信息出错！\n%s", traceback.format_exc())
            return False, {"message": f"用工号获取用户信息出错。{traceback.format_exc()}"}

        return True, response

    def push_issue(self, token, **kwargs):
        api = "project/issue/public/addIssue"

        url = BASE_URL + api

        data = {
            "projectCode": kwargs.get("projectCode"),
            "title": kwargs.get("title"),
            "level": kwargs.get("level"),
            "module": kwargs.get("module"),
            "frontCondition": kwargs.get("frontCondition"),
            "testStep": kwargs.get("testStep"),
            "expectResult": kwargs.get("expectResult"),
            "probability": kwargs.get("probability"),
            "actualResult": kwargs.get("actualResult"),
            "testerOpenIds": kwargs.get("testerOpenIds"),
            "beginTime": kwargs.get("beginTime").strftime("%Y-%m-%d %H:%M:%S"),
            "endTime": kwargs.get("endTime").strftime("%Y-%m-%d %H:%M:%S"),
            "occurProjectProgress": kwargs.get("occurProjectProgress"),
            "handlerIdList": kwargs.get("handlerIdList"),
            "occurVersionList": kwargs.get("occurVersionList"),
        }

        if kwargs.get("starIdList"):
            data["starIdList"] = kwargs.get("starIdList")
        if kwargs.get("remark"):
            data["remark"] = kwargs.get("remark")
        if kwargs.get("test_case_number") and kwargs.get("test_case_version"):
            data["testManagerNum"] = kwargs.get("test_case_number")
            data["testManagerVersion"] = kwargs.get("test_case_version")

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("创建研发问题 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers, timeout=TIMEOUT)
            logger.info("创建研发问题 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"创建研发问题。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("创建研发问题出错！\n%s", traceback.format_exc())
            return False, {"message": f"创建研发问题出错。{traceback.format_exc()}"}

        return True, response

    def push_case_discard(self, token, project_code, use_state, number_list):
        api = "project/task-test-case/public/test/manage/update/use/state"

        url = BASE_URL + api

        data = {
            "projectCode": project_code,
            "useState": use_state,
            "numList": number_list,
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("废弃用例 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers, timeout=TIMEOUT)
            logger.info("废弃用例 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"废弃用例。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("废弃用例出错！\n%s", traceback.format_exc())
            return False, {"message": f"废弃用例出错。{traceback.format_exc()}"}

        return True, response

    def get_project_version_type(self, token):
        api = "system/dict/list/PROJECT_VERSION"

        url = BASE_URL + api

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("获取产品版本 url: %s", url)
            response = requests.get(url, headers=headers, timeout=TIMEOUT)
            logger.info("获取产品版本 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"获取产品版本。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("获取产品版本出错！\n%s", traceback.format_exc())
            return False, {"message": f"获取产品版本出错。{traceback.format_exc()}"}

        return True, response

    def get_project_version_number(self, token, project_number, type_):
        api = "project/version/public/search"

        url = BASE_URL + api

        headers = {
            "Authorization": f"Bearer {token}"
        }

        data = {
            "current": 0,
            "size": 1000,
            "projectCode": project_number,
            "type": type_
        }

        try:
            logger.info("获取版本编号 url: %s, data: %s", url, data)
            response = requests.post(url, json=data, headers=headers, timeout=TIMEOUT)
            logger.info("获取版本编号 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"获取版本编号。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("获取版本编号出错！\n%s", traceback.format_exc())
            return False, {"message": f"获取版本编号出错。{traceback.format_exc()}"}

        return True, response

    def search_user(self, token, name, product_code=PRODUCT_CODE):
        api = "system/fs/user/search/key"

        url = BASE_URL + api

        params = {
            "productCode": product_code,
            "realName": name
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("通过姓名模糊查询飞书成员 url: %s", url)
            response = requests.get(url, params=params, headers=headers, timeout=TIMEOUT)
            logger.info("通过姓名模糊查询飞书成员 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"通过姓名模糊查询飞书成员。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("通过姓名模糊查询飞书成员！\n%s", traceback.format_exc())
            return False, {"message": f"通过姓名模糊查询飞书成员。{traceback.format_exc()}"}

        return True, response

    def get_project_list(self, token, page=1, pagesize=10, **kwargs):
        api = "project/project/list"

        url = BASE_URL + api

        headers = {
            "Authorization": f"Bearer {token}"
        }

        data = {
            "current": page,
            "size": pagesize,
        }

        name = kwargs.get("name")
        if name is not None:
            data["name"] = name

        projectId = kwargs.get("projectId")
        if projectId is not None:
            data["projectId"] = projectId

        clientId = kwargs.get("clientId")
        if clientId is not None:
            data["clientId"] = clientId

        projectTypeId = kwargs.get("projectTypeId")
        if projectTypeId is not None:
            data["projectTypeId"] = projectTypeId

        start = kwargs.get("start")
        if start is not None:
            data["start"] = start

        end = kwargs.get("end")
        if end is not None:
            data["end"] = end

        try:
            # logger.info("查询项目列表 url: %s", url)
            response = requests.post(url, json=data, headers=headers, timeout=7)
            # logger.info("查询项目列表 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"查询项目列表。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("查询项目列表出错！\n%s", traceback.format_exc())
            return False, {"message": f"查询项目列表出错。{traceback.format_exc()}"}

        return True, response

    def get_project_list_all(self, token):
        api = "project/project/get/simple/project/data"

        url = BASE_URL + api

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("查询项目列表 url: %s", url)
            response = requests.get(url, headers=headers, timeout=7)
            logger.info("查询项目列表 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"查询项目列表。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("查询项目列表出错！\n%s", traceback.format_exc())
            return False, {"message": f"查询项目列表出错。{traceback.format_exc()}"}

        return True, response

    def get_project_detail(self, token, project_id):
        api = f"project/project/detail/{project_id}"

        url = BASE_URL + api

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("查询项目详细信息 url: %s", url)
            response = requests.get(url, headers=headers, timeout=TIMEOUT)
            logger.info("查询项目详细信息 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"查询项目详细信息。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("查询项目详细信息！\n%s", traceback.format_exc())
            return False, {"message": f"查询项目详细信息。{traceback.format_exc()}"}

        return True, response

    def sync_test_case(self, token, test_case, user, version):
        api = "project/task-test-case/public/test/manage/import"

        url = BASE_URL + api

        m1 = test_case.get("module")
        m2 = test_case.get("module_2level")
        m3 = test_case.get("module_3level")

        test_method = ",".join(test_case.get("test_method"))

        if version is None:
            pass

        # steps = []
        # count = 0
        # for i in test_case.get("test_steps"):
        #     count += 1
        #
        #     if not i.get("desc").strip():
        #         return False, {"message": "测试步骤描述不能为空！"}
        #
        #     steps.append({
        #         "stepDescription": i.get("desc"),
        #         "expectResult": i.get("expectation"),
        #         "sort": count,
        #     })

        steps = [{
            "stepDescription": test_case.get("steps"),
            "expectResult": test_case.get("expected"),
            "sort": 1,
        }]

        data = {
            "projectCode": test_case.get("project_number"),
            "module": "-".join([i for i in [m1, m2, m3] if i]),
            "num": test_case.get("number"),
            "version": version,
            "email": user,
            "data": {
                "title": test_case.get("name"),
                "categoryFormat": test_case.get("action_type"),
                "type": test_case.get("type"),
                "level": test_case.get("priority"),
                "testGenerationMethod": test_case.get("generation_method"),
                "testFunction": test_method,
                "safetyAttribute": test_case.get("function_safe_attrib"),
                "modeExecution": test_case.get("execute_mode"),
                "testSource": test_case.get("source"),
                "frontCondition": test_case.get("preconditions"),
                "caseProcedureDetails": steps,
                "remark": test_case.get("remark")
            }
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("同步测试用例 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers, timeout=TIMEOUT)
            logger.info("同步测试用例 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"同步测试用例。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("同步测试用例出错！\n%s", traceback.format_exc())
            return False, {"message": f"同步测试用例出错。{traceback.format_exc()}"}

        return True, response

    def update_product_version_status(self, project_number, version_name, status):
        api = "project-local-api/project/version/public/update/state"

        url = BASE_URL.split('project-api/')[0] + api

        data = {
            "projectCode": project_number,
            "name": version_name,
            "state": status,
        }

        try:
            logger.info("修改产品版本状态 url: %s", url)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("修改产品版本状态 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"修改产品版本状态。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("修改产品版本状态！\n%s", traceback.format_exc())
            return False, {"message": f"修改产品版本状态。{traceback.format_exc()}"}

        return True, response

    def get_project_members(self, project_code):
        api = "project-local-api/project/member/public/search"

        url = BASE_URL.split('project-api/')[0] + api

        data = {
            # "projectId": project_id,
            "projectCode": project_code,
            "targetProductCode": "autotest",
        }

        try:
            logger.info("获取项目成员信息 url: %s", url)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("获取项目成员信息 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"获取项目成员信息。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("获取项目成员信息！\n%s", traceback.format_exc())
            return False, {"message": f"获取项目成员信息。{traceback.format_exc()}"}

        return True, response

    def get_test_types(self, token, project_number):
        api = "/project/task-test-case/public/test/manage/case/type/list"

        url = BASE_URL + api

        params = {
            "projectCode": project_number,
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("获取项目测试类型 url: %s", url)
            response = requests.post(url, json=params, headers=headers, timeout=TIMEOUT)
            logger.info("获取项目测试类型 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"获取项目测试类型。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("获取项目测试类型！\n%s", traceback.format_exc())
            return False, {"message": f"获取项目测试类型。{traceback.format_exc()}"}

        return True, response

    def patch_sync_test_case(self, token, project_number, category, user_email, test_cases):
        api = "/project/task-test-case/public/test/manage/batch/import"

        url = BASE_URL + api

        datas = []
        for test_case in test_cases:
            m1 = test_case.get("module")
            m2 = test_case.get("module_2level")
            m3 = test_case.get("module_3level")

            test_method = ",".join(test_case.get("test_method"))

            # steps = []
            # count = 0
            # for i in test_case.get("test_steps"):
            #     count += 1
            #
            #     if not i.get("desc").strip():
            #         return False, {
            #             "message": f"用例{test_case.get('name')}({test_case.get('number')}) 测试步骤描述不能为空！"
            #         }
            #
            #     steps.append({
            #         "stepDescription": i.get("desc"),
            #         "expectResult": i.get("expectation"),
            #         "sort": count,
            #     })

            steps = [{
                "stepDescription": test_case.get("steps"),
                "expectResult": test_case.get("expected"),
                "sort": 1,
            }]

            datas.append({
                "module": "-".join([i for i in [m1, m2, m3] if i]),
                "num": test_case.get("number"),
                "version": int(test_case.get("version")),
                "email": test_case.get("creator_email"),
                "remark": test_case.get("remark"),
                "data": {
                    "title": test_case.get("name"),
                    "categoryFormat": test_case.get("action_type"),
                    "type": test_case.get("type"),
                    "level": test_case.get("priority"),
                    "testGenerationMethod": test_case.get("generation_method"),
                    "testFunction": test_method,
                    "safetyAttribute": test_case.get("function_safe_attrib"),
                    "modeExecution": test_case.get("execute_mode"),
                    "testSource": test_case.get("source"),
                    "frontCondition": test_case.get("preconditions"),
                    "caseProcedureDetails": steps,
                    "requirementIds": [i.get("requirement_id") for i in test_case.get("requirements")],
                }

            })

        data = {
            "projectCode": project_number,
            "category": category,
            "email": user_email,
            "datas": datas,
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("批量同步和评审测试用例 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers, timeout=120)
            logger.info("批量同步和评审测试用例 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"批量同步和评审测试用例。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("批量同步和评审测试用例出错！\n%s", traceback.format_exc())
            return False, {"message": f"批量同步和评审测试用例出错。{traceback.format_exc()}"}

        return True, response

    def sync_test_plan(self, token, user_email, test_plan):
        api = "/project/test_case_plan/public/test/manage/import"

        url = BASE_URL + api

        pv = set()
        pv.add(test_plan.get("m_version"))
        for i in test_plan.get("product_version"):
            pv.add(i.get("label"))
        pv = list(pv)

        test_cases = []
        for tc in test_plan.get("test_cases"):
            test_cases.append({
                "num": tc.get("number"),
                "version": int(tc.get("version")),
            })

        data = {
            "creatorEmail": user_email,
            "handlerEmail": test_plan.get("pic_email"),
            "projectCode": test_plan.get("project_number"),
            "name": test_plan.get("name"),
            "type": test_plan.get("test_type"),
            "use": test_plan.get("plan_use"),
            "sample": test_plan.get("sample_info") if test_plan.get("sample_info") else "",
            "startTime": test_plan.get("p_start_time").strftime("%Y-%m-%d %H:%M:%S"),
            "endTime": test_plan.get("p_end_time").strftime("%Y-%m-%d %H:%M:%S"),
            "versionNameList": pv,
            "testCasesList": test_cases,
            "createTime": test_plan.get("create_time2").strftime("%Y-%m-%d %H:%M:%S:%f")[:-3],
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("同步测试计划 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers, timeout=120)
            logger.info("同步测试计划 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"同步测试计划。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("同步测试计划出错！\n%s", traceback.format_exc())
            return False, {"message": f"同步测试计划出错。{traceback.format_exc()}"}

        return True, response

    def sync_test_plan_result(self, token, test_plan):
        api = "/project/test_case_plan/public/test/manage/import/result"

        url = BASE_URL + api

        test_cases = []
        for tc in test_plan.get("test_cases"):

            d = {
                "num": tc.get("number"),
                "version": int(tc.get("version")),
            }

            status = tc.get("result", {}).get("result_two")
            value = tc.get("result", {}).get("value")
            remark = tc.get("result", {}).get("remark", "")
            if status is None:
                continue
            elif status == 1:
                status = "PASS"
            elif status == 2:
                status = "NA"
            elif status == 3:
                status = "NT"
            elif status == 0:
                status = "NG"
            else:
                continue
            d["state"] = status
            d["realResult"] = value
            d["remark"] = remark
            test_cases.append(d)

        data = {
            "email": test_plan.get("pic_email"),
            "projectCode": test_plan.get("project_number"),
            "name": test_plan.get("name"),
            "testCasesList": test_cases
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("同步测试计划结果 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers, timeout=TIMEOUT)
            logger.info("同步测试计划结果 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"同步测试计划结果。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("同步测试计划结果出错！\n%s", traceback.format_exc())
            return False, {"message": f"同步测试计划结果出错。{traceback.format_exc()}"}

        return True, response

    def get_requirement_list(self, token, type_list, project_id, page=1, pagesize=10, **kwargs):
        api = "project/requirement/public/search"

        url = BASE_URL + api

        headers = {
            "Authorization": f"Bearer {token}"
        }

        data = {
            "current": page,
            "size": pagesize,
            "typeList": type_list,
            "projectId": project_id,
        }

        name = kwargs.get("name")
        if name:
            data["name"] = name

        propertyList = kwargs.get("propertyList")
        if propertyList:
            data["propertyList"] = propertyList

        reviewStatusList = kwargs.get("reviewStatusList")
        if reviewStatusList:
            data["reviewStatusList"] = reviewStatusList

        multiReviewStatusList = kwargs.get("multiReviewStatusList")
        if multiReviewStatusList:
            data["multiReviewStatusList"] = multiReviewStatusList

        create_time = kwargs.get("create_time")
        if create_time:
            if len(create_time) >= 1:
                data["createStartTime"] = create_time[0].strftime("%Y-%m-%d %H:%M:%S")
            if len(create_time) >= 2:
                data["createEndTime"] = create_time[1].strftime("%Y-%m-%d %H:%M:%S")

        update_time = kwargs.get("update_time")
        if update_time:
            if len(update_time) >= 1:
                data["updateStartTime"] = update_time[0].strftime("%Y-%m-%d %H:%M:%S")
            if len(update_time) >= 2:
                data["updateEndTime"] = update_time[1].strftime("%Y-%m-%d %H:%M:%S")

        stateList = kwargs.get("stateList")
        if stateList:
            data["stateList"] = stateList

        version = kwargs.get("version")
        if version:
            data["version"] = version

        versionType = kwargs.get("versionType")
        if versionType:
            data["versionType"] = versionType

        try:
            logger.info("查询需求列表 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers, timeout=7)
            logger.info("查询需求列表 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"查询需求列表。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("查询需求列表出错！\n%s", traceback.format_exc())
            return False, {"message": f"查询需求列表出错。{traceback.format_exc()}"}

        return True, response

    def get_requirement_detail(self, token, requirement_id):
        api = f"project/requirement/public/detail/id"

        url = BASE_URL + api

        headers = {
            "Authorization": f"Bearer {token}"
        }

        data = {
            "id": requirement_id,
        }

        try:
            logger.info("查询需求详细信息 url: %s", url)
            response = requests.post(url, json=data, headers=headers, timeout=TIMEOUT)
            logger.info("查询需求详细信息 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"查询需求详细信息。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("查询需求详细信息！\n%s", traceback.format_exc())
            return False, {"message": f"查询需求详细信息。{traceback.format_exc()}"}

        return True, response

    def get_bug_list(self, token, project_number, test_case_number, page=1, pagesize=10, **kwargs):
        api = "project/task/public/queryTaskByTestManagerIds"

        url = BASE_URL + api

        headers = {
            "Authorization": f"Bearer {token}"
        }

        f, data = self.get_project_list_all(token)
        if not f:
            return f, data

        projects = data.get("data")

        project_id = None
        for project in projects:
            if project.get("projectCode") == project_number:
                project_id = project.get("id")
                break

        data = {
            # "current": page,
            # "size": pagesize,
            "projectId": project_id,
            "testManageNums": [test_case_number],
        }

        try:
            logger.info("查询问题列表 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers)
            logger.info("查询问题列表 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"查询问题列表。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("查询问题列表出错！\n%s", traceback.format_exc())
            return False, {"message": f"查询问题列表出错。{traceback.format_exc()}"}

        return True, response

    def add_bug_test_record(self, token, bug_id, task_version, task_scheme, task_conclusion):
        api = "project/task/test/record/public/saveData"

        url = BASE_URL + api

        headers = {
            "Authorization": f"Bearer {token}"
        }

        data = {
            "taskId": bug_id,
            "testVersion": task_version,
            "testScheme": task_scheme,
            "testConclusion": task_conclusion,
        }

        try:
            logger.info("添加问题测试记录 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers)
            logger.info("添加问题测试记录 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"添加问题测试记录。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("添加问题测试记录出错！\n%s", traceback.format_exc())
            return False, {"message": f"添加问题测试记录出错。{traceback.format_exc()}"}

        return True, response

    def get_requirements_by_case_numbers(self, token, project_id, category, test_case_numbers):
        api = "/project/task-test-case-detail/public/queryRequirementByTestManageNum"

        url = BASE_URL + api

        headers = {
            "Authorization": f"Bearer {token}"
        }

        cases = []
        for i in test_case_numbers:
            cases.append({
                "testManagerId": i,
                "version": None
            })

        data = {
            "cases": cases,
            "projectId": project_id,
            "category": category,
        }

        try:
            logger.info("查询用例关联需求 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers)
            logger.info("查询用例关联需求 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"查询用例关联需求。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("查询用例关联需求出错！\n%s", traceback.format_exc())
            return False, {"message": f"查询用例关联需求��错。{traceback.format_exc()}"}

        return True, response

    def access_stat(self, page_url, page_title, user_id):
        api = "/request/log/request/public/saveRecord"

        url = "https://ipd.hiway.com:10002/request-api" + api

        data = {
            "pageUrl": page_url,
            "pageTitle": page_title,
            "systemName": "autotest",
            "userId": user_id,
        }

        try:
            logger.info("访问统计 url: %s data: %s", url, data)
            response = requests.post(url, json=data)
            logger.info("访问统计 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"访问统计。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("访问统计出错！\n%s", traceback.format_exc())
            return False, {"message": f"访问统计出错。{traceback.format_exc()}"}

        return True, response

    def get_project_issues(self, token, project_code, status_list, current=1, size=1000):
        api = "project/issue/public/search"

        url = BASE_URL + api

        data = {
            "projectCode": project_code,
            "statusList": status_list,
            "current": current,
            "size": size
        }
        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("获取项目问题列表 url: %s data: %s", url, data)
            # print(f"获取项目问题列表 url={url} data={data}")
            response = requests.post(url, json=data, headers=headers)
            logger.info("获取项目问题列表 response: %s", response.text)
            # print(f"获取项目问题列表 response={response.text}")
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"获取项目问题列表。请求的返回状态码：{response.status_code}。"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("获取项目问题列表出错！\n%s", traceback.format_exc())
            return False, {"message": f"获取项目问题列表出错。{traceback.format_exc()}"}

        return True, response

    def push_related_versions(self, token, project_code, test_manage_id, test_product):
        api = "/project/project-software-publish-notify/public/sync"

        url = BASE_URL + api

        version_list = []

        for com in test_product.get("components", []):
            if not com.get("hardware_versions"):
                continue
            h_ver = com.get("hardware_versions")[0]
            s_ver = com.get("software_versions")

            vd = []
            for sv in s_ver:
                vd.append({
                    "mainVersionType": sv.get("type"),
                    "mainVersionPackage": sv.get("packageUrl"),
                    "mainVersionNo":sv.get("num"),
                    "subVersionInfo": sv.get("subVersionInfo"),
                    "update": 1,
                })

            ver = {
                "no": h_ver.get("num"),
                "pcbaCode": h_ver.get("pcbaCode"),
                "versionData": vd
            }

            version_list.append(ver)


        data = {
            "projectCode": project_code,
            "testManageId": test_manage_id,
            "hardwareVersionData": version_list,
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            logger.info("关联版本 url: %s data: %s", url, data)
            response = requests.post(url, json=data, headers=headers, timeout=TIMEOUT)
            logger.info("关联版本 response: %s", response.text)
            if response.status_code == 401:
                return False, {"message": 401}
            if response.status_code != 200:
                return False, {"message": f"关联版本。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 20000:
                return False, response
        except Exception:
            logger.error("关联版本出错！\n%s", traceback.format_exc())
            return False, {"message": f"关联版本出错。{traceback.format_exc()}"}

        return True, response