import traceback
import logging
import datetime
from io import BytesIO
import urllib.parse

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.http import HttpResponse
from openpyxl import Workbook
from openpyxl.utils import get_column_letter

from .serializers import (
    CreateInspecItemSerializer, GetInspecItemSerializer, UpdateInspecItemSerializer,
    CreateInspecPersonSerializer, GetInspecPersonSerializer, UpdateInspecPersonSerializer,
    GetInspecRecordSerializer, UpdateInspecRecordSerializer,
    DispatchTaskSerializer, RecordsDownloadSerializer,
)
from .models import InspecModel, InspecTime, InspecRecord

logger = logging.getLogger("inspecs")


class InspecItemsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = GetInspecItemSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = InspecModel()
            content = model.get_items(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = CreateInspecItemSerializer(data=request.data)
            print(request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = InspecModel()
            model.create_item(
                **serializer.validated_data
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class InspecItemDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = InspecModel()
            result = model.get_item(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:

            serializer = UpdateInspecItemSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = InspecModel()
            model.update_item(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            model = InspecModel()
            model.delete_item(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class InspecPersonsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = GetInspecPersonSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = InspecModel()
            content = model.get_persons(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = CreateInspecPersonSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = InspecModel()
            model.create_person(
                **serializer.validated_data
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class InspecPersonDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = InspecModel()
            result = model.get_person(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:

            serializer = UpdateInspecPersonSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = InspecModel()
            model.update_person(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            model = InspecModel()
            model.delete_person(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class InspecRecordsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = GetInspecRecordSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = InspecModel()
            content = model.get_records(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class InspecRecordDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = InspecModel()
            result = model.get_record(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:

            serializer = UpdateInspecRecordSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user

            model = InspecModel()

            record = model.get_record(pk=pk)

            if not record:
                return Response({"err_code": 1, "msg": "记录不存在。"}, status.HTTP_404_NOT_FOUND)
            if record.get("person_email") != user.email:
                return Response({"err_code": 1, "msg": "无权修改该记录。"}, status.HTTP_403_FORBIDDEN)
            t = datetime.datetime.now()
            if t.day != record.get("time_date").day or t.month != record.get("time_date").month or t.year != record.get(
                    "time_date").year:
                return Response({"err_code": 1, "msg": "只能修改当天的记录。"}, status.HTTP_403_FORBIDDEN)
            if t.hour < 17:
                return Response({"err_code": 1, "msg": "只能在17点后修改记录。"}, status.HTTP_403_FORBIDDEN)

            model.update_record(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DispatchTaskView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = DispatchTaskSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            person_id = serializer.validated_data.get("person_id")

            model = InspecModel()

            today = datetime.datetime.now()

            inspec_time_r = InspecTime.objects.filter(
                date=today,
            ).first()
            if inspec_time_r:
                if InspecRecord.objects.filter(time_id=inspec_time_r.id).exists():
                    return Response({"err_code": 1, "msg": "今天的点检任务已分配。"}, status.HTTP_400_BAD_REQUEST)
            else:
                inspec_time_r = InspecTime.objects.create(date=today)

            model.dispatch_task(person_id, inspec_time_r.id)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class RecordsDownloadView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = RecordsDownloadSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            wb = Workbook()

            ws = wb.active

            model = InspecModel()
            records = model.get_records2(**serializer.validated_data)

            date_d = {}
            for record in records:
                if date_d.get(record["time_date"]) is None:
                    date_d[record["time_date"]] = {}
                date_d[record["time_date"]][record["item_name"]] = record

            for index1, i in enumerate(date_d):
                ws.cell(index1 + 2, 1, i)

                for index2, j in enumerate(date_d[i]):
                    if index1 == 0:
                        ws.cell(1, index2 + 2, j)
                        if index2 == 0:
                            ws.cell(1, len(date_d[i]) + 2, "轮班人员")
                    if index2 == 0:
                        ws.cell(index1 + 2, len(date_d[i]) + 2, date_d[i][j]["person_name"])

                    value = date_d[i][j]["is_pass"]
                    if value is None:
                        value = ""
                    elif value:
                        value = "通过"
                    else:
                        value = "不通过"

                    ws.cell(index1 + 2, index2 + 2, value)

            for col in ws.iter_cols(min_col=1, max_col=ws.max_column):
                col_letter = get_column_letter(col[0].column)

                ws.column_dimensions[col_letter].width = 20

            output = BytesIO()
            wb.save(output)
            output.seek(0)

            response = HttpResponse(
                output,
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
            filename = "点检记录.xlsx"
            filename = urllib.parse.quote(filename)
            response['Content-Disposition'] = f'attachment; filename={filename}'

            return response

        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
