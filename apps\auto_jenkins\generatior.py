
import logging
import os
from typing import Dict, Any, List
from jinja2 import Environment, FileSystemLoader, Template

logger = logging.getLogger("auto_jenkins")


class JenkinsfileGenerator:
    """Jenkinsfile生成器"""
    
    def __init__(self):
        # 设置模板目录
        self.template_dir = os.path.join(os.path.dirname(__file__), 'templates')
        self.env = Environment(
            loader=FileSystemLoader(self.template_dir),
            trim_blocks=True,
            lstrip_blocks=True
        )
        # 添加自定义过滤器
        self.env.filters['quote'] = lambda x: f"'{x}'"
    
    def generate(self, pipeline_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成Jenkinsfile
        
        Args:
            pipeline_config: Pipeline配置
            
        Returns:
            生成结果
        """
        try:
            # 验证配置
            validation_errors = self._validate_config(pipeline_config)
            if validation_errors:
                return {
                    'success': False,
                    'errors': validation_errors,
                    'jenkinsfile': None
                }

            # 生成Jenkinsfile内容
            logger.info("开始生成Jenkinsfile")
            jenkinsfile_content = self._build_jenkinsfile(pipeline_config)
            logger.info("Jenkinsfile生成成功")
            
            return {
                'success': True,
                'jenkinsfile': jenkinsfile_content,
                'syntax_errors': [],
                'metadata': {
                    'pipeline_name': pipeline_config.get('name', 'Unknown'),
                    'stage_count': len(pipeline_config.get('stages', [])),
                    'line_count': len(jenkinsfile_content.split('\n'))
                }
            }
            
        except Exception as e:
            logger.error(f"生成Jenkinsfile失败: {str(e)}", exc_info=True)
            return {
                'success': False,
                'errors': [f"生成失败: {str(e)}"],
                'jenkinsfile': None
            }
    
    def _validate_config(self, config: Dict[str, Any]) -> List[str]:
        """验证Pipeline配置"""
        errors = []
        # 检查必需字段
        if 'stages' not in config:
            errors.append("配置缺少stages字段")
        
        stages = config.get('stages', [])
        if not isinstance(stages, list):
            errors.append("stages字段必须是数组")
        elif len(stages) == 0:
            errors.append("至少需要一个stage")
        # 验证每个stage
        for i, stage in enumerate(stages):
            if not isinstance(stage, dict):
                errors.append(f"Stage {i} 必须是对象")
                continue
            
            if 'name' not in stage:
                errors.append(f"Stage {i} 缺少name字段")

        
        return errors
    
    def _build_jenkinsfile(self, config: Dict[str, Any]) -> str:
        """构建Jenkinsfile内容"""
        try:
            template = self.env.get_template('jenkinsfile.j2')
            
            # 处理stages中的base64脚本
            stages = config.get('stages', [])
            processed_stages = []
            
            for stage in stages:
                processed_stage = stage.copy()
                
                # 如果有script_content且是base64编码的raw_script
                if (stage.get('script_type') == 'raw_script' and stage.get('script_content')):
                    # 直接使用前端传来的base64内容
                    processed_stage['script_b64'] = stage['script_content']
                processed_stages.append(processed_stage)
            
            # 准备模板变量
            logger.info(f"{config.get('task_id')}")
            template_vars = {
                'agent': config.get('agent', 'any'),
                'parameters': config.get('parameters', []),
                'environment': config.get('environment', {}),
                'stages': processed_stages,  # 使用处理后的stages
                'post_actions': config.get('post_actions', {}),
                'git_url': config.get('git_url'),
                'branch': config.get('branch', 'master'),
                'engineering_path': config.get('engineering_path'),
                'task_id': config.get('task_id', 'test'),
                'django_base_url': config.get('django_base_url', 'http://**********:9000'),
                'enable_git_tag': config.get('enable_git_tag', True)  # 默认启用Git Tag
            }
            logger.info(f"{template_vars['task_id']=}")
            template_vars['credentials_id'] = (config.get('credentials_id') or '').strip()
            logger.info(f"{template_vars['credentials_id']=}")
            # 渲染模板
            jenkinsfile_content = template.render(**template_vars)
            return jenkinsfile_content
            
        except Exception as e:
            logger.error(f"模板渲染失败: {str(e)}", exc_info=True)
            # 如果模板渲染失败，回退到原始方法
            return self._build_jenkinsfile_fallback(config)
    
    def _build_jenkinsfile_fallback(self, config: Dict[str, Any]) -> str:
        """构建Jenkinsfile内容 - 回退方法"""
        lines = []
        
        # Pipeline开头
        lines.append("pipeline {")
        
        # Agent配置
        agent = config.get('agent', 'any')
        if agent == 'any':
            lines.append("    agent any")
        else:
            lines.append(f"    agent {{ label '{agent}' }}")
        
        # 参数配置
        parameters = config.get('parameters', [])
        if parameters:
            lines.append("")
            lines.append("    parameters {")
            for param in parameters:
                param_type = param.get('type', 'string')
                param_name = param.get('name', '')
                param_default = param.get('default_value', '')
                param_desc = param.get('description', '')
                
                if param_type == 'string':
                    lines.append(f"        string(name: '{param_name}', defaultValue: '{param_default}', description: '{param_desc}')")
                elif param_type == 'choice':
                    choices = param.get('choices', [])
                    choices_str = ', '.join([f"'{c}'" for c in choices])
                    lines.append(f"        choice(name: '{param_name}', choices: [{choices_str}], description: '{param_desc}')")
                elif param_type == 'boolean':
                    default_bool = str(param_default).lower()
                    lines.append(f"        booleanParam(name: '{param_name}', defaultValue: {default_bool}, description: '{param_desc}')")
            lines.append("    }")
        
        # 环境变量
        environment = config.get('environment', {})
        if environment:
            lines.append("")
            lines.append("    environment {")
            for key, value in environment.items():
                lines.append(f"        {key} = '{value}'")
            lines.append("    }")
        
        # Stages
        lines.append("")
        lines.append("    stages {")
        
        stages = config.get('stages', [])
        for stage in stages:
            stage_name = stage.get('name', 'Unknown')
            
            lines.append(f"        stage('{stage_name}') {{")
            lines.append("            steps {")
            
            # 动态处理stage内容
            if 'script_block' in stage:
                # 脚本块模式
                lines.append("                script {")
                lines.append(f"                    echo '执行Stage: {stage_name}'")
                script_lines = stage['script_block'].strip().split('\n')
                for script_line in script_lines:
                    lines.append(f"                    {script_line}")
                lines.append("                }")
            elif 'commands' in stage:
                # 命令列表模式
                commands = stage['commands']
                for cmd in commands:
                    if isinstance(cmd, dict):
                        # 对象形式的命令
                        cmd_type = cmd.get('type', 'bat')
                        cmd_content = cmd.get('content', '')
                        cmd_desc = cmd.get('description', '')
                        
                        if cmd_type == 'script':
                            lines.append("                script {")
                            if cmd_desc:
                                lines.append(f"                    echo '{cmd_desc}'")
                            script_lines = cmd_content.strip().split('\n')
                            for script_line in script_lines:
                                lines.append(f"                    {script_line}")
                            lines.append("                }")
                        else:  # 默认为bat
                            lines.append("                bat '''")
                            lines.append("                    cd /d %WORKSPACE%")
                            lines.append(f"                    {cmd_content}")
                            lines.append("                '''")
                    else:
                        # 字符串形式的命令（向后兼容）
                        lines.append("                bat '''")
                        lines.append("                    cd /d %WORKSPACE%")
                        lines.append(f"                    {cmd}")
                        lines.append("                '''")
            else:
                # 默认处理
                lines.append(f"                echo 'Stage: {stage_name}'")
            
            lines.append("            }")
            lines.append("        }")
        
        lines.append("    }")
        
        # Post actions
        post_actions = config.get('post_actions', {})
        if post_actions:
            lines.append("")
            lines.append("    post {")
            
            if 'always' in post_actions:
                lines.append("        always {")
                for action in post_actions['always']:
                    lines.append(f"            {action}")
                lines.append("        }")
            
            if 'success' in post_actions:
                lines.append("        success {")
                for action in post_actions['success']:
                    lines.append(f"            {action}")
                lines.append("        }")
            
            if 'failure' in post_actions:
                lines.append("        failure {")
                for action in post_actions['failure']:
                    lines.append(f"            {action}")
                lines.append("        }")
            
            lines.append("    }")
        
        # Pipeline结尾
        lines.append("}")
        
        return '\n'.join(lines)
    