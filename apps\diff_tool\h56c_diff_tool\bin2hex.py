from array import array
import os, sys, zipfile
import logging
import subprocess

logger = logging.getLogger("diff_tool")

BASE_DIR = os.path.dirname(os.path.abspath(__file__))


# bin文件转换为hex文件
def convert_bin_to_hex(bin_file_path: str, hex_file_path: str, start_address: int):
    """
    将二进制文件转换为十六进制文件。

    Parameters:
        bin_file_path (str): 要转换的二进制文件的路径。
        hex_file_path (str): 要创建的十六进制文件的路径。

    Returns:
        bool: 如果转换成功，则为True；否则为False。
    """
    try:
        bin_file = open(bin_file_path, "rb")
        hex_file = open(hex_file_path, "w+")
        stats = os.stat(bin_file_path)
    except:
        logger.error("无法打开文件...")
        return False, "无法打开文件"

    # 当前的偏移量
    current_offset = start_address
    # 文件长度的最大偏移量
    max_offset = stats.st_size + current_offset
    read_length = 0x20
    # 文件没有转化完毕
    while current_offset < max_offset:

        write_data = array("B", ("\0" * 7).encode("ascii"))
        write_data[0] = 0x02
        write_data[1] = 0x00
        write_data[2] = 0x00
        write_data[3] = 0x04
        write_data[4] = (current_offset >> 24) & 0xFF
        write_data[5] = (current_offset >> 16) & 0xFF
        write_data[6] = (-sum(write_data)) & 0xFF
        hex_file.write(":" + array.tobytes(write_data).hex().upper() + "\n")

        while True:
            offset = current_offset & 0xFFFF
            data = bin_file.read(read_length)
            if not data:
                break
            write_data = array("B", ("\0" * (5 + len(data))).encode("ascii"))
            write_data[0] = len(data)
            write_data[1] = (offset >> 8) & 0xFF
            write_data[2] = offset & 0xFF
            write_data[3] = 0x00
            for i in range(0, len(data)):
                write_data[4 + i] = data[i]
            write_data[-1] = (-sum(write_data)) & 0xFF
            hex_file.write(":" + array.tobytes(write_data).hex().upper() + "\n")
            current_offset += len(data)
            if offset == 0x10000 - read_length:
                break
    hex_file.write(":00000001FF\n")

    return True, "成功"


# hex文件转换为bin文件
def convert_hex_to_bin(hex_file_path: str, bin_file_path: str):
    """
    将十六进制文件转换为二进制文件。

    Args:
        hex_file_path (str): 十六进制文件的路径。
        bin_file_path (str): 转换后的二进制文件的路径。

    Returns:
        bool: 转换是否成功。
    """
    try:
        hex_file = open(hex_file_path, "r")
        bin_file = open(bin_file_path, "w+b")
    except:
        return False, "无法打开文件"

    line = int(0)
    offset = int(0)
    buffer = dict()
    for data in hex_file.readlines():
        line += 1
        if data[0] == ":":
            data = "".join(filter(str.isalnum, data))
            read_data = array("B", bytes.fromhex(data[0:]))

            # 长度检查
            record_length = read_data[0]
            if len(read_data) != record_length + 5:
                # print("Record at line %d has invalid length" % line)
                return False, "%d行数据的长度错误" % line

            addr = (read_data[1] << 8) + read_data[2]

            # 校验和检查
            if sum(read_data) & 0xFF != 0x00:
                # print("Record at line %d has invalid checksum" % line)
                return False, "%d行数据的校验和错误" % line

            # 类型检查
            record_type = read_data[3]
            if not (0 <= record_type <= 5):
                # print("Record at line %d has invalid type" % line)
                return False, "%d行数据的类型错误" % line

            # 数据记录
            if record_type == 0:
                addr += offset
                for i in range(4, 4 + record_length):
                    if addr in buffer:
                        # print("Record at line %d has duplicate address" % line)
                        return False, "%d行数据的地址重复" % line
                    buffer[addr] = read_data[i]
                    addr += 1

            # 文件结尾记录
            elif record_type == 1:
                if record_length != 0:
                    # print("File has invalid End-of-File record")
                    return False, "文件有无效的结束记录"

            # 扩展段地址记录
            elif record_type == 2:
                if record_length != 2 or addr != 0:
                    # print("Invalid Extended Segment Address Record at line %d" % line)
                    return False, "%d行数据的扩展段地址记录无效" % line
                offset = ((read_data[4] << 8) + read_data[5]) << 4

            # 扩展线性地址记录
            elif record_type == 4:
                if record_length != 2 or addr != 0:
                    # print("Invalid Extended Linear Address Record at line %d" % line)
                    return False, "%d行数据的扩展线性地址记录无效" % line
                offset = ((read_data[4] << 8) + read_data[5]) << 16

            # 开始段地址记录
            elif record_type == 3:
                print("Record type 3 is not supported")

            # 开始线性地址记录
            elif record_type == 5:
                print("Record type 5 is not supported")
        else:
            print("Hex file contains invalid record at line %d" % line)
            return False, "%d行包含无效记录" % line

    if buffer != {}:
        start = min(buffer.keys())
        end = max(buffer.keys())
        if start > end:
            start, end = end, start
        for i in range(start, end + 1):
            bin_file.write(buffer[i].to_bytes(1, byteorder="big"))

    return True, "成功"


# 解压缩文件
def unzip_file(zip_file_path: str, extract_file_path: str):
    """
    解压缩文件。

    Args:
        zip_file_path (str): 要解压缩的文件的路径。

    Returns:
        bool: 解压缩是否成功。
    """
    try:
        with zipfile.ZipFile(zip_file_path, "r") as zip_ref:
            zip_ref.extractall(extract_file_path)
    except Exception as e:
        print("unzip error:", e)
        return False

    return True


# 差分bin文件
def diff_bin_file(old_file: str, new_file: str, output_file: str, version: str,
                  temp_file_path: str) -> bool:
    logger.info("diff bin file... %s, %s, %s, %s", old_file, new_file, output_file, version)
    # 形成hdiffz的命令
    temp_output_file = os.path.join(temp_file_path, "temp_output.bin")
    cmd = ""
    if os.name == "nt":
        # windows
        command_file_path = os.path.join(BASE_DIR, "win", "hdiffz.exe")
        cmd = (
                command_file_path
                + " -f -c-zlib "
                + old_file
                + " "
                + new_file
                + " "
                + temp_output_file
        )
    elif os.name == "posix":
        # linux
        command_file_path = os.path.join(BASE_DIR, "linux", "hdiffz")
        cmd = (
                command_file_path
                + " -f -c-zlib "
                + old_file
                + " "
                + new_file
                + " "
                + temp_output_file
        )
    else:
        logger.error("os error: os must be windows or linux")
        return False
    logger.info("excute: %s" + cmd)

    try:
        # 执行命令
        result = subprocess.run(cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        # 检查返回码
        if result.returncode == 0:
            if version != "":
                logger.info("version: %s", version)
                try:
                    hex_file = open(temp_output_file, "a+")
                except:
                    logger.error("hdiffz tool error...")
                    sys.exit(1)
                hex_file.write(version)
                hex_file.close()
            # 对文件进行转化
            convert_bin_to_hex(temp_output_file, output_file, 0xC1000)
        else:
            logger.error("Command failed with return code: %s", result.returncode)
            logger.error("Error: %s", result.stderr)
            return False
    except subprocess.CalledProcessError as e:
        logger.error("Command execution failed with error: %s", e)
        return False

    return True


# 差分hex文件
def diff_hex_file(old_hex_file: str, new_hex_file: str, output_file: str, version: str, temp_file_path: str):
    logger.info("diff hex file...", old_hex_file, new_hex_file, output_file, version)
    # 旧文件转换为bin文件
    temp_file = os.path.join(temp_file_path, "temp_old.bin")
    logger.info("temp_file:", temp_file)
    result, error = convert_hex_to_bin(old_hex_file, temp_file)
    if not result:
        logger.error("error:", error)
        return False
    old_file = temp_file

    # 新文件转换为bin文件
    temp_file = os.path.join(temp_file_path, "temp_new.bin")
    logger.info("temp_file:", temp_file)
    result, error = convert_hex_to_bin(new_hex_file, temp_file)
    if not result:
        logger.error("error:", error)
        return False
    new_file = temp_file

    return diff_bin_file(old_file, new_file, output_file, version, temp_file_path)


# 差分zip文件
def diff_zip_file(
        old_zip_file: str, new_zip_file: str, output_file: str, version: str, temp_file_path: str
) -> bool:
    # 解压文件
    unzip_file_path_old = os.path.join(temp_file_path, "old_file")
    unzip_file(old_zip_file, unzip_file_path_old)
    unzip_file_path_new = os.path.join(temp_file_path, "new_file")
    unzip_file(new_zip_file, unzip_file_path_new)

    # 查看解压后的文件
    list_old_file = []
    for root, dirs, files in os.walk(unzip_file_path_old):
        for file in files:
            list_old_file.append(file)
    list_new_file = []
    for root, dirs, files in os.walk(unzip_file_path_new):
        for file in files:
            list_new_file.append(file)

    # 寻找需要差分的文件
    diff_old_file_path = ""
    for i in range(len(list_old_file)):
        if list_old_file[i].find("_04") != -1:
            diff_old_file_path = os.path.join(unzip_file_path_old, list_old_file[i])
            break
    diff_new_file_path = ""
    for i in range(len(list_new_file)):
        if list_new_file[i].find("_04") != -1:
            diff_new_file_path = os.path.join(unzip_file_path_new, list_new_file[i])
            break

    # 检查是否找到需要差分的文件
    if diff_old_file_path == "" or diff_new_file_path == "":
        logger.error("diff file not found...")
        return False

    # 差分临时的输出路径
    diff_output_file_path = os.path.join(temp_file_path, "diff_output_file.hex")

    # 检查需要差分的内容是否是hex文件
    if not diff_old_file_path.endswith(".hex") or not diff_new_file_path.endswith(
            ".hex"
    ):
        logger.error("the unzip file must be hex file...")
        return False

    # 执行差分
    if not diff_hex_file(
            diff_old_file_path, diff_new_file_path, diff_output_file_path, version, temp_file_path
    ):
        return False

    # 将差分后的产物和新的内容进行打包
    logger.info("start_pack")
    with zipfile.ZipFile(output_file, "w") as destination_file:
        for file in list_new_file:
            # 其他文件直接复制
            if file.find("_04") == -1:

                destination_file.write(
                    os.path.join(unzip_file_path_new, file), os.path.basename(file)
                )
            else:
                destination_file.write(diff_output_file_path, os.path.basename(file))

    return True
