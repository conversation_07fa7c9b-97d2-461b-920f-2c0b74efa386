--测试任务表
CREATE TABLE IF NOT EXISTS public.test_tasks(
    id serial primary key, --id
    "name" varchar(255),
    test_plan_id int,
    creator_email varchar(255),
    creator_name varchar(255),
    creator_employee_number varchar(255),
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.test_tasks OWNER TO atpms;


--测试记录表
CREATE TABLE IF NOT EXISTS public.test_records(
    id varchar(255) primary key, --id
    task_id int,
    project_number varchar(255),
    project_name varchar(255),
    test_plan_name varchar(255),
    test_plan_id int,
    test_sub_plan_name varchar(255),
    test_sub_plan_id int,
    tester_email varchar(255),
    tester_name varchar(255),
    tester_employee_number varchar(255),
    machine_number varchar(255),
    progress varchar(255),
    status int default 0, -- 状态  0 测试中 1 测试结束
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.test_records OWNER TO atpms;

--测试记录项表
CREATE TABLE IF NOT EXISTS public.test_record_items(
    id serial primary key, --id
    task_id int,
    test_record_id varchar(255),
    project_number varchar(255),
    project_name varchar(255),
    test_plan_name varchar(255),
    test_plan_id int,
    test_sub_plan_name varchar(255),
    test_sub_plan_id int,
    test_case_number varchar(255),
    test_case_name varchar(255),
    test_case_id int,
    test_case_version int,
    test_time timestamp ,
    end_time timestamp ,
    tester_email varchar(255),
    tester_name varchar(255),
    tester_employee_number varchar(255),
    machine_number varchar(255),
    result bool default null,
    status int default 0, -- 状态  0 未处理 1 已处理
    raw text,  -- 原始值
    is_false_alarm bool default false,
    false_alarm_reasons text,
    is_manual_determinated bool default false,
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.test_record_items OWNER TO atpms;

--测试记录项关联外部资源
CREATE TABLE IF NOT EXISTS public.test_record_item_resources(
    id serial primary key, --id
    test_record_item_id int,
    resource_name varchar(255),
    resource_path varchar(255),
    create_time timestamp --创建时间
);
ALTER TABLE public.test_record_item_resources OWNER TO atpms;