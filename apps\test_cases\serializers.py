from rest_framework import serializers


class TestCaseListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    project_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    action_type = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    module_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    module_2level_list = serializers.ListField(child=serializers.Char<PERSON>ield(max_length=255), min_length=0,
                                               required=False)
    module_3level_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0,
                                               required=False)
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    number = serializers.Char<PERSON>ield(max_length=255, required=False, allow_blank=True, allow_null=True)
    status = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    status_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    priority_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    execute_mode_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    search_str = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    is_durable = serializers.BooleanField(required=False, allow_null=True, default=None)
    creator_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    product_type_id = serializers.IntegerField(required=False)
    product_type_id_list = serializers.ListField(child=serializers.IntegerField(), required=False)
    order = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    creator_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    # 增加用例类型
    type = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)


class TestStepSerializer(serializers.Serializer):
    type = serializers.CharField(max_length=255)
    params = serializers.CharField()
    desc = serializers.CharField()
    expectation = serializers.CharField()


class TestCaseSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    module = serializers.CharField(max_length=255)
    type = serializers.CharField(max_length=255)
    action_type = serializers.CharField(max_length=255)
    priority = serializers.CharField(max_length=255)
    related_requirements = serializers.BooleanField()
    source = serializers.CharField(max_length=255)
    generation_method = serializers.CharField(max_length=255)
    test_method = serializers.ListField(child=serializers.CharField(max_length=255), min_length=1)
    execute_mode = serializers.CharField(max_length=255)
    function_safe_attrib = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    preconditions = serializers.CharField()
    steps = serializers.CharField()
    expected = serializers.CharField()
    remark = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    cycle = serializers.IntegerField(min_value=0)
    project_name = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255)
    project_id = serializers.CharField(max_length=255)
    test_steps = serializers.ListField(child=TestStepSerializer(), min_length=0)
    tag = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    es_source = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    es_id = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    product_type_id = serializers.IntegerField()
    public_test_case_id = serializers.IntegerField(required=False, allow_null=True)
    requirement_id = serializers.IntegerField(required=False, allow_null=True)
    requirement_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    requirement_version = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)

    def validate_module(self, value):
        module_list = value.split(", ")
        for module in module_list:
            if module.strip() == '':
                raise serializers.ValidationError('module不能为空')

        return value


class TestCaseUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    type = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    priority = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    related_requirements = serializers.BooleanField()
    source = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    generation_method = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    test_method = serializers.ListField(child=serializers.CharField(max_length=255), min_length=1, required=False)
    execute_mode = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    function_safe_attrib = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    preconditions = serializers.CharField()
    steps = serializers.CharField()
    expected = serializers.CharField()
    remark = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    cycle = serializers.IntegerField(min_value=0, required=False)
    vision_revert = serializers.BooleanField(required=False)
    vision_algorithm = serializers.BooleanField(required=False)
    test_steps = serializers.ListField(child=TestStepSerializer(), min_length=0)
    tag = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    es_source = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    es_id = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    product_type_id = serializers.IntegerField()


class TestCaseUpdateStepsSerializer(serializers.Serializer):
    test_steps = serializers.ListField(child=TestStepSerializer(), min_length=0)


class TestCaseArchiveListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    test_case_id = serializers.IntegerField(required=False)


class TestCaseVersionSerializer(serializers.Serializer):
    projectCode = serializers.CharField(max_length=255)
    num = serializers.CharField(max_length=255)
    version = serializers.IntegerField()
    reviewStatus = serializers.CharField(max_length=255)


class TestCaseVersionUpdateSerializer(serializers.Serializer):
    datas = serializers.ListField(child=TestCaseVersionSerializer(), min_length=1)


class TestCaseMoveSerializer(serializers.Serializer):
    module = serializers.CharField(max_length=255)
    action_type = serializers.CharField(max_length=255)


class PublicTestCaseListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    search_str = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    name_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    execute_mode_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    action_type = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    module_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    module_2level_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0,
                                               required=False)
    module_3level_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0,
                                               required=False)
    priority_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    related_requirements = serializers.BooleanField()
    action_type_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    tags = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    es_source = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class PublicTestCaseSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    module = serializers.CharField(max_length=255)
    type = serializers.CharField(max_length=255)
    action_type = serializers.CharField(max_length=255)
    priority = serializers.CharField(max_length=255)
    related_requirements = serializers.BooleanField()
    source = serializers.CharField(max_length=255)
    generation_method = serializers.CharField(max_length=255)
    test_method = serializers.ListField(child=serializers.CharField(max_length=255), min_length=1)
    execute_mode = serializers.CharField(max_length=255)
    function_safe_attrib = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    preconditions = serializers.CharField()
    steps = serializers.CharField()
    expected = serializers.CharField()
    remark = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    cycle = serializers.IntegerField(min_value=0)
    test_steps = serializers.ListField(child=TestStepSerializer(), min_length=0)
    tag = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    es_source = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    es_id = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class PublicTestCaseUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    module = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    type = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    action_type = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    priority = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    related_requirements = serializers.BooleanField()
    source = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    generation_method = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    test_method = serializers.ListField(child=serializers.CharField(max_length=255), min_length=1, required=False)
    execute_mode = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    function_safe_attrib = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    preconditions = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    steps = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    expected = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    remark = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    cycle = serializers.IntegerField(min_value=0, required=False)
    test_steps = serializers.ListField(child=TestStepSerializer(), min_length=0)
    project_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    tag = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    es_source = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    es_id = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class TestCasePatchSyncSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)
    category = serializers.CharField(max_length=255)
    ids = serializers.ListField(child=serializers.IntegerField(), min_length=1)


class TestCasePatchImportSerializer(serializers.Serializer):
    project_name = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255)
    project_id = serializers.CharField(max_length=255)
    product_type_id = serializers.IntegerField()
    test_case_ids = serializers.ListField(child=serializers.IntegerField(), min_length=1)


class ProjectTestCaseCopySerializer(serializers.Serializer):
    source_project_number = serializers.CharField(max_length=255)
    target_project_name = serializers.CharField(max_length=255)
    target_project_number = serializers.CharField(max_length=255)
    target_project_id = serializers.CharField(max_length=255)

    def validate(self, attrs):
        if attrs['source_project_number'] == attrs['target_project_number']:
            raise serializers.ValidationError('源项目和目标项目不能相同')
        return attrs


class RequirementSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    version = serializers.CharField()
    number = serializers.CharField()


class TestCaseRelateRequirementsSerializer(serializers.Serializer):
    test_case_id = serializers.CharField()
    test_case_version = serializers.CharField()
    requirements = serializers.ListField(child=RequirementSerializer(), min_length=1)

