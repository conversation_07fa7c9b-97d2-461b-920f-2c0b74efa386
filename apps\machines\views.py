import logging
import traceback
import datetime

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuth<PERSON>icated, IsAdminUser
from rest_framework_simplejwt.authentication import JWTAuthentication

from .serializers import (
    DeviceTypeSerializer, DeviceTypeListSerializer, DeviceTypeUpdateSerializer,
    DeviceSerializer, DeviceListSerializer, DeviceUpdateSerializer,
    FunctionSerializer, FunctionListSerializer, FunctionUpdateSerializer,
    MachineSerializer, MachineListSerializer, MachineUpdateSerializer,
    MachineReservationSerializer, MachineReservationListSerializer, MachineReservationUpdateSerializer,
    MachineUsageRecordListSerializer,
    MachineReservationTimelineSerializer, MachineReservationCheckinSerializer,
    MachineDetailByMNumberSerializer,
)
from .models import (
    DeviceModel, FunctionModel, MachineModel, MachineReservationModel, MachineUsageRecordModel,
    DeviceTypeModel, get_machine_reservation_timeline
)
from users.models import User
from utils.fs_app import fs_app

logger = logging.getLogger("machine")


class DeviceTypesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = DeviceTypeListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DeviceTypeModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = DeviceTypeSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DeviceTypeModel()
            model.create(**serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DeviceTypeDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = DeviceTypeModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = DeviceTypeUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DeviceTypeModel()
            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            model = DeviceTypeModel()
            model.delete(pk=pk)
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DevicesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = DeviceListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DeviceModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = DeviceSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DeviceModel()
            model.create(**serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DeviceDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = DeviceModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = DeviceUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DeviceModel()
            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            model = DeviceModel()
            model.delete(pk=pk)
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class FunctionsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = FunctionListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = FunctionModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = FunctionSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = FunctionModel()
            model.create(**serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class FunctionDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = FunctionModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = FunctionUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = FunctionModel()
            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            model = FunctionModel()
            model.delete(pk=pk)
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class MachinesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = MachineListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = MachineModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = MachineSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = MachineModel()
            model.create(**serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class MachineDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = MachineModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = MachineUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = MachineModel()
            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            model = MachineModel()
            model.delete(pk=pk)
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class MachineDetailByMNumberView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            serializer = MachineDetailByMNumberSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            m_number = serializer.validated_data.get("number")

            model = MachineModel()
            result = model.retrieve_by_m_number(m_number)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class MachineReservationsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = MachineReservationListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = MachineReservationModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = MachineReservationSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            kwargs = dict(serializer.validated_data)
            kwargs["employee_number"] = user.employee_number

            kwargs["is_share"] = False
            kwargs["status"] = 0

            model = MachineReservationModel()
            result = model.create(**kwargs)

            # emails = [
            #     # # "<EMAIL>",
            #     # "<EMAIL>",
            #     # "<EMAIL>",
            #     # "<EMAIL>",
            #     "<EMAIL>"
            # ]

            machine_id = serializer.validated_data.get("machine_id")
            machine = MachineModel().retrieve(machine_id)

            # 根据维护人员的工号查询邮箱
            maintainer = machine.get("maintainer")
            if maintainer:
                try:
                    maintainer_user = User.objects.get(employee_number=maintainer)
                    emails = [maintainer_user.email]  # 将维护人员的邮箱添加到列表
                    logger.debug(f"Maintainer email found: {emails}")
                except User.DoesNotExist:
                    logger.warning(f"No user found with employee_number: {maintainer}")
                    emails = []  # 如果没有找到维护人员，邮箱列表为空
            else:
                logger.warning("Maintainer not provided in the request.")
                emails = []

            if machine:
                machine_name = machine.get("name")
            else:
                machine_name = ""
            if result:
                r_id = str(result[0].get("id"))
            else:
                r_id = ""
            for e in emails:
                fs_app.send_machine_reservation_apply_msg(
                    receive_email=e,
                    title="机台预约申请",
                    machine=machine_name,
                    start_time=serializer.validated_data.get("start_time"),
                    end_time=serializer.validated_data.get("end_time"),
                    project=serializer.validated_data.get("project"),
                    content=serializer.validated_data.get("content"),
                    applicant=user.username,
                    r_id=r_id
                )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class MachineReservationCheckinView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = MachineReservationCheckinSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            mr = MachineReservationModel().retrieve(pk=serializer.validated_data.get("id"))

            # 获取当前用户的邮箱
            try:
                user_email = User.objects.get(employee_number=user.employee_number).email
            except User.DoesNotExist:
                return Response({"err_code": 1, "msg": "用户不存在"}, status.HTTP_400_BAD_REQUEST)

            # 检查当前用户的邮箱是否与预约的执行人员邮箱一致
            if user_email != mr.get("executive_personnel"):
                return Response({"err_code": 1, "msg": "签到用户与执行人员不符合。"}, status.HTTP_400_BAD_REQUEST)
            if mr.get("status") != 1:
                return Response({"err_code": 1, "msg": "该状态下预约不可以签到。"}, status.HTTP_400_BAD_REQUEST)
            now = datetime.datetime.now()
            if not ((mr.get("start_time") >= (now - datetime.timedelta(minutes=10)))
                    and (mr.get("start_time") <= (now + datetime.timedelta(minutes=10)))):
                return Response({"err_code": 1, "msg": "只有预约前后10分钟才可以签到。"}, status.HTTP_400_BAD_REQUEST)

            model = MachineReservationModel()
            model.update(pk=serializer.validated_data.get("id"), status=3)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class MachineReservationDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = MachineReservationModel()
            result = model.retrieve(pk=pk)

            if result:
                result["start_time"] = result["start_time"].strftime("%Y-%m-%d %H:%M:%S")
                result["end_time"] = result["end_time"].strftime("%Y-%m-%d %H:%M:%S")

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = MachineReservationUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = MachineReservationModel()

            mr = MachineReservationModel().retrieve(pk=pk)
            if not mr:
                return Response({"err_code": 1, "msg": "预约不存在！"}, status.HTTP_400_BAD_REQUEST)
            if mr.get("status") not in [0, 1, 3]:
                return Response({"err_code": 1, "msg": "该状态下预约不允许修改！"}, status.HTTP_400_BAD_REQUEST)

            if serializer.data.get("start_time") and serializer.data.get("end_time"):
                record = model.retrieve(pk=pk)
                r = MachineReservationModel().exists(
                    machine_id=record.get("machine_id"), start_time=serializer.data["start_time"],
                    end_time=serializer.data["end_time"], exclude_id=pk
                )
                if r:
                    return Response({"err_code": 1, "msg": "预约时间段有冲突。"}, status.HTTP_400_BAD_REQUEST)

            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            model = MachineReservationModel()
            model.delete(pk=pk)
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class MachineReservationCancelView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        try:
            user = request.user
            mr = MachineReservationModel().retrieve(pk=pk)
            if user.employee_number != mr.get("employee_number"):
                return Response({"err_code": 1, "msg": "用户与预约人不符合。"}, status.HTTP_400_BAD_REQUEST)
            if mr.get("status") not in [0, 1, 3]:
                return Response({"err_code": 1, "msg": "该状态下预约不可以取消。"}, status.HTTP_400_BAD_REQUEST)

            model = MachineReservationModel()
            model.cancel(pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class MachineUsageRecordsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = MachineUsageRecordListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = MachineUsageRecordModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class MachineReservationTimelineView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = MachineReservationTimelineSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            content = get_machine_reservation_timeline(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)