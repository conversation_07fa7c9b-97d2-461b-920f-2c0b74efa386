from django.db import models


class SoftwareTrack(models.Model):
    id = models.AutoField(primary_key=True)
    creation_time = models.DateTimeField(auto_now_add=True,verbose_name="创建时间")
    project = models.CharField(max_length=255,verbose_name="所属项目")
    software_type = models.CharField(max_length=255,verbose_name="产品软件类型") # display 对应 产品显示屏MCU版本 motor 对应 产品电机MCU版本  graphic  对应产品图形显示MCU版本
    main_version = models.CharField(max_length=255, null=True, blank=True, verbose_name="主版本号")
    
    version_meta = models.JSONField(null=True, blank=True, verbose_name="版本元数据")
    version_usage = models.Char<PERSON>ield(max_length=255, null=True, blank=True, verbose_name="版本用途")
    
    engineering_group = models.CharField(max_length=255, null=True, blank=True,verbose_name="工程组")
    engineering_path = models.Char<PERSON>ield(max_length=255, null=True, blank=True,verbose_name="工程路径")
    source_branch = models.Char<PERSON>ield(max_length=255, null=True, blank=True,verbose_name="源分支")
    target_branch = models.CharField(max_length=255, null=True, blank=True,verbose_name="目标分支")
    change_description = models.TextField(null=True, blank=True, verbose_name="变更情况说明")
    status = models.CharField(max_length=255, null=True, blank=True,verbose_name="当前状态")
    creator = models.CharField(max_length=255, null=True, blank=True,verbose_name="创建人")
    update_time = models.DateTimeField(auto_now=True,verbose_name="编辑时间")
    merge_commit_hash = models.CharField(max_length=255, null=True, blank=True,verbose_name="合并提交成功后的hash值")
    mr_iid = models.CharField(max_length=255, null=True, blank=True,verbose_name="调用合并请求的ID值")

    class Meta:
        db_table = 'software_track'
        managed = False
        app_label = 'softtrack'

class SoftwareVersionProfile(models.Model):
    id = models.AutoField(primary_key=True)
    version_type = models.CharField(max_length=128, verbose_name="版本类型")  # 例如："产品软件版本"、"产品显示屏MCU版本"等
    component_code = models.CharField(max_length=128, verbose_name="组件代码")  # 例如：DISPLAY_MCU / MOTOR_U / OSD_MCU 等
    version_meta = models.JSONField(verbose_name="版本元数据")  # 存放组件下所有版本字段的元信息
    is_upgradeable = models.BooleanField(default=True, verbose_name="支持在线升级")
    is_writable_online = models.BooleanField(default=False, verbose_name="支持产线写入")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'software_version_profile'
        managed = False
        app_label = 'softtrack'

