import os
import sys
import datetime

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)
os.chdir(BASE_DIR)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'atpms.settings')
import django

django.setup()

from django.db import close_old_connections

from inspecs.models import InspecPerson, InspecRecord, InspecTime
from utils.fs_app import fs_app


def main():
    try:
        today = datetime.date.today()

        inspect_person = InspecPerson.objects.filter(on_shift=True).first()
        if not inspect_person:
            print("点检人员不存在")
            return
        inspect_time = InspecTime.objects.filter(date=today).first()
        if not inspect_time:
            print("点检时间不存在")
            return
        if not InspecRecord.objects.filter(person_id=inspect_person.id, time_id=inspect_time.id).exists():
            print('点检记录不存在')
            return

        time = datetime.datetime(today.year, today.month, today.day, 18, 0, 0)

        time = time.strftime("%Y-%m-%d %H:%M:%S")
        tip = "请完成安排的点检任务，并记录"
        link = "http://www.hwauto.com.cn:59999/inspec_records/list"

        f, data = fs_app.send_inspection_msg(
            receive_email=inspect_person.user_email, person_name=inspect_person.user_name,
            time=time, tip=tip, link=link
        )

        if not f:
            print("发送失败")
    except Exception:
        close_old_connections()


if __name__ == '__main__':
    main()
