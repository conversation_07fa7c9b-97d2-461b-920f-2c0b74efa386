import datetime

from django.db import models, transaction

from utils.sql_helper import (
    sql_fetchone_dict, sql_fetchall_dict,
)


class InspecItem(models.Model):
    name = models.CharField(max_length=255)
    # code = models.CharField(max_length=255, unique=True)
    desc = models.TextField(default='', blank=True)
    is_active = models.BooleanField(default=True)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inspec_items'
        managed = False
        app_label = "inspecs"


class InspecPerson(models.Model):
    user_name = models.CharField(max_length=255)
    user_email = models.CharField(max_length=255)
    order = models.IntegerField(default=0)
    is_available = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    on_shift = models.BooleanField(default=False)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inspec_persons'
        managed = False
        app_label = "inspecs"


class InspecTime(models.Model):
    date = models.DateField()
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inspec_times'
        managed = False
        app_label = "inspecs"


class InspecRecord(models.Model):
    item_id = models.IntegerField()
    person_id = models.IntegerField()
    time_id = models.IntegerField()
    is_pass = models.BooleanField(verbose_name="是否通过")
    desc = models.TextField(default='', blank=True, verbose_name="描述")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'inspec_records'
        managed = False
        app_label = "inspecs"


class InspecModel:
    def __init__(self):
        pass

    def get_items(self, **kwargs):
        page = kwargs.get('page', 1)
        pagesize = kwargs.get('pagesize', 10)
        offset = (page - 1) * pagesize

        queryset = InspecItem.objects.all()

        if kwargs.get('name'):
            queryset = queryset.filter(name__icontains=kwargs['name'])
        # if kwargs.get('code'):
        #     queryset = queryset.filter(code__icontains=kwargs['code'])

        total_count = queryset.count()

        queryset = queryset.filter(is_active=True)

        queryset = queryset.order_by('-create_time')[offset:offset + pagesize]

        results = queryset.all().values(
            'id', 'name', 'desc', 'create_time', 'update_time'
        )

        for i in results:
            i['create_time'] = i['create_time'].strftime('%Y-%m-%d %H:%M:%S')
            i['update_time'] = i['update_time'].strftime('%Y-%m-%d %H:%M:%S')

        return {
            'count': total_count,
            'results': results,
        }

    def create_item(self, **kwargs):
        InspecItem.objects.create(
            name=kwargs.get('name'),
            # code=kwargs.get('code'),
            desc=kwargs.get('desc'),
        )

    def get_item(self, pk):
        try:
            item = InspecItem.objects.get(pk=pk, is_active=True)
            return {
                'id': item.id,
                'name': item.name,
                # 'code': item.code,
                'desc': item.desc,
                'create_time': item.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                'update_time': item.update_time.strftime('%Y-%m-%d %H:%M:%S'),
            }
        except InspecItem.DoesNotExist:
            return None

    def update_item(self, pk, **kwargs):
        try:
            item = InspecItem.objects.get(pk=pk, is_active=True)
            item.name = kwargs.get('name', item.name)
            item.desc = kwargs.get('desc', item.desc)
            item.save()
        except InspecItem.DoesNotExist:
            raise ValueError("Item not found")

    def delete_item(self, pk):
        try:
            item = InspecItem.objects.get(pk=pk, is_active=True)
            item.is_active = False
            item.save()
        except InspecItem.DoesNotExist:
            return None

    def get_persons(self, **kwargs):
        page = kwargs.get('page', 1)
        pagesize = kwargs.get('pagesize', 10)
        offset = (page - 1) * pagesize

        queryset = InspecPerson.objects.all()

        if kwargs.get('user_name'):
            queryset = queryset.filter(user_name__icontains=kwargs['user_name'])
        if kwargs.get('status') is not None:
            queryset = queryset.filter(is_available=kwargs['status'])

        total_count = queryset.count()

        queryset = queryset.filter(is_active=True)

        queryset = queryset.order_by('order')[offset:offset + pagesize]

        results = queryset.all().values(
            'id', 'user_name', 'is_available', 'user_email', 'on_shift', 'create_time', 'update_time'
        )

        for i in results:
            i['create_time'] = i['create_time'].strftime('%Y-%m-%d %H:%M:%S')
            i['update_time'] = i['update_time'].strftime('%Y-%m-%d %H:%M:%S')

        return {
            'count': total_count,
            'results': results,
        }

    def create_person(self, **kwargs):
        with transaction.atomic():
            pn = InspecPerson.objects.create(
                user_name=kwargs.get('user_name'),
                user_email=kwargs.get('user_email'),
            )
            pn.order = pn.id
            pn.save()

    def get_person(self, pk):
        try:
            person = InspecPerson.objects.get(pk=pk, is_active=True)
            return {
                'id': person.id,
                'user_name': person.user_name,
                'user_email': person.user_email,
                'is_available': person.is_available,
                'on_shift': person.on_shift,
                'order': person.order,
                'create_time': person.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                'update_time': person.update_time.strftime('%Y-%m-%d %H:%M:%S'),
            }
        except InspecPerson.DoesNotExist:
            return None

    def update_person(self, pk, **kwargs):
        try:
            person = InspecPerson.objects.get(pk=pk, is_active=True)
            person.is_available = kwargs.get('is_available')
            person.save()
        except InspecPerson.DoesNotExist:
            raise ValueError("Item not found")

    def delete_person(self, pk):
        try:
            person = InspecPerson.objects.get(pk=pk, is_active=True)
            person.is_active = False
            person.save()
        except InspecPerson.DoesNotExist:
            return None

    def get_records(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        params = {
        }

        sql_where_list = [
        ]

        start_time = kwargs.get("start_time")
        if not start_time:
            start_time = datetime.date.today()
        sql_where_list.append("t.date >= %(start_time)s")
        params["start_time"] = start_time

        end_time = kwargs.get("end_time")
        if not end_time:
            end_time = datetime.date.today()
        end_time += datetime.timedelta(days=1)
        sql_where_list.append("t.date < %(end_time)s")
        params["end_time"] = end_time

        item_name = kwargs.get("item_name")
        if item_name is not None and item_name != '':
            sql_where_list.append("i.name ilike %(item_name)s")
            params["item_name"] = f"%{item_name}%"

        # item_code = kwargs.get("item_code")
        # if item_code is not None and item_code != '':
        #     sql_where_list.append("i.code ilike %(item_code)s")
        #     params["item_code"] = f"%{item_code}%"

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        sql_order_by = " ORDER BY t.date, r.item_id"

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
            FROM public.inspec_times t left join public.inspec_records r 
                on t.id = r.time_id
                left join public.inspec_items i
                on r.item_id = i.id
                left join public.inspec_persons p
                on r.person_id = p.id
            {sql_where}
            ;
        """.format(
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT r.id, r.is_pass, r.desc, r.create_time, r.update_time, t.date as time_date,
                i.id as item_id, i.name as item_name, i.desc as item_desc,
                p.id as person_id, p.user_name as person_name, p.user_email as person_email
            FROM public.inspec_times t left join public.inspec_records r 
                on t.id = r.time_id
                left join public.inspec_items i
                on r.item_id = i.id
                left join public.inspec_persons p
                on r.person_id = p.id
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def get_records2(self, **kwargs):
        params = {
        }
        sql_where_list = [
        ]

        start_time = kwargs.get("start_time")
        if not start_time:
            today = datetime.date.today()
            start_time = datetime.date(year=today.year, month=today.month, day=1)
        sql_where_list.append("t.date >= %(start_time)s")
        params["start_time"] = start_time

        end_time = kwargs.get("end_time")
        if not end_time:
            today = datetime.date.today()
            end_time = datetime.date(year=today.year, month=today.month + 1, day=1)
        else:
            end_time += datetime.timedelta(days=1)
        sql_where_list.append("t.date < %(end_time)s")
        params["end_time"] = end_time

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        sql_order_by = " ORDER BY t.date, r.item_id"

        sql = """
            SELECT r.is_pass, t.date as time_date, i.name as item_name, p.user_name as person_name
            FROM public.inspec_times t left join public.inspec_records r 
                on t.id = r.time_id
                left join public.inspec_items i
                on r.item_id = i.id
                left join public.inspec_persons p
                on r.person_id = p.id
            {sql_where}
            {sql_order_by}
            ;
        """.format(
            sql_where=sql_where,
            sql_order_by=sql_order_by,
        )

        results = sql_fetchall_dict(sql, params)

        return results

    def get_record(self, pk):
        sql = """
            SELECT r.id, r.is_pass, r.desc, r.create_time, r.update_time, t.date as time_date,
                i.id as item_id, i.name as item_name, i.desc as item_desc,
                p.id as person_id, p.user_name as person_name, p.user_email as person_email
            FROM public.inspec_records r 
                left join public.inspec_times t on r.time_id = t.id
                left join public.inspec_items i on r.item_id = i.id
                left join public.inspec_persons p on r.person_id = p.id
            WHERE r.id = %s;
        """

        result = sql_fetchone_dict(sql, [pk])

        return result

    def update_record(self, pk, **kwargs):
        try:
            record = InspecRecord.objects.get(pk=pk)
            record.is_pass = kwargs.get('is_pass')
            record.desc = kwargs.get('desc')
            record.save()
        except InspecRecord.DoesNotExist:
            raise ValueError("Item not found")

    def dispatch_task(self, person_id, time_id):
        n_items = InspecItem.objects.filter(is_active=True).order_by('id').all()
        with transaction.atomic():
            for n_item in n_items:
                InspecRecord.objects.create(
                    item_id=n_item.id,
                    time_id=time_id,
                    person_id=person_id,
                )
