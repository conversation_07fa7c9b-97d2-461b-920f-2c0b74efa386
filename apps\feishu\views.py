import traceback
import logging
import json
import datetime
import pprint
import time

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from utils.fs_aes import cipher
from utils.fs_app import fs_app
from utils.fs_service import FSService
from machines.models import MachineReservationModel, MachineModel
from users.models import User
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

logger = logging.getLogger("machine")

STATUS_MAP = {
    0: "审核中",
    1: "已批准",
    2: "已取消",
    3: "已签到",
    4: "超期未签到释放",
    5: "到期释放",
    6: "超期未批准释放",
}

fs_service = FSService()


class FSCallBackView(APIView):

    def post(self, request):
        try:
            data = request.data
            encrypt = data.get("encrypt")
            data = json.loads(cipher.decrypt_string(encrypt))
            logger.info(data)

            event = data.get("event")
            action = event.get("action").get("value").get("action")
            r_id = int(event.get("action").get("value").get("r_id"))

            if action == "checkin":
                mr = MachineReservationModel().retrieve(pk=r_id)

                now = datetime.datetime.now()
                if not ((mr.get("start_time") >= (now - datetime.timedelta(minutes=10)))
                        and (mr.get("start_time") <= (now + datetime.timedelta(minutes=10)))):
                    content = "只有预约前后10分钟才可以签到。"
                else:
                    if mr.get("status") != 1:
                        if mr.get("status") == 3:
                            content = "预约已签到。不用重复签到"
                        else:
                            content = f"{STATUS_MAP.get(mr.get('status'))}状态下的预约不可以签到。"
                    else:
                        try:
                            model = MachineReservationModel()
                            model.update(pk=r_id, status=3)
                            content = "预约签到成功!"
                        except Exception:
                            content = "预约签到失败!"

                r_data = {
                    "toast": {
                        "type": "info",
                        "content": content,
                    }
                }
            elif action == "cancel":
                mr = MachineReservationModel().retrieve(pk=r_id)

                if mr.get("status") not in [1, 3]:
                    if mr.get("status") == 2:
                        content = "预约已取消。不用重复取消"
                    else:
                        content = f"{STATUS_MAP.get(mr.get('status'))}状态下的预约不可以取消。"
                else:
                    try:
                        model = MachineReservationModel()
                        model.cancel(r_id)
                        content = "预约取消成功!"
                    except Exception:
                        content = "预约取消失败!"

                r_data = {
                    "toast": {
                        "type": "info",
                        "content": content,
                    }
                }
            elif action == "approve":
                mr = MachineReservationModel().retrieve(pk=r_id)

                if mr.get("status") != 0:
                    if mr.get("status") == 1:
                        content = "预约已批准。"
                    else:
                        content = f"{STATUS_MAP.get(mr.get('status'))}状态下的预约不能进行批准操作。"
                else:
                    r = MachineReservationModel().exists(
                        machine_id=mr["machine_id"], start_time=mr["start_time"], end_time=mr["end_time"],
                        status_list=[1, 3]
                    )
                    if r:
                        content = "该时间段已有预约。不能批准该预约"
                    else:
                        try:
                            model = MachineReservationModel()
                            model.update(status=1, pk=r_id)
                            content = "预约批准成功!"

                            employee_number = mr.get("employee_number")
                            user = User.objects.get(employee_number=employee_number)
                            machine_id = mr.get("machine_id")
                            machine = MachineModel().retrieve(pk=machine_id)

                            fs_app.send_machine_reservation_time_out_msg(
                                receive_email=user.email,
                                title="机台预约已批准",
                                machine=machine.get("name"),
                                start_time=mr.get("start_time"),
                                end_time=mr.get("end_time"),
                                project=mr.get("project"),
                                content=mr.get("content"),
                                applicant=user.username
                            )

                            open_id = data.get("event").get("operator").get("open_id")
                            if open_id != user.open_id:
                                user2 = User.objects.get(open_id=open_id)
                                fs_app.send_machine_reservation_time_out_msg(
                                    receive_email=user2.email,
                                    title="机台预约已批准",
                                    machine=machine.get("name"),
                                    start_time=mr.get("start_time"),
                                    end_time=mr.get("end_time"),
                                    project=mr.get("project"),
                                    content=mr.get("content"),
                                    applicant=user.username
                                )

                        except Exception:
                            content = "预约批准失败!"

                r_data = {
                    "toast": {
                        "type": "info",
                        "content": content,
                    }
                }
            else:
                r_data = {}

            return Response(r_data, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class FSOrganizationView(APIView):
    @method_decorator(cache_page(60 * 60))
    def get(self, request):
        try:
            department_id = request.query_params.get("department_id", "0")

            items = []
            has_more = True
            page_token = None
            while has_more:
                f, data = fs_app.get_departments_by_department_id(department_id=department_id,
                                                                  page_token=page_token)
                if not f:
                    return Response({"err_code": 3, "msg": data.get("msg")}, status.HTTP_200_OK)
                t_items = data.get("data").get("items", [])
                for i in t_items:
                    i["is_department"] = True
                items.extend(t_items)
                has_more = data.get("data").get("has_more")
                page_token = data.get("data").get("page_token")

                if has_more:
                    time.sleep(0.06)

            has_more = True
            page_token = None
            while has_more:
                f, data = fs_app.get_users_by_department_id(department_id=department_id, page_token=page_token)
                if not f:
                    return Response({"err_code": 3, "msg": data.get("msg")}, status.HTTP_200_OK)
                t_items = data.get("data").get("items", [])
                for i in t_items:
                    i["is_department"] = False
                items.extend(t_items)
                has_more = data.get("data").get("has_more")
                page_token = data.get("data").get("page_token")

                if has_more:
                    time.sleep(0.06)

            return Response({"err_code": 0, "data": items, "msg": "ok"}, status.HTTP_200_OK)

        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
