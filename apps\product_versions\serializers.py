from rest_framework import serializers


class ProductVersionListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    project_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    status_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    type_list = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)
    version_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    version_number = serializers.Char<PERSON>ield(max_length=255, required=False, allow_blank=True, allow_null=True)


class ProductVersionSerializer(serializers.Serializer):
    project_name = serializers.CharField(max_length=255)
    project_number = serializers.Char<PERSON><PERSON>(max_length=255)
    project_id = serializers.Char<PERSON>ield(max_length=255)
    type = serializers.Char<PERSON>ield(max_length=255)
    type_name = serializers.CharField(max_length=255)
    use = serializers.Char<PERSON>ield(max_length=255)
    use_name = serializers.CharField(max_length=255)
    version_number = serializers.CharField(max_length=255)
    version_name = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_blank=True, allow_null=True, trim_whitespace=False)
    publisher_name = serializers.CharField(max_length=255)
    publisher_email = serializers.CharField(max_length=255)
    version_package_address = serializers.CharField(allow_blank=True, max_length=255)
    plan_version = serializers.CharField(max_length=255)
    customer_version = serializers.CharField(max_length=255)
    publish_time = serializers.DateTimeField()
    sub_version_info = serializers.CharField(required=False, allow_blank=True, allow_null=True)


class ProductVersionUpdateStatusSerializer(serializers.Serializer):
    id = serializers.IntegerField()  # 添加 id 字段
    project_number = serializers.CharField(max_length=255)
    version_names = serializers.ListField(child=serializers.CharField(max_length=255), min_length=1)
    status = serializers.CharField(max_length=255)


class ProductVersionStatusUpdateSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    status = serializers.CharField(max_length=255)
    remark = serializers.CharField(required=False, allow_blank=True, max_length=255)
    package_url = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    target_url = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    attachment_url = serializers.CharField(required=False, allow_blank=True, allow_null=True)


class ProductVersionRemarkUpdateSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    remark = serializers.CharField()

