from rest_framework import serializers


class IssueListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    project_number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    machine_number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    tester = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)
    status = serializers.IntegerField(required=False)
    start_time = serializers.DateTimeField(required=False)
    end_time = serializers.DateTimeField(required=False)


class IssueSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=120)
    project_number = serializers.Char<PERSON><PERSON>(max_length=255)
    occur_time = serializers.DateTimeField()
    tester = serializers.CharField(max_length=255)
    machine_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    images = serializers.ListField(child=serializers.FileField(), min_length=0, required=False)
    video = serializers.FileField(required=False)


class OccurVersionSerializer(serializers.Serializer):
    num = serializers.CharField(max_length=255)
    type = serializers.CharField(max_length=255)


class IssuePushSerializer(serializers.Serializer):
    title = serializers.CharField(max_length=120)
    projectCode = serializers.CharField(max_length=255)
    module = serializers.CharField(max_length=255)
    probability = serializers.CharField(max_length=255)
    testerOpenIds = serializers.ListField(child=serializers.EmailField(), min_length=1)
    occurVersionList = serializers.ListField(child=OccurVersionSerializer(), min_length=1)
    frontCondition = serializers.CharField(max_length=255)
    testStep = serializers.CharField(max_length=255)
    expectResult = serializers.CharField(max_length=255)
    actualResult = serializers.CharField(max_length=255)
    remark = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    handlerIdList = serializers.ListField(child=serializers.EmailField(), min_length=1)
    starIdList = serializers.ListField(child=serializers.EmailField(), min_length=0, allow_null=None)
    beginTime = serializers.DateTimeField()
    endTime = serializers.DateTimeField()
    level = serializers.CharField(max_length=255)
    occurProjectProgress = serializers.CharField(max_length=255)
    test_case_number = serializers.CharField(max_length=255)
    test_case_version = serializers.CharField(max_length=255)
