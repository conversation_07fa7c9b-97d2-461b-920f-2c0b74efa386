--
CREATE TABLE IF NOT EXISTS public.software_track(
    id serial PRIMARY KEY,
    creation_time TIMESTAMP,
    project VARCHAR(255),
    software_type VARCHAR(255),
    main_version VARCHAR(255),
    
    -- Product display screen MCU version fields
    software VARCHAR(255),
    hw_software VARCHAR(255),
    hw_boot VARCHAR(255),
    
    -- Product motor MCU version fields
    motor_software VARCHAR(255),
    hw_motor_software VARCHAR(255),
    hw_motor_boot VARCHAR(255),
    
    -- Product graphical MCU version fields
    osd_software VARCHAR(255),
    hw_osd_software VARCHAR(255),
    hw_osd_boot VARCHAR(255),
    
    engineering_group VARCHAR(255),
    engineering_path VARCHAR(255),
    source_branch VARCHAR(255),
    target_branch VARCHAR(255),
    change_description TEXT,
    status VARCHAR(255),
    creator VARCHA<PERSON>(255),
    merge_commit_hash VARCHAR(255),  
    update_time TIMESTAMP
);
ALTER TABLE public.software_track OWNER TO atpms;
ALTER TABLE IF EXISTS public.software_track 
ADD COLUMN IF NOT EXISTS merge_commit_hash VARCHAR(255);  
ADD COLUMN IF NOT EXISTS mr_iid VARCHAR(255);  

ALTER TABLE public.software_track
    DROP COLUMN IF EXISTS software,
    DROP COLUMN IF EXISTS hw_software,
    DROP COLUMN IF EXISTS hw_boot,
    DROP COLUMN IF EXISTS motor_software,
    DROP COLUMN IF EXISTS hw_motor_software,
    DROP COLUMN IF EXISTS hw_motor_boot,
    DROP COLUMN IF EXISTS osd_software,
    DROP COLUMN IF EXISTS hw_osd_software,
    DROP COLUMN IF EXISTS hw_osd_boot,
    ADD COLUMN IF NOT EXISTS version_meta json,
    ADD COLUMN IF NOT EXISTS version_usage VARCHAR(255);



CREATE TABLE IF NOT EXISTS public.software_version_profile (
    id                  SERIAL PRIMARY KEY,
    -- 例如：“产品软件版本”、“产品显示屏MCU版本”等中文描述
    version_type        VARCHAR(128) NOT NULL,
    -- 例如：DISPLAY_MCU / MOTOR_U / OSD_MCU 等英文标识
    component_code      VARCHAR(128) NOT NULL,
    -- 存放该组件下所有版本字段的元信息（name: 字段名，meaning: 字段含义，value: 实际版本值）
    version_meta        JSONB        NOT NULL,
    -- 是否支持在线升级
    is_upgradeable      BOOLEAN      DEFAULT TRUE,
    -- 是否支持产线写入
    is_writable_online  BOOLEAN      DEFAULT FALSE,
    -- 记录创建时间
    created_at          TIMESTAMP    DEFAULT NOW(),
    -- 记录最后更新时间
    updated_at          TIMESTAMP    DEFAULT NOW()
);