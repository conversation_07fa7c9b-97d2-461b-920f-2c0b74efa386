from django.urls import path

from .views import (
    DiffToolUsageRecordsView, DiffToolUsageRecordDetailView, DiffToolUsageRecordVerifyView,
    DiffPackageShareUrlsView, DiffPackageShareUrlDetailView, DiffPackageDownloadView,
    DiffPackageMadeByJenkinsView
)

urlpatterns = [
    path("/records", DiffToolUsageRecordsView.as_view()),

    path("/records/made_by_jenkins", DiffPackageMadeByJenkinsView.as_view()),

    path("/records/<int:pk>", DiffToolUsageRecordDetailView.as_view()),

    path("/records/verify", DiffToolUsageRecordVerifyView.as_view()),

    path("/records/share_urls", DiffPackageShareUrlsView.as_view()),

    path("/records/share_urls/<str:token>", DiffPackageShareUrlDetailView.as_view()),

    path("/records/share_urls/<str:token>/download", DiffPackageDownloadView.as_view()),
]
