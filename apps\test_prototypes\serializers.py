from rest_framework import serializers


class TestPrototypesListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    type_re = serializers.IntegerField(default=0)
    status_re = serializers.IntegerField(default=0)
    number_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    project_re = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)
    project_name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TestPrototypesSerializer(serializers.Serializer):
    name = serializers.Char<PERSON><PERSON>(max_length=255)
    type = serializers.IntegerField(default=0)
    status = serializers.Integer<PERSON>ield(default=0)
    project = serializers.Char<PERSON>ield(max_length=255)
    project_name = serializers.CharField(max_length=255, required=False)
    user_name = serializers.CharField(max_length=255)
    user_email = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    prototype_stage = serializers.IntegerField(required=False, allow_null=True)


class TestPrototypesUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    type = serializers.IntegerField(default=0)
    status = serializers.IntegerField(default=0)
    project = serializers.CharField(max_length=255)
    project_name = serializers.CharField(max_length=255, required=False)
    user_name = serializers.CharField(max_length=255)
    user_email = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    prototype_stage = serializers.IntegerField(required=False, allow_null=True)
