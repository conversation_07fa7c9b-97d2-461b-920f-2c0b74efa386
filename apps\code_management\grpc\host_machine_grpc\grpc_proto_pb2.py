# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: grpc_proto.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'grpc_proto.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10grpc_proto.proto\x1a\x1bgoogle/protobuf/empty.proto\"N\n\rWorkSpaceInfo\x12\x10\n\x08sdk_path\x18\x01 \x01(\t\x12\x14\n\x0cproject_path\x18\x02 \x01(\t\x12\x15\n\rbranch_status\x18\x03 \x01(\t\",\n\x18GetProjectTreeDataParams\x12\x10\n\x08sdk_path\x18\x01 \x01(\t\"A\n\x17ProjectTreeDataResponse\x12\x13\n\x0bmodule_list\x18\x01 \x03(\t\x12\x11\n\tchip_list\x18\x02 \x03(\t\"j\n\x19\x43onfigChipFunctionsParams\x12\'\n\x0fwork_space_info\x18\x01 \x01(\x0b\x32\x0e.WorkSpaceInfo\x12\x11\n\tchip_type\x18\x02 \x01(\t\x12\x11\n\tfunctions\x18\x03 \x01(\t\"O\n\x1b\x43onfigChipFunctionsResponse\x12\x15\n\rconfig_status\x18\x01 \x01(\x05\x12\x19\n\x11project_tree_data\x18\x02 \x01(\t\"Q\n\x0bSubFunction\x12\'\n\x0fwork_space_info\x18\x01 \x01(\x0b\x32\x0e.WorkSpaceInfo\x12\x19\n\x11project_tree_item\x18\x02 \x01(\t\"=\n\x1fProjectSubFunctionItemsResponse\x12\x1a\n\x12sub_function_items\x18\x01 \x01(\t\"\xd1\x01\n\x0c\x43onfigParams\x12\'\n\x0fwork_space_info\x18\x01 \x01(\x0b\x32\x0e.WorkSpaceInfo\x12\x18\n\x10\x63onfig_para_path\x18\x02 \x01(\t\x12\x1a\n\x12\x63onfig_module_name\x18\x03 \x01(\t\x12\x18\n\x10\x63onfig_para_type\x18\x04 \x01(\t\x12\x18\n\x10\x63onfig_para_name\x18\x05 \x01(\t\x12\x19\n\x11\x63onfig_para_value\x18\x06 \x01(\t\x12\x13\n\x0bmodule_name\x18\x07 \x01(\t\"1\n\x18UpdateConfigItemResponse\x12\x15\n\rconfig_status\x18\x01 \x01(\x05\"d\n\x14GetMCUChipInfoParams\x12\'\n\x0fwork_space_info\x18\x01 \x01(\x0b\x32\x0e.WorkSpaceInfo\x12\x11\n\tchip_name\x18\x02 \x01(\t\x12\x10\n\x08hal_path\x18\x03 \x01(\t\"L\n\x1cGetMCUChipInfoParamsResponse\x12\x15\n\rconfig_status\x18\x01 \x01(\x05\x12\x15\n\rMCU_chip_info\x18\x02 \x01(\t\"<\n\x11GetGPIOInfoParams\x12\'\n\x0fwork_space_info\x18\x01 \x01(\x0b\x32\x0e.WorkSpaceInfo\"L\n\x19GetGPIOInfoParamsResponse\x12\x15\n\rconfig_status\x18\x01 \x01(\x05\x12\x18\n\x10gpio_module_info\x18\x02 \x01(\t\"\xb8\x01\n\x1aUpdateGPIOConfigItemParams\x12\'\n\x0fwork_space_info\x18\x01 \x01(\x0b\x32\x0e.WorkSpaceInfo\x12\x15\n\rconfig_module\x18\x02 \x01(\t\x12\x13\n\x0b\x63onfig_item\x18\x03 \x01(\t\x12\x14\n\x0c\x63onfig_value\x18\x04 \x01(\t\x12\x11\n\tchip_name\x18\x05 \x01(\t\x12\x0e\n\x06pin_id\x18\x06 \x01(\t\x12\x0c\n\x04name\x18\x07 \x01(\t\";\n\"UpdateGPIOConfigItemParamsResponse\x12\x15\n\rconfig_status\x18\x01 \x01(\x05\"$\n\x12\x45xportCodeResponse\x12\x0e\n\x06status\x18\x01 \x01(\x05\x32\xca\x04\n\x0bHWCPService\x12I\n\x12GetProjectTreeData\x12\x19.GetProjectTreeDataParams\x1a\x18.ProjectTreeDataResponse\x12O\n\x13\x43onfigChipFunctions\x12\x1a.ConfigChipFunctionsParams\x1a\x1c.ConfigChipFunctionsResponse\x12\x45\n\x13GetSubFunctionItems\x12\x0c.SubFunction\x1a .ProjectSubFunctionItemsResponse\x12<\n\x10UpdateConfigItem\x12\r.ConfigParams\x1a\x19.UpdateConfigItemResponse\x12\x46\n\x0eGetMCUChipInfo\x12\x15.GetMCUChipInfoParams\x1a\x1d.GetMCUChipInfoParamsResponse\x12=\n\x0bGetGPIOInfo\x12\x12.GetGPIOInfoParams\x1a\x1a.GetGPIOInfoParamsResponse\x12X\n\x14UpdateGPIOConfigItem\x12\x1b.UpdateGPIOConfigItemParams\x1a#.UpdateGPIOConfigItemParamsResponse\x12\x39\n\nExportCode\x12\x16.google.protobuf.Empty\x1a\x13.ExportCodeResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'grpc_proto_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_WORKSPACEINFO']._serialized_start=49
  _globals['_WORKSPACEINFO']._serialized_end=127
  _globals['_GETPROJECTTREEDATAPARAMS']._serialized_start=129
  _globals['_GETPROJECTTREEDATAPARAMS']._serialized_end=173
  _globals['_PROJECTTREEDATARESPONSE']._serialized_start=175
  _globals['_PROJECTTREEDATARESPONSE']._serialized_end=240
  _globals['_CONFIGCHIPFUNCTIONSPARAMS']._serialized_start=242
  _globals['_CONFIGCHIPFUNCTIONSPARAMS']._serialized_end=348
  _globals['_CONFIGCHIPFUNCTIONSRESPONSE']._serialized_start=350
  _globals['_CONFIGCHIPFUNCTIONSRESPONSE']._serialized_end=429
  _globals['_SUBFUNCTION']._serialized_start=431
  _globals['_SUBFUNCTION']._serialized_end=512
  _globals['_PROJECTSUBFUNCTIONITEMSRESPONSE']._serialized_start=514
  _globals['_PROJECTSUBFUNCTIONITEMSRESPONSE']._serialized_end=575
  _globals['_CONFIGPARAMS']._serialized_start=578
  _globals['_CONFIGPARAMS']._serialized_end=787
  _globals['_UPDATECONFIGITEMRESPONSE']._serialized_start=789
  _globals['_UPDATECONFIGITEMRESPONSE']._serialized_end=838
  _globals['_GETMCUCHIPINFOPARAMS']._serialized_start=840
  _globals['_GETMCUCHIPINFOPARAMS']._serialized_end=940
  _globals['_GETMCUCHIPINFOPARAMSRESPONSE']._serialized_start=942
  _globals['_GETMCUCHIPINFOPARAMSRESPONSE']._serialized_end=1018
  _globals['_GETGPIOINFOPARAMS']._serialized_start=1020
  _globals['_GETGPIOINFOPARAMS']._serialized_end=1080
  _globals['_GETGPIOINFOPARAMSRESPONSE']._serialized_start=1082
  _globals['_GETGPIOINFOPARAMSRESPONSE']._serialized_end=1158
  _globals['_UPDATEGPIOCONFIGITEMPARAMS']._serialized_start=1161
  _globals['_UPDATEGPIOCONFIGITEMPARAMS']._serialized_end=1345
  _globals['_UPDATEGPIOCONFIGITEMPARAMSRESPONSE']._serialized_start=1347
  _globals['_UPDATEGPIOCONFIGITEMPARAMSRESPONSE']._serialized_end=1406
  _globals['_EXPORTCODERESPONSE']._serialized_start=1408
  _globals['_EXPORTCODERESPONSE']._serialized_end=1444
  _globals['_HWCPSERVICE']._serialized_start=1447
  _globals['_HWCPSERVICE']._serialized_end=2033
# @@protoc_insertion_point(module_scope)
