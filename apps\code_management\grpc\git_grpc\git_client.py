import grpc
import json
from google.protobuf.empty_pb2 import Empty

from utils.proto import logdir_pb2
from utils.proto import logdir_pb2_grpc



# 定义全局服务地址
# SERVICE_ADDRESS = '**********:50051'
# SERVICE_ADDRESS = 'localhost:50051'
SERVICE_ADDRESS = '**********:50051'


# 连接复用（可选：在程序启动时创建全局channel）
def get_stub():
    channel = grpc.insecure_channel(SERVICE_ADDRESS)
    return logdir_pb2_grpc.GitlabServiceStub(channel)



def get_branch_serve(sdk_path):
    # 获取分支信息
    try:
        stub = get_stub()
        response = stub.GetProjectBranches(
            logdir_pb2.GetProjectBranchesRequest(
                project_name=sdk_path
            )
        )
        print("获取得分支信息：", response)
        return response
    except Exception as e:
        print("调用 serve 异常:", e)
        return None  # 或自行定义错误对象

def get_tag_serve(sdk_path):
    # 获取tag信息
    try:
        stub = get_stub()
        response = stub.GetProjectTags(
            logdir_pb2.GetProjectTagsRequest(
                project_path=sdk_path,
                limit=5,
                sort=True
            )
        )
        print("获取得tag信息：", response)
        return response
    except Exception as e:
        print("调用 serve 异常:", e)
        return None  # 或自行定义错误对象


def clone_serve(repo_url, project_path, branch=None, tag=None):
    # 克隆仓库指定分支到指定路径
    try:
        # 定义作用域
        response = ""
        stub = get_stub()
        if branch and not tag:
            response = stub.CloneRepository(
                logdir_pb2.CloneRepositoryRequest(
                repo_url=repo_url,
                project_path=project_path,
                branch=branch
                )
            )
            print("克隆分支代码：", response)
        elif tag and not branch:
            response = stub.CloneRepository(
                logdir_pb2.CloneRepositoryRequest(
                repo_url=repo_url,
                project_path=project_path,
                tag=tag
                )
            )
            print("克隆tag代码：", response)
        return response
    except Exception as e:
        print("调用 serve 异常:", e)
        return None  # 或自行定义错误对象


def get_temp_serve():
    # 获取组信息
    try:
        stub = get_stub()
        response = stub.GetSubgroupPaths(
            logdir_pb2.GetSubgroupPathsRequest(
                group_path="mcu-team"
                # 测试阶段
                # group_path="python-team"
            )
        )
        print("获取的组：", response)
        return response
    except Exception as e:
        print("调用 serve 异常:", e)
        return None  # 或自行定义错误对象


def create_project_serve(full_project_name, group_path, description=None):
    try:
        stub = get_stub()
        # 动态构建请求参数
        request_kwargs = {
            "full_project_name": full_project_name,
            "group_path": group_path
        }
        if description:
            request_kwargs["description"] = description

        request = logdir_pb2.CreateRepositoryRequest(**request_kwargs)
        response = stub.CreateRepository(request)
        print("创建仓库：", response)
        return response

    except Exception as e:
        print("调用 serve 异常:", e)
        return None



def create_branch_serve(project_path, branch_name):
    # 创建分支
    try:
        stub = get_stub()
        response = stub.CreateBranch(
            logdir_pb2.CreateBranchRequest(
                project_path=project_path,
                branch_name=branch_name
            )
        )
        print("创建分支：", response)
        return response
    except Exception as e:
        print("调用 serve 异常:", e)
        return None  # 或自行定义错误对象



def commit_serve(project_path, commit_message):
    """

    message GitCommitRequest {
      string project_path = 1;
      string branch = 2;  // 传空
      string commit_message = 3;
      repeated string file_paths = 4;  //传空
      repeated string file_paths = 4;  //传空
    }

    """
    try:
        print("project_path:", project_path)
        stub = get_stub()
        response = stub.GitCommit(
            logdir_pb2.GitCommitRequest(
            project_path=project_path,
            branch="",
            commit_message=commit_message,
            file_paths=""
            )
        )
        print("commit的状态:", response)
        return response
    except Exception as e:
        print("调用 serve 异常:", e)
        return None  # 或自行定义错误对象


def push_serve(project_path):

    try:
        stub = get_stub()
        response = stub.GitPush(
            logdir_pb2.GitPushRequest(
            project_path=project_path,
            branch=""
           )
        )
        print("push的状态:", response)
        return response
    except Exception as e:
        print("调用 serve 异常:", e)
        return None  # 或自行定义错误对象



def merge_serve(gitlab, local_branch, merge_branch):
    try:
        stub = get_stub()
        response = stub.CreateOrGetMergeRequest(
            logdir_pb2.CreateOrGetMergeRequestRequest(
            engineering_path=gitlab,
            source_branch=local_branch,
            target_branch=merge_branch
           )
        )
        print("merge的状态:", response)
        return response
    except Exception as e:
        print("调用 serve 异常:", e)
        return None  # 或自行定义错误对象



# if __name__ == "__main__":
#     response = get_temp_serve()
#     print("获取组信息：", response)
#     groups = list(response.subgroup_paths)
#     print("获取到得组：", groups)
    # workgroup = "mcu-team/alps/rescn11"
    # workspace = "test"
    # full_project_name = f"{workspace}"
    # group_path = f"{workgroup}"
    # # print("full_project_name:", full_project_name)
    # # print("group_path:", group_path)
    # # project_response = creat_project_serve(full_project_name, group_path)
    # # print("创建仓库:", project_response)
    # project_path = f"{workgroup}/{workspace}"
    # branch_name = "dev_test_hwcp"
    # branch_response = create_brnach_serve(project_path, branch_name)
    # print("创建分支：", branch_response)

    # project_path = "AutoTestMicroservices"
    # response = commit_serve(project_path)
    # """
    #   bool success = 1;
    #   string message = 2;
    #   string commit_id = 3;
    #   int32 code = 4;
    # """
    # print("状态：", response.success)
    # print("信息：", response.message)
    # print("commit_id:", response.commit_id)
    # print("code：", response.code)
    # 查询分支
    # sdk_path = "hiwaysdk_2.0"
    # response = get_branch_serve(sdk_path)
    # print(response.success)
    # print(response.message)
    # print(response.branches)

    # # 克隆
    # repo_url = "http://10.1.1.99/python-team/AutoTestMicroservices"
    # project_path = r"C:\Users\<USER>\Desktop\AutoTestMicroservices\media\AutoTestMicroservices"
    # branch = "dev"
    # clone_response = clone_serve(repo_url, project_path, branch)
    # print(clone_response.success)
    # print(clone_response.message)
    # print(clone_response.local_path)

    # # # commit
    # commit_message = "本地测试提交"
    # project_path = r"C:\Users\<USER>\Desktop\AutoTestMicroservices\media\AutoTestMicroservices"
    # commit_response = commit_serve(project_path, commit_message)
    # print(commit_response.success)
    # print(commit_response.message)
    # print(commit_response.commit_id)
    # print(commit_response.code)
    #
    # push_response = push_serve(project_path)
    # print(push_response.success)
    # print(push_response.message)
    # print(push_response.commit_id)
    # print(push_response.code)
    # #