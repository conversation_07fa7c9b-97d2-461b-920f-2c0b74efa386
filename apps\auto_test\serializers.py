from rest_framework import serializers


class SendMsgSerializer(serializers.Serializer):
    receive_email = serializers.Char<PERSON>ield(max_length=255, required=False)
    msg = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255, required=False)


class SendTestExceptionMsgSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)
    test_record_item_id = serializers.IntegerField()
    plan_name = serializers.CharField(max_length=255)
    tester = serializers.Char<PERSON>ield(max_length=255)
    test_case_total = serializers.IntegerField()
    test_case_exec = serializers.IntegerField()
    test_case_ng = serializers.IntegerField()


class SendMachineStorageAlarmMsgSerializer(serializers.Serializer):
    project_number = serializers.Char<PERSON>ield(max_length=255)
    machine = serializers.<PERSON><PERSON><PERSON><PERSON>(max_length=255)
    tester = serializers.Cha<PERSON><PERSON><PERSON>(max_length=255)
    cwd = serializers.Char<PERSON>ield(max_length=255)
    remain_capacity = serializers.CharField(max_length=255)
    tip = serializers.Char<PERSON><PERSON>(max_length=255)


class SendTestCompletedMsgSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)
    plan_name = serializers.CharField(max_length=255)
    tester = serializers.CharField(max_length=255)
    test_case_total = serializers.IntegerField()
    test_case_exec = serializers.IntegerField()
    test_case_ng = serializers.IntegerField()


class AccessStatSerializer(serializers.Serializer):
    page_url = serializers.CharField()
    page_title = serializers.CharField()
    user_id = serializers.CharField()


class GetProjectTestersSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)
