import logging
import traceback
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db import transaction
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.cache import cache
from django.core.serializers import serialize
import json
import os
import re
import shutil
import time
from pathlib import Path

logger = logging.getLogger("code_management")
from apps.code_management.grpc.host_machine_grpc import host_machine_client
from apps.code_management.grpc.git_grpc import git_client
from apps.code_management.models.project_models import ProjectInfoSerializer, CodeProjectInfo, CodeBranchInfo, BranchInfoSerializer
from apps.code_management.models.config_models import (
    NodeTree,
    NodeTreeSerializer,
    UserDefinedConfig,
    UserDefinedOption,
    Level1Group,
    Level2MainParam,
    Level3SubParam,
    MainParamEnum,
    MainParamUnit8,
    SubParamEnumRelation,
    ParamOriginLocation,
    BranchDefinedValue,
    MainSpecialEnum
)
from apps.code_management.views.memory_views import get_dataflash
from users.models import UserFSInfo, User
from utils.fs_service import FSService
from utils.fs_app import fs_app


# local_path = "/home/<USER>/hwcp"
local_path = "F:\project\HWCP"
config_path = "/home/<USER>/hwcp/gRPC_Server/HwcpTest"
config_url = "http://*********/python-team/HwcpTest.git"
config_branch = "main"
sdk_git_path = "http://*********/"


def get_user_email(user):
    """
        获取用户详细信息
    """
    user_fs_info = UserFSInfo.objects.get(employee_number=user)
    logger.info("user_fs_info.access_token:%s", user_fs_info.access_token)
    user_info = User.objects.get(employee_number=user)
    logger.info("user_info.open_id:%s", user_info.open_id)
    fs = FSService()
    f, data = fs.get_user_info(
        token=user_fs_info.access_token,
        open_id=user_info.open_id)

    if not f:
        if data.get("message") == 401:
            raise ValueError({"err_code": 3, "msg": "平台token过期"}, status.HTTP_401_UNAUTHORIZED)
        raise ValueError({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    person_content = data.get("data", {})
    logger.info("person_content:", person_content)

    return person_content['username'], person_content['departmentList'][0]['leaderId']


class ConfigParamsView(APIView):

    # 提交子节点配置信息详情
    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("receive info: %s", params)

            config_value_path = params.get('path')
            config_value = params.get('value')
            workspace_path = params.get('workspace_path')
            branch_status = params.get('branch_status')

            label = params.get("label")
            project_code = params.get('project_code')
            project_name = params.get('project_name')
            project_gitlab = params.get('project_gitlab')
            project_branch = params.get('project_branch')
            node_level = params.get("node_level")
            nodeName = params.get("nodeName")


            # 插入数据库
            belong_query = ParamOriginLocation.objects.using("hwcp").filter(
                param_key=label  # 修正为 param_key（根据您的模型定义）
            ).values_list('belong', flat=True).first()

            if belong_query:
                belong = belong_query

                logger.info(f" 修改的字段属于 {belong} 模块")

                # 使用数据库事务包裹操作
                with transaction.atomic(using="hwcp"):

                    logger.info("{%s: %s}", label, config_value)
                    try:
                        # 尝试查询记录
                        defind_result = BranchDefinedValue.objects.using("hwcp").get(
                            project_code=project_code,
                            project_name=project_name,
                            project_gitlab=project_gitlab,
                            project_branch=project_branch,
                            node_level=node_level,  # 修正为node_level（原代码中是mode_level）
                            var_name=nodeName
                        )
                        # 如果查询到记录，则更新
                        # 如果查询到记录，则更新
                        if belong == "user_defined":
                            # # 如果user_defined是一个字典
                            # if isinstance(defind_result.user_defined, dict):
                            #     # 删除已存在的键
                            #     if label in defind_result.user_defined:
                            #         del defind_result.user_defined[label]
                            #     # 添加新的键值对
                            #     defind_result.user_defined[label] = config_value
                            #
                            # # 如果user_defined是一个字典列表
                            # elif isinstance(defind_result.user_defined, list):
                            #     # 遍历列表中的每个字典
                            #     for item in defind_result.user_defined:
                            #         if isinstance(item, dict) and label in item:
                            #             del item[label]
                            #     # 添加新的字典项
                            #     defind_result.user_defined.append({label: config_value})
                            # 处理空值情况：如果user_defined为空，直接初始化
                            if not defind_result.user_defined:  # 覆盖None、空字典、空列表
                                defind_result.user_defined = {label: config_value}
                            # 如果是字典
                            elif isinstance(defind_result.user_defined, dict):
                                if label in defind_result.user_defined:
                                    del defind_result.user_defined[label]
                                defind_result.user_defined[label] = config_value
                            # 如果是列表
                            elif isinstance(defind_result.user_defined, list):
                                for item in defind_result.user_defined:
                                    if isinstance(item, dict) and label in item:
                                        del item[label]
                                defind_result.user_defined.append({label: config_value})
                            # 处理异常类型（强制转为字典）
                            else:
                                logger.warning(f"user_defined类型异常: {type(defind_result.user_defined)}，强制转为字典")
                                defind_result.user_defined = {label: config_value}

                        elif belong == "variables":

                            # # 如果variables是一个字典
                            # if isinstance(defind_result.variables, dict):
                            #     logger.info("修改的值是个字典")
                            #     # 删除已存在的键
                            #     if label in defind_result.variables:
                            #         del defind_result.variables[label]
                            #     # 添加新的键值对
                            #     defind_result.variables[label] = config_value
                            #
                            # # 如果variables是一个字典列表
                            # elif isinstance(defind_result.variables, list):
                            #     logger.info("修改的值是个列表")
                            #     # 遍历列表中的每个字典
                            #     for item in defind_result.variables:
                            #         if isinstance(item, dict) and label in item:
                            #             del item[label]
                            #     # 添加新的字典项
                            #     defind_result.variables.append({label: config_value})
                            if not defind_result.variables:  # 覆盖None、空字典、空列表
                                defind_result.variables = {label: config_value}
                                # 如果是字典
                            elif isinstance(defind_result.variables, dict):
                                logger.info("修改的值是个字典")
                                if label in defind_result.variables:
                                    del defind_result.variables[label]
                                defind_result.variables[label] = config_value
                                # 如果是列表
                            elif isinstance(defind_result.variables, list):
                                logger.info("修改的值是个列表")
                                for item in defind_result.variables:
                                    if isinstance(item, dict) and label in item:
                                        del item[label]
                                defind_result.variables.append({label: config_value})
                                # 处理异常类型（强制转为字典）
                            else:
                                logger.warning(f"variables类型异常: {type(defind_result.variables)}，强制转为字典")
                                defind_result.variables = {label: config_value}

                        elif belong == "arrays":
                            if not defind_result.arrays:  # 覆盖None、空字典、空列表
                                defind_result.arrays = {label: config_value}
                                # 如果是字典
                            elif isinstance(defind_result.arrays, dict):
                                logger.info("修改的值是个字典")
                                if label in defind_result.arrays:
                                    del defind_result.arrays[label]
                                defind_result.arrays[label] = config_value
                                # 如果是列表
                            elif isinstance(defind_result.arrays, list):
                                logger.info("修改的值是个列表")
                                for item in defind_result.arrays:
                                    if isinstance(item, dict) and label in item:
                                        del item[label]
                                defind_result.arrays.append({label: config_value})
                                # 处理异常类型（强制转为字典）
                            else:
                                logger.warning(f"arrays类型异常: {type(defind_result.arrays)}，强制转为字典")
                                defind_result.arrays = {label: config_value}

                        defind_result.save()

                    except BranchDefinedValue.DoesNotExist:
                        # 如果记录不存在，则创建
                        if belong == "user_defined":
                            BranchDefinedValue.objects.using("hwcp").create(
                                project_code=project_code,
                                project_name=project_name,
                                project_gitlab=project_gitlab,
                                project_branch=project_branch,
                                node_level=node_level,
                                var_name=nodeName,
                                user_defined={label: config_value}
                            )
                        elif belong == "variables":
                            BranchDefinedValue.objects.using("hwcp").create(
                                project_code=project_code,
                                project_name=project_name,
                                project_gitlab=project_gitlab,
                                project_branch=project_branch,
                                node_level=node_level,
                                var_name=nodeName,
                                variables={label: config_value}
                            )

                        elif belong == "arrays":
                            BranchDefinedValue.objects.using("hwcp").create(
                                project_code=project_code,
                                project_name=project_name,
                                project_gitlab=project_gitlab,
                                project_branch=project_branch,
                                node_level=node_level,
                                var_name=nodeName,
                                arrays={label: config_value}
                            )


                    # 构建 SDK 路径
                    sdk_path = os.path.dirname(workspace_path) + "/" + "hiwaysdk_2.0" + '/' + "Application"
                    # 获取当前node的父节点
                    # {'path': 'Brightness/Config_Brightness/DEBUG_INFO_TYPE', 'value': '0x01,0x02,0x04,0x08,0x10', 'workspace_path': '/home/<USER>/hwcp/WPTSN11/mcu-team/hwcp_test/hwcptest036/dev_zhicheng', 'branch_status': '0', 'label': 'DEBUG_INFO_TYPE', 'project_code': 'WPTSN11', 'project_name': '产品自动化测试平台', 'project_gitlab': 'http://*********/mcu-team/hwcp_test/hwcptest036', 'project_branch': 'dev_zhicheng', 'node_level': '5.1', 'nodeName': 'Config_System'}
                    module_name = ""
                    parent_obj = NodeTree.objects.using("hwcp").filter(
                        name=nodeName,
                        level=node_level
                    ).first()
                    if parent_obj:
                        parent_id = parent_obj.parent_id
                        sibling_nodes = NodeTree.objects.using("hwcp").filter(
                            parent_id=parent_id
                        ).values_list('name', 'level', 'node_path', 'file_path')

                        for node_name, node_level_val, node_path, file_path in sibling_nodes:
                            if str(node_level_val) != str(node_level) and str(node_level_val).startswith(str(node_level).split(".")[0]):
                                module_name = node_name
                            else:
                                config_para_path = node_path

                    # 调用grpc前，需将config_value统一成字符串
                    if not isinstance(config_value, str):
                        logger.info("转换 config_value %s 为字符串", config_value)
                        config_value = str(config_value)

                    # 提交配置
                    logger.info(f"转换前 sdk_path, workspace_path, branch_status: {sdk_path, workspace_path, branch_status}")
                    logger.info(f"转换前module_name, nodeName, config_para_path, belong:{module_name, nodeName, config_para_path, belong}")
                    logger.info(f"转换前 label, config_value:{label, config_value}")

                    update_value = config_value
                    # 替换数据
                    params_info = ParamOriginLocation.objects.using("hwcp").get(
                        param_key=label
                    )
                    type = params_info.type

                    if type:
                        special_enum = MainSpecialEnum.objects.using("hwcp").filter(
                            type=type,
                            project_code=project_code,
                            project_name=project_name,
                            project_gitlab=project_gitlab,
                            project_branch=project_branch
                        )
                        special = special_enum.first()
                        for key, value in special.enum_key.items():
                            if key == config_value:
                                update_value = value
                                logger.info(f"label标签：{label}, type 类型是 {type}, 原始值config_value为：{config_value}, 替换后的值：{update_value}")
                    try:
                        # 调用 gRPC 服务
                        logger.info(
                            f"sdk_path, workspace_path, branch_status: {sdk_path, workspace_path, branch_status}")
                        logger.info(
                            f"module_name, nodeName, config_para_path, belong:{module_name, nodeName, config_para_path, belong}")
                        logger.info(f"label, update_value:{label, update_value}")
                        response = host_machine_client.get_configParms_serve(
                            sdk_path, workspace_path, branch_status, module_name, nodeName, config_para_path, belong, label, update_value)

                        logger.info("send host_grpc currentConfigItems, get config modify status:%s", response)

                        # 检查配置状态
                        if response.config_status != 1:
                            logger.error("send host_grpc currentConfigItems, get config modify fail....")
                            # 手动触发回滚
                            raise transaction.TransactionManagementError("配置失败，触发回滚")

                    except Exception as e:
                        # 捕获异常并触发回滚
                        logger.error(f"gRPC 调用失败: {str(e)}")
                        raise transaction.TransactionManagementError("gRPC 调用失败，触发回滚")

                # 如果事务成功提交，返回成功响应
                return Response({"config_status": 1, "message": "配置成功"})

            else:
                logger.warning(f"未找到参数 {label} 的归属信息")
                raise ValueError("未找到参数归属信息")

        except Exception as e:
            logger.info(f"配置提交失败: {str(e)}")
            return Response({"config_status": 0, "message": str(e)})



class ConfigInfoView(APIView):



    def get_userDefined(self, node_name, node_level, project_code, project_name,  project_gitlab, project_branch):
        # 拼接userdefined
        try:
            userDefined = []
            # user_defined_config 中每个key只有一条数据
            config_result = UserDefinedConfig.objects.using("hwcp").filter(
                var_name=node_name,
                node_level=node_level
            )

            for config_item in config_result:
                logger.info(f"组名: {config_item.group}, 描述: {config_item.desc}, 默认值: {config_item.default_value}")

                option_list = UserDefinedOption.objects.using("hwcp").filter(
                    var_name=node_name,
                    node_level=node_level,
                    group=config_item.group
                ).values_list('name', flat=True)
                # 查询项目表
                user_defined_result = BranchDefinedValue.objects.using("hwcp").filter(
                    project_code=project_code,
                    project_name=project_name,
                    project_gitlab=project_gitlab,
                    project_branch=project_branch,
                    var_name=node_name,
                    node_level=node_level
                ).values_list('user_defined', flat=True)
                logger.info(f"user_defined_result：{user_defined_result}")
                default_value = config_item.default_value
                # 查找分支修改的数据
                if user_defined_result and len(user_defined_result) == 1 and user_defined_result[0] is None:
                    logger.info("检测到 user_defined_result 为 <QuerySet [None]>，不执行后续处理")
                else:
                    if user_defined_result:
                        for item in user_defined_result:
                            for key, value in item.items():
                                logger.info(f"键: {key}, 值: {value}")
                                if config_item.group == key:
                                    default_value = value
                                else:
                                    continue

                info = {
                    "name": config_item.group,
                    "default_value": default_value,
                    "desc": config_item.desc,
                    "enum": option_list
                }
                userDefined.append(info)
                logger.info(f"最终 userDefined 结构：{userDefined}")
            return userDefined
        except Exception as e:
            logger.info("Exception: %s", e)
            return []


    def get_variables(self, node_name, node_level, project_code, project_name,  project_gitlab, project_branch):
        try:
            variables = []
            logger.info(f"开始构建变量，node_name={node_name}, node_level={node_level}")

            SPECIAL_TYPES = ["IIC", "PWM", "EXIT", "ADC"]

            variables_result = BranchDefinedValue.objects.using("hwcp").filter(
                project_code=project_code,
                project_name=project_name,
                project_gitlab=project_gitlab,
                project_branch=project_branch,
                var_name=node_name,
                node_level=node_level
            ).values_list('variables', flat=True)
            logger.info(f"variables_result：{variables_result}")
            # 1. 一级分组查询
            level1_result = Level1Group.objects.using("hwcp").filter(
                var_name=node_name,
                node_level=node_level
            )
            logger.info(
                f"一级分组查询结果数：{level1_result.count()}，条件：var_name={node_name}, node_level={node_level}")

            for level1_item in level1_result:
                group_name = level1_item.group
                logger.info(f"===== 处理一级分组：{group_name}（id={level1_item.id}） =====")

                group_data = {"group": group_name, "list": {}}

                # 2. 二级主参数查询（关联一级group）
                level2_result = Level2MainParam.objects.using("hwcp").filter(
                    var_name=node_name,
                    node_level=node_level,
                    group=group_name
                )
                logger.info(f"二级参数查询结果数：{level2_result.count()}，条件：group={group_name}")

                if not level2_result.exists():
                    logger.warning(f"一级分组 {group_name} 下无二级参数！")
                    variables.append(group_data)
                    continue


                for level2_item in level2_result:
                    param_key = level2_item.parent_param_key
                    param_name = level2_item.parent_param_name
                    param_desc = level2_item.desc
                    param_type = level2_item.type
                    # 处理default：为空时设为默认空字符串，避免None
                    logger.info(f"param_type:{param_type}")
                    param_default = level2_item.default_value if level2_item.default_value is not None else ""
                    optional_key = []
                    # 判断是否为特殊类型（PWM/IIC/EXIT）
                    is_special = param_type in SPECIAL_TYPES
                    optional_keys = []  # 统一初始化可选键列表
                    logger.info(f"is_special:%s", is_special)
                    if is_special:
                        logger.info(f"解析 iic, pwm, exit, adc类型 数据")
                        try:
                            logger.info(
                                f"{project_code}, {project_name}, {project_gitlab}, {project_branch}, {param_type}")

                            special_enum = MainSpecialEnum.objects.using("hwcp").filter(
                                type=param_type,
                                project_code=project_code,
                                project_name=project_name,
                                project_gitlab=project_gitlab,
                                project_branch=project_branch
                            )

                            special = special_enum.first()

                            if special:
                                # optional_key 是 JSONField，已自动解析为 dict
                                optional_keys = special.optional_key
                                logger.info(f"optional_key: {optional_keys}")
                            else:
                                logger.warning(f"{param_type} 未查到 MainSpecialEnum 记录")
                                optional_keys = []

                        except Exception as e:
                            logger.error(f"查询 MainSpecialEnum 失败：{str(e)}")
                            optional_keys = []

                    if param_type == "uint8_list_or":
                        logger.info(f"解析uint8_list_or默认值")
                        # 移除所有\r\n换行符和多余空格
                        cleaned_default = re.sub(r'\r\n', '', param_default).strip()
                        logger.info(f"cleaned_default内容: {cleaned_default}")
                        logger.info(f"cleaned_default长度: {len(cleaned_default)}")
                        logger.info("cleaned_default类型: %s", type(cleaned_default))

                        try:
                            # 解析为JSON数组
                            parsed_default = json.loads(cleaned_default)
                            param_default = parsed_default  # 将解析后的列表赋值给param_default
                            logger.info("JSON解析成功")
                            logger.info("解析后的类型: %s", type(param_default))
                            logger.info("解析后的元素数量: %d", len(param_default))
                        except json.JSONDecodeError as e:
                            # 解析失败时记录详细错误信息
                            logger.error(f"解析uint8_list_or默认值失败（{param_key}）：{e}")
                            logger.error(f"错误位置: {e.pos}")
                            logger.error(
                                f"错误附近的文本: {cleaned_default[e.pos - 20:e.pos + 20] if e.pos > 20 else cleaned_default[0:e.pos + 20]}")
                            param_default = []  # 设置为空列表
                            logger.warning(f"已将{param_key}的默认值设置为空列表")
                        except Exception as e:
                            logger.error(f"发生未知错误: {e}")
                            param_default = []  # 设置为空列表
                    logger.info(f"param_type: {param_type},param_key:{param_key}")
                    logger.info(f"最终param_default值: {param_default}")
                    logger.info("最终类型: %s", type(param_default))
                    if variables_result and len(variables_result) == 1 and variables_result[0] is None:
                        logger.info("检测到 user_defined_result 为 <QuerySet [None]>，不执行后续处理")
                    else:
                        if variables_result:
                            for item in variables_result:
                                for key, value in item.items():
                                    if key == param_key:
                                        logger.info(f"键: {key}, 值: {value}")
                                        param_default = value
                                    else:
                                        continue
                    param_data = {
                        "name": param_name,
                        "desc": param_desc,
                        "type": param_type,
                        "default": param_default
                    }
                    if is_special:
                        param_data["optional_keys"] = optional_keys  # 对应前端下拉选项
                        param_data["is_channel"] = True  # 保持与目标结构一致的标记
                    # 枚举类型处理
                    if param_type == "enum":
                        enum_result = MainParamEnum.objects.using("hwcp").filter(
                            param_key=param_key,
                            var_name=node_name,
                            node_level=node_level
                        ).first()
                        logger.info(f"枚举值查询结果：{enum_result}，条件：param_key={param_key}")

                        if enum_result:
                            enum_keys = enum_result.enum_key
                            param_data["enum"] = enum_keys
                            param_data["list"] = dict()

                            # 为枚举类型设置默认值（如第一个枚举项）
                            if not param_data["default"] and enum_keys:
                                param_data["default"] = enum_keys[0]
                                if variables_result:
                                    for item in variables_result:
                                        for key, value in item.items():
                                            if key == param_key:
                                                logger.info(f"键: {key}, 值: {value}")
                                                param_data["default"] = value
                                            else:
                                                continue

                            sub_param_relations = SubParamEnumRelation.objects.using("hwcp").filter(
                                parent_param_key=param_key,
                                var_name=node_name,
                                node_level=node_level
                            )
                            # 处理每个枚举值对应的子参数
                            for sub_param_row in sub_param_relations:
                                enum_value = sub_param_row.parent_enum_key
                                sub_param_keys = sub_param_row.sub_param_key if sub_param_row.sub_param_key else []
                                param_data["list"][enum_value] = []

                                for sub_key in sub_param_keys:
                                    # 获取子参数基本信息
                                    logger.info(f"sub_key: {sub_key}")
                                    sub_param_info = Level3SubParam.objects.using("hwcp").filter(
                                        sub_param_key=sub_key,
                                        var_name=node_name,
                                        node_level=node_level
                                    ).first()
                                    # 获取子参数的min/max/unit（从MainParamUnit8）
                                    sub_unit8_info = MainParamUnit8.objects.using("hwcp").filter(
                                        param_key=sub_key,
                                        var_name=node_name,
                                        node_level=node_level
                                    ).first()
                                    logger.info(f"sub_param_info: {sub_param_info}")
                                    if sub_param_info:
                                        sub_param_type = sub_param_info.param_type
                                        sub_is_special = sub_param_type in SPECIAL_TYPES
                                        sub_optional_keys = []

                                        if sub_is_special:
                                            logger.info(f"检测到特殊类型子参数：{sub_param_type}（{sub_key}）")

                                            # 查询子参数的optional_key
                                            try:
                                                logger.info(f"解析 iic, pwm, exit, adc类型  数据")
                                                logger.info(
                                                    f"{project_code}, {project_name}, {project_gitlab}, {project_branch}, {sub_param_type}")

                                                # 查询子参数记录
                                                sub_special = MainSpecialEnum.objects.using("hwcp").filter(
                                                    type=sub_param_type,
                                                    project_code=project_code,
                                                    project_name=project_name,
                                                    project_gitlab=project_gitlab,
                                                    project_branch=project_branch
                                                ).first()

                                                if sub_special:
                                                    # 直接拿就是 list 类型
                                                    sub_optional_keys = sub_special.optional_key or []
                                                    logger.info(
                                                        f"{sub_param_type} 的 optional_key 值为：{sub_optional_keys}")
                                                else:
                                                    sub_optional_keys = []
                                                    logger.warning("未查询到 MainSpecialEnum 记录")

                                            except Exception as e:
                                                sub_optional_keys = []
                                                logger.error(f"查询子参数失败：{str(e)}")

                                        if variables_result:
                                            for item in variables_result:
                                                for key, value in item.items():
                                                    if key == sub_key:
                                                        logger.info(f"键: {key}, 值: {value}")
                                                        sub_param_info.default_value = value
                                                    else:
                                                        continue
                                        sub_param_dict = {
                                            sub_key: {
                                                "name": sub_param_info.sub_param_name,
                                                "desc": sub_param_info.desc,
                                                "type": sub_param_info.param_type,
                                                "default": sub_param_info.default_value,
                                                # 从MainParamUnit8获取min/max/unit，无则设为None
                                                "min": sub_unit8_info.min if sub_unit8_info else None,
                                                "max": sub_unit8_info.max if sub_unit8_info else None,
                                                "unit": sub_unit8_info.unit if sub_unit8_info else ""
                                            }
                                        }
                                        if sub_is_special:
                                            sub_param_dict[sub_key]["optional_keys"] = sub_optional_keys
                                            sub_param_dict[sub_key]["is_channel"] = True
                                        param_data["list"][enum_value].append(sub_param_dict)
                                    else:
                                        logger.info(f"未查询到子参数 {sub_key} 的基本信息")
                        else:
                            logger.info(f"二级参数 {param_key} 为枚举类型，但未查询到枚举值！")

                    # uint8类型处理
                    elif "uint" in param_type:
                        # 查询uint8对应的范围和单位（取第一条记录）
                        uint8_info = MainParamUnit8.objects.using("hwcp").filter(
                            param_key=param_key,
                            var_name=node_name,
                            node_level=node_level
                        ).first()

                        if uint8_info:
                            param_data["min"] = uint8_info.min
                            param_data["max"] = uint8_info.max
                            # 单位
                            param_data["unit"] = uint8_info.unit
                        else:
                            logger.info(f"二级参数 {param_key}（uint8）未查询到min/max/unit信息")

                    logger.info(f"param_type: {param_type}, param_key:{param_key}, param_default:{param_default}  ")
                    if param_type == "text":
                        if "0x" in param_default:
                            logger.info("0x存在")
                            param_data['type'] = "uint8_hex"
                    # 将参数添加到分组（统一在最后赋值，避免重复）
                    group_data["list"][param_key] = param_data

                variables.append(group_data)

            logger.info(f"最终variables结构：{variables}")
            return variables
        except Exception as e:
            logger.error(f"构建变量失败：{str(e)}")  # 改为error级别便于调试
            return []


    def get_level_info(self, node_name, node_level, project_code, project_name,  project_gitlab, project_branch):

            userDefined = self.get_userDefined(
                node_name, node_level, project_code, project_name,  project_gitlab, project_branch
            )

            variables = self.get_variables(
                node_name, node_level, project_code, project_name,  project_gitlab, project_branch
            )
            # 判断 memeory
            if node_level == "2.1" and node_name == "Config_Memory":
                chip_dataflash = get_dataflash(node_name, node_level, project_code, project_name,  project_gitlab, project_branch)
                if chip_dataflash:
                    variables.insert(0, chip_dataflash)


            if variables:
                return {
                    "userDefined": userDefined,
                    "variables": variables
                }
            else:
                return {}


    def post(self, request):

        # 获取子配置信息

        try:
            params = request.data.get('params', {})
            logger.info("receive info: %s", params)
            parent_name = params.get('parentName')
            node_name = params.get('nodeName')
            node_level = params.get('node_level')
            workspace_path = params.get('workspace_path')
            branch_status = params.get('branch_status')

            project_code = params.get('project_code')
            project_name = params.get('project_name')
            project_gitlab = params.get('project_gitlab')
            project_branch = params.get('project_branch')



            # 子项名称
            current_config_items = str(parent_name + "/" + node_name)
            sdk_path = os.path.dirname(workspace_path) + "/" + "hiwaysdk_2.0" + "/" + "Application"
            config_data = self.get_level_info(node_name, node_level, project_code, project_name,  project_gitlab, project_branch)
            logger.info("config_data: %s", config_data)
            if config_data:
                return Response({
                        "config_status": 1,
                        "message": "配置成功",
                        "config_info": config_data,
                    })
            else:
                return Response(
                    {
                        "config_status": 0,
                        "message": "获取当前节点数据失败"}
                )

        except Exception as e:
            logger.info("Exception: %s", e)
            return Response({"config_status": 0, "message": "Exception：" + str(e)})



class ConfigCommitView(APIView):
    # 执行commit功能
    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("receive info:%s", params)
            commit_message = params.get('commit_message')
            workspace_path = params.get('workspace_path')
            branch_status = params.get('branch_status')

            # commit
            logger.info("当前的 workspace_path:%s", workspace_path)
            logger.info("收到commit信息:%s", commit_message)

            # 调用 git commit 服务
            commit_response = git_client.commit_serve(workspace_path, commit_message)
            logger.info("调用git, commit状态信息:%s", commit_response)

            if commit_response.success:
                return Response({"commit_status": 1, "message": "commit操作成功"})
            else:
                raise ValueError("commit操作失败: " + str(commit_response.message))

        except Exception as e:
            logger.info(f"配置提交失败: {str(e)}")
            return Response({"commit_status": 0, "message": "Exception:" + str(e)})



class ConfigPushView(APIView):
    # push功能
    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("receive info:%s", params)
            workspace_path = params.get('workspace_path')
            branch_status = params.get('branch_status')

            logger.info("当前的 workspace_path:%s", workspace_path)

            # push
            push_response = git_client.push_serve(workspace_path)
            logger.info("调用git, push状态信息: %s", push_response)

            if push_response.success:
                return Response({"push_status": 1, "message": "push操作成功"})

            else:
                raise ValueError(f"push操作失败{push_response.message}")

        except Exception as e:
            logger.info(f"配置提交失败: {str(e)}")
            return Response({"push_status": 0, "message": "Exception:" + str(e)})





class MergeProjectView(APIView):

    """
        merge分支
        飞书消息推送
    """

    # 人员认证
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]



    def send_leader_fs_info(self, leader_email, gitlabs, local_branchs, merge_branch, mr_url):
        """
            发送飞书消息： 给当前 分支创建人 上级 发送 merge请求
                        给当前 创建人 发送分支 已merge 得提示
        """
        # 将信息传入飞书卡片
        leader_result = False
        if leader_email:
            logger.info("即将给: %s 发送合并请求", leader_email)

            leader_result, leader_response = fs_app.send_merge_request_msg(
                leader_email,
                gitlabs.first(),
                local_branchs.first(),
                merge_branch,
                mr_url
            )


        return leader_result



    def send_merge_serve(self, gitlabs, local_branchs, merge_branch):
        """
            执行merge 请求，获得merge的url
        """

        gitlab = str(gitlabs.first()).split(sdk_git_path)[-1]
        local_branch = local_branchs.first()
        logger.info("gitlab:%s, local_branch:%s, merge_branch:%s", gitlab, local_branch, merge_branch)
        response = git_client.merge_serve(gitlab, local_branch, merge_branch)
        logger.info("调用git, merge状态信息: %s", response)
        if response.mr_url and response.mr_iid:
            logger.info("merge 合并的 url: %s,  response.mr_iid:%s", response.mr_url,  response.mr_iid)
        return response.mr_url,  response.mr_iid



    def post(self, request):
        try:
            # 获取参数
            params = request.data.get('params', {})
            logger.info("receive info:%s", params)
            workspace_path = params.get("workspace_path")
            merge_branch = params.get("merge_branch")
            logger.info("workspace_path:%s, merge_branch:%s", workspace_path, merge_branch)
            # 根据工作空间查询分支信息表中的分支和gitlab地址
            project = CodeBranchInfo.objects.filter(
                branch_space=workspace_path
            )
            logger.info("在分支信息表中查询到的数据:%s", project)
            gitlabs = project.values_list('project_gitlab', flat=True).distinct()
            local_branchs = project.values_list('project_branch', flat=True).distinct()
            create_persons = project.values_list('create_person', flat=True).distinct()
            logger.info("gitlabs:%s, local_branchs:%s, create_persons:%s", gitlabs, local_branchs, create_persons)

            user = request.user
            logger.info("user:%s, %s", user, type(user))
            # user = "T050959"
            # 获取当前人，以及当前人员上级得邮箱
            persons_email, leader_email = get_user_email(user)
            logger.info("persons_email: %s, leader_email: %s", persons_email, leader_email)

            # 调用 merge 请求
            mr_url, mr_iid = self.send_merge_serve(gitlabs, local_branchs, merge_branch)

            # 将mr_iid存入数据库
            affected_rows = CodeBranchInfo.objects.filter(branch_space=workspace_path).update(mr_iid=mr_iid)

            if affected_rows == 1:
                logger.info("更新成功")
            elif affected_rows > 1:
                logger.info(f"警告：更新了 {affected_rows} 条记录（可能匹配多个）")
            else:
                logger.info("未找到符合条件的数据")
                raise ValueError("未找到符合条件的数据")

            project = CodeBranchInfo.objects.filter(
                branch_space=workspace_path
            )
            logger.info("插入mr_iid后, 在分支信息表中查询到的数据:%s", project)
            # 给当前分支创建人发送飞书卡片信息
            leader_result = self.send_leader_fs_info(leader_email, gitlabs, local_branchs, merge_branch, mr_url)
            logger.info("leader_result: %s", leader_result)
            # 最终统一返回逻辑
            if leader_result:
                return Response({"merge_status": 1, "message": "给分支创建人的上级发送飞书消息提示成功"})
            else:
                return Response({"merge_status": 0, "message": "给分支创建人的上级发送飞书消息提示失败"})

        except Exception as e:
            logger.info("meger流程失败: %s", str(e))
            return Response({"merge_status": 0, "message": "给分支创建人的上级发送飞书消息提示失败"})





