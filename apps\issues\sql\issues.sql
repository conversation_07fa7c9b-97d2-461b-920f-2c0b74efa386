--问题表
CREATE TABLE IF NOT EXISTS public.issues(
    id serial primary key, --id
    "name" varchar(120),
    project_number varchar(255),
    occur_time timestamp ,
    tester varchar(255),
    machine_number varchar(255),
    images text,
    video varchar(255),
    raw text,  -- 原始值
    status int default 0, -- 状态  0 未处理 1 已推送 2 已取消
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.issues OWNER TO atpms;