from django.urls import path

from .views import (
    PackToolUsageRecordsView, PackToolUsageRecordDetailView, PackToolUsageRecordVerifyView,
    PackageShareUrlsView, PackageShareUrlDetailView, PackageDownloadView,
    PackageMadeByJenkinsView,
)

urlpatterns = [
    path("/records", PackToolUsageRecordsView.as_view()),

    path("/records/made_by_jenkins", PackageMadeByJenkinsView.as_view()),

    path("/records/<int:pk>", PackToolUsageRecordDetailView.as_view()),

    path("/records/verify", PackToolUsageRecordVerifyView.as_view()),

    path("/records/share_urls", PackageShareUrlsView.as_view()),

    path("/records/share_urls/<str:token>", PackageShareUrlDetailView.as_view()),

    path("/records/share_urls/<str:token>/download", PackageDownloadView.as_view()),
]
