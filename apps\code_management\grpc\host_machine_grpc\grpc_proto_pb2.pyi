from google.protobuf import empty_pb2 as _empty_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class WorkSpaceInfo(_message.Message):
    __slots__ = ("sdk_path", "project_path", "branch_status")
    SDK_PATH_FIELD_NUMBER: _ClassVar[int]
    PROJECT_PATH_FIELD_NUMBER: _ClassVar[int]
    BRANCH_STATUS_FIELD_NUMBER: _ClassVar[int]
    sdk_path: str
    project_path: str
    branch_status: str
    def __init__(self, sdk_path: _Optional[str] = ..., project_path: _Optional[str] = ..., branch_status: _Optional[str] = ...) -> None: ...

class GetProjectTreeDataParams(_message.Message):
    __slots__ = ("sdk_path",)
    SDK_PATH_FIELD_NUMBER: _ClassVar[int]
    sdk_path: str
    def __init__(self, sdk_path: _Optional[str] = ...) -> None: ...

class ProjectTreeDataResponse(_message.Message):
    __slots__ = ("module_list", "chip_list")
    MODULE_LIST_FIELD_NUMBER: _ClassVar[int]
    CHIP_LIST_FIELD_NUMBER: _ClassVar[int]
    module_list: _containers.RepeatedScalarFieldContainer[str]
    chip_list: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, module_list: _Optional[_Iterable[str]] = ..., chip_list: _Optional[_Iterable[str]] = ...) -> None: ...

class ConfigChipFunctionsParams(_message.Message):
    __slots__ = ("work_space_info", "chip_type", "functions")
    WORK_SPACE_INFO_FIELD_NUMBER: _ClassVar[int]
    CHIP_TYPE_FIELD_NUMBER: _ClassVar[int]
    FUNCTIONS_FIELD_NUMBER: _ClassVar[int]
    work_space_info: WorkSpaceInfo
    chip_type: str
    functions: str
    def __init__(self, work_space_info: _Optional[_Union[WorkSpaceInfo, _Mapping]] = ..., chip_type: _Optional[str] = ..., functions: _Optional[str] = ...) -> None: ...

class ConfigChipFunctionsResponse(_message.Message):
    __slots__ = ("config_status",)
    CONFIG_STATUS_FIELD_NUMBER: _ClassVar[int]
    config_status: int
    def __init__(self, config_status: _Optional[int] = ...) -> None: ...

class SubFunction(_message.Message):
    __slots__ = ("work_space_info", "project_tree_item")
    WORK_SPACE_INFO_FIELD_NUMBER: _ClassVar[int]
    PROJECT_TREE_ITEM_FIELD_NUMBER: _ClassVar[int]
    work_space_info: WorkSpaceInfo
    project_tree_item: str
    def __init__(self, work_space_info: _Optional[_Union[WorkSpaceInfo, _Mapping]] = ..., project_tree_item: _Optional[str] = ...) -> None: ...

class ProjectSubFunctionItemsResponse(_message.Message):
    __slots__ = ("sub_function_items",)
    SUB_FUNCTION_ITEMS_FIELD_NUMBER: _ClassVar[int]
    sub_function_items: str
    def __init__(self, sub_function_items: _Optional[str] = ...) -> None: ...

class ConfigParams(_message.Message):
    __slots__ = ("work_space_info", "config_para_path", "config_module_name", "config_para_type", "config_para_name", "config_para_value", "module_name")
    WORK_SPACE_INFO_FIELD_NUMBER: _ClassVar[int]
    CONFIG_PARA_PATH_FIELD_NUMBER: _ClassVar[int]
    CONFIG_MODULE_NAME_FIELD_NUMBER: _ClassVar[int]
    CONFIG_PARA_TYPE_FIELD_NUMBER: _ClassVar[int]
    CONFIG_PARA_NAME_FIELD_NUMBER: _ClassVar[int]
    CONFIG_PARA_VALUE_FIELD_NUMBER: _ClassVar[int]
    MODULE_NAME_FIELD_NUMBER: _ClassVar[int]
    work_space_info: WorkSpaceInfo
    config_para_path: str
    config_module_name: str
    config_para_type: str
    config_para_name: str
    config_para_value: str
    module_name: str
    def __init__(self, work_space_info: _Optional[_Union[WorkSpaceInfo, _Mapping]] = ..., config_para_path: _Optional[str] = ..., config_module_name: _Optional[str] = ..., config_para_type: _Optional[str] = ..., config_para_name: _Optional[str] = ..., config_para_value: _Optional[str] = ..., module_name: _Optional[str] = ...) -> None: ...

class UpdateConfigItemResponse(_message.Message):
    __slots__ = ("config_status",)
    CONFIG_STATUS_FIELD_NUMBER: _ClassVar[int]
    config_status: int
    def __init__(self, config_status: _Optional[int] = ...) -> None: ...

class MemoryConfigParams(_message.Message):
    __slots__ = ("work_space_info", "config_para_path", "config_module_name", "config_para_type", "config_para_name", "config_para_value", "module_name", "row_index", "column_index", "struct_member_type", "xml_path")
    WORK_SPACE_INFO_FIELD_NUMBER: _ClassVar[int]
    CONFIG_PARA_PATH_FIELD_NUMBER: _ClassVar[int]
    CONFIG_MODULE_NAME_FIELD_NUMBER: _ClassVar[int]
    CONFIG_PARA_TYPE_FIELD_NUMBER: _ClassVar[int]
    CONFIG_PARA_NAME_FIELD_NUMBER: _ClassVar[int]
    CONFIG_PARA_VALUE_FIELD_NUMBER: _ClassVar[int]
    MODULE_NAME_FIELD_NUMBER: _ClassVar[int]
    ROW_INDEX_FIELD_NUMBER: _ClassVar[int]
    COLUMN_INDEX_FIELD_NUMBER: _ClassVar[int]
    STRUCT_MEMBER_TYPE_FIELD_NUMBER: _ClassVar[int]
    XML_PATH_FIELD_NUMBER: _ClassVar[int]
    work_space_info: WorkSpaceInfo
    config_para_path: str
    config_module_name: str
    config_para_type: str
    config_para_name: str
    config_para_value: str
    module_name: str
    row_index: int
    column_index: int
    struct_member_type: str
    xml_path: str
    def __init__(self, work_space_info: _Optional[_Union[WorkSpaceInfo, _Mapping]] = ..., config_para_path: _Optional[str] = ..., config_module_name: _Optional[str] = ..., config_para_type: _Optional[str] = ..., config_para_name: _Optional[str] = ..., config_para_value: _Optional[str] = ..., module_name: _Optional[str] = ..., row_index: _Optional[int] = ..., column_index: _Optional[int] = ..., struct_member_type: _Optional[str] = ..., xml_path: _Optional[str] = ...) -> None: ...

class UpdateMemoryConfigItemResponse(_message.Message):
    __slots__ = ("config_status",)
    CONFIG_STATUS_FIELD_NUMBER: _ClassVar[int]
    config_status: int
    def __init__(self, config_status: _Optional[int] = ...) -> None: ...

class GetMCUChipInfoParams(_message.Message):
    __slots__ = ("work_space_info", "chip_name", "hal_path")
    WORK_SPACE_INFO_FIELD_NUMBER: _ClassVar[int]
    CHIP_NAME_FIELD_NUMBER: _ClassVar[int]
    HAL_PATH_FIELD_NUMBER: _ClassVar[int]
    work_space_info: WorkSpaceInfo
    chip_name: str
    hal_path: str
    def __init__(self, work_space_info: _Optional[_Union[WorkSpaceInfo, _Mapping]] = ..., chip_name: _Optional[str] = ..., hal_path: _Optional[str] = ...) -> None: ...

class GetMCUChipInfoParamsResponse(_message.Message):
    __slots__ = ("config_status", "MCU_chip_info")
    CONFIG_STATUS_FIELD_NUMBER: _ClassVar[int]
    MCU_CHIP_INFO_FIELD_NUMBER: _ClassVar[int]
    config_status: int
    MCU_chip_info: str
    def __init__(self, config_status: _Optional[int] = ..., MCU_chip_info: _Optional[str] = ...) -> None: ...

class GetGPIOInfoParams(_message.Message):
    __slots__ = ("work_space_info",)
    WORK_SPACE_INFO_FIELD_NUMBER: _ClassVar[int]
    work_space_info: WorkSpaceInfo
    def __init__(self, work_space_info: _Optional[_Union[WorkSpaceInfo, _Mapping]] = ...) -> None: ...

class GetGPIOInfoParamsResponse(_message.Message):
    __slots__ = ("config_status", "gpio_module_info")
    CONFIG_STATUS_FIELD_NUMBER: _ClassVar[int]
    GPIO_MODULE_INFO_FIELD_NUMBER: _ClassVar[int]
    config_status: int
    gpio_module_info: str
    def __init__(self, config_status: _Optional[int] = ..., gpio_module_info: _Optional[str] = ...) -> None: ...

class UpdateGPIOConfigItemParams(_message.Message):
    __slots__ = ("work_space_info", "config_module", "config_item", "config_value", "chip_name", "pin_id", "name")
    WORK_SPACE_INFO_FIELD_NUMBER: _ClassVar[int]
    CONFIG_MODULE_FIELD_NUMBER: _ClassVar[int]
    CONFIG_ITEM_FIELD_NUMBER: _ClassVar[int]
    CONFIG_VALUE_FIELD_NUMBER: _ClassVar[int]
    CHIP_NAME_FIELD_NUMBER: _ClassVar[int]
    PIN_ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    work_space_info: WorkSpaceInfo
    config_module: str
    config_item: str
    config_value: str
    chip_name: str
    pin_id: str
    name: str
    def __init__(self, work_space_info: _Optional[_Union[WorkSpaceInfo, _Mapping]] = ..., config_module: _Optional[str] = ..., config_item: _Optional[str] = ..., config_value: _Optional[str] = ..., chip_name: _Optional[str] = ..., pin_id: _Optional[str] = ..., name: _Optional[str] = ...) -> None: ...

class UpdateGPIOConfigItemParamsResponse(_message.Message):
    __slots__ = ("config_status",)
    CONFIG_STATUS_FIELD_NUMBER: _ClassVar[int]
    config_status: int
    def __init__(self, config_status: _Optional[int] = ...) -> None: ...

class ExportCodeResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: int
    def __init__(self, status: _Optional[int] = ...) -> None: ...
