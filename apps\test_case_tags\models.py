import logging

from django.db import models

logger = logging.getLogger("machines")


class TagModel(models.Model):
    name = models.CharField(max_length=255)
    number = models.CharField(max_length=255)
    desc = models.TextField()
    create_time = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False
        app_label = 'test_case_tags'
        db_table = 'test_case_tags'

    def to_dict(self):
        return {
            "name": self.name,
            "number": self.number,
            "desc": self.desc,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S"),
        }
