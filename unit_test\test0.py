import openpyxl
import requests
import traceback
import pprint
import json
import sys
import re

module_map = {

}

source_map = {
    "问题变更": "TASK_CHANGE",
    "问题解决": "TASK_AFFIRM",
    "新产品开发": "NEW_PROJECT",
}

type_map = {
    "耐久测试": "DURABLE_TEST",
    "性能测试": "PERFORMANCE_TEST",
    "功能测试": "FUNCTION_TEST",
}

generation_method_map = {
    "边界值法": "BOUNDARY_VALUE_METHOD",
    "因果法": "FRUIT_GRAPH_METHOD",
    "判定表驱法": "DECISION_TABLE_DRIVE",
    "功能图法": "FUNCTION_DIAGRAM_METHOD",
    "场景法": "SCENE_METHOD",
    "等价类": "EQUIVALENCE_CLASS",
    "现场经验分析": "FIELD_EXPERIENCE_ANALYSIS",
    "外部和内部接口分析法": "EXTERNAL_AND_INTERNAL_INTERFACE_ANALYSIS",
    "流程分析法": "PROCESS_ANALYSIS",
    "反向分析": "BACKWARD_ANALYSIS",
    "正向分析": "FORWARD_ANALYSIS",
    "环境条件和操作用例分析": "ENVIRONMENTAL_CONDITIONS_AND_OPERATIONAL_USE_CASE_ANALYSIS",
    "错误猜错法": "MISGUESS",
    "序列和来源的分析": "SEQUENCE_AND_SOURCE_ANALYSIS",
    "相依性的常见极限条件": "COMMON_LIMIT_CONDITIONS_4_DEPENDENCE",
    "用例的运行条件分析": "ANALYSIS_OPERATING_CONDITIONS_USE_CASES",
    "基于需求分析": "DEMAND_ANALYSIS",
}

test_method_map = {
    "压力测试": "PRESSURE_TEST",
    "资源使用情况测试": "RESOURCE_USAGE_TESTING",
    "互动/沟通测试": "INTERACTION_COMMUNICATION_TESTING",
    "接口一致性检查": "INTERFACE_CONSISTENCY_CHECK",
    "根据现场经验进行的测试": "TESTS_BASED_ON_FIELD_EXPERIENCE",
    "错误猜测测试": "FALSE_GUESS_TEST",
    "性能测试": "PERFORMANCE_TEST",
    "故障注入测试": "FAULT_INJECTION_TEST",
    "背靠背测试": "BACK_TO_BACK_TESTING",
    "基于接口测试": "INTERFACE_TEST",
    "基于需求测试": "DEMAND_TEST",
}

priority_map = {
    "高": "HIGH",
    "中": "MIDDLE",
    "低": "LOW",
}

session = requests.session()

res = session.post("http://127.0.0.1:9091/users/login2",
                   json={"username": "<EMAIL>", "password": "hwtc@666"})

r = res.json()

if r.get("err_code") != 0:
    sys.exit(1)
else:
    token = r.get("data").get("access_token")
    session.headers.update({"Authorization": f"Bearer {token}"})

r = session.get("http://127.0.0.1:9091/functions")
r = r.json()
data = r.get("data").get("results")

for i in data:
    module_map[i["name"]] = i["number"]
    if i.get("children"):
        for j in i["children"]:
            module_map["{}-{}".format(i["name"], j["name"])] = j["number"]

            if j.get("children"):
                for k in j["children"]:
                    module_map["{}-{}-{}".format(i["name"], j["name"], k["name"])] = k["number"]


workbook = openpyxl.load_workbook('岚图H56C实车测试用例.xlsx')

for sheet in workbook.worksheets:
    if sheet.title.strip() in ["封面", "变更履历", "参考", "总结", "问题统计分析", "模板变更履历", "一般信息"]:
        continue

    count = 0
    for row in sheet.iter_rows(values_only=True):
        count += 1
        if count == 1:
            continue

        if not row[5]:
            print(row)
            continue

        source = source_map.get(row[4])
        module_list = [i for i in row[5].split("/") if i]
        module = module_map.get(module_list[0])
        module_2level = None
        module_3level = None
        if len(module_list) >= 2:
            module_2level = module_map.get("{}-{}".format(module_list[0], module_list[1]))
        if len(module_list) >= 3:
            module_3level = module_map.get("{}-{}-{}".format(module_list[0], module_list[1], module_list[2]))
        module = ", ".join([i for i in [module, module_2level, module_3level] if i])
        type_ = type_map.get(row[6])
        generation_method = generation_method_map.get(row[7])
        test_method = [test_method_map.get(row[8])]
        name = row[9]
        priority = priority_map.get(row[10])
        # execute_mode = row[11]
        execute_mode = "MANUAL_EXECUTION"
        preconditions = row[13]
        remark = ""

        # module = module_map.get(module)
        # preconditions = case.attrib.get("describe")
        # if not preconditions:
        #     preconditions = "t"
        # cycle = int(case.attrib.get("cycle"))
        # vision_revert = case.attrib.get("vision_revert")
        # vision_revert = {"False": False, "True": True}.get(vision_revert)
        # vision_algorithm = case.attrib.get("vision_algorithm")
        # vision_algorithm = {"False": False, "True": True}.get(vision_algorithm)
        # manual_decision = case.attrib.get("manual_decision")
        # execute_mode = {"False": "AUTOMATED_EXECUTION", "True": "MANUAL_EXECUTION"}.get(manual_decision)
        #
        # steps = row[14].split("\n")
        # test_steps = []
        # for step in steps:
        #     step = re.sub(r'^\d+\.', '', step).strip()
        #     if not step:
        #         continue
        #     test_steps.append({
        #         "type": "MANUAL",
        #         "params": json.dumps(""),
        #         "desc": step,
        #     })

        test_steps = [{
            "type": "MANUAL",
            "params": json.dumps(""),
            "desc": row[14],
            "expectation": row[15],
        }]

        #
        d = {
            "name": name,
            "module": module,
            "preconditions": preconditions,
            "remark": remark,
            "project_number": "ILTCF07",
            "project_name": "东风岚图H56C",
            "project_id": "1778408256446136321",
            "type": type_,
            "action_type": "SAT",
            "priority": priority,
            "source": source,
            "generation_method": generation_method,
            "execute_mode": execute_mode,
            "test_method": test_method,
            "cycle": 1,
            "vision_revert": False,
            "vision_algorithm": False,
            "test_steps": test_steps
        }

        # pprint.pprint(d)

        # if not d.get("preconditions").strip():
        #     d["preconditions"] = "1.14.4V电源给simbox和halo分开供电\n2.halo唤醒状态\n3.连接CANoe\n"

        try:
            res = session.post("http://127.0.0.1:9091/test_cases", json=d)

            print(res.json())
            if res.json().get("err_code") != 0:
                print(sheet.title, count)
        except Exception:
            print(traceback.format_exc())
            print(sheet.title, count)

workbook.close()
