from django.urls import path

from .views import (
    TestRecordsView, TestRecordDetailView, TestRecordItemsView, TestRecordItemDetailView,
    TestRecordItemResultDecideView, TestRecordItemFalseAlarmView, IssuePushView, TestRecordItemSourcesView
)

urlpatterns = [
    path("", TestRecordsView.as_view()),

    path("/<int:pk>", TestRecordDetailView.as_view()),

    path("/items", TestRecordItemsView.as_view()),

    path("/items/decide", TestRecordItemResultDecideView.as_view()),

    path("/items/false_alarm", TestRecordItemFalseAlarmView.as_view()),

    path("/items/issue_push", IssuePushView.as_view()),

    path("/items/resources", TestRecordItemSourcesView.as_view()),

    path("/items/<int:pk>", TestRecordItemDetailView.as_view()),
]
