
from django.db import models
from rest_framework import serializers
from django.contrib.postgres.fields import ArrayField



class NodeTree(models.Model):
    NODE_TYPES = (
        ('module', 'Module'),
        ('var', 'Var'),
        ('func', 'Func'),
    )

    id = models.AutoField(primary_key=True)
    parent_id = models.IntegerField()
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=20, choices=NODE_TYPES)
    level = models.CharField(max_length=50)
    des = models.TextField(null=True, blank=True)
    node_path = models.TextField(null=True, blank=True)
    file_path = models.TextField(null=True, blank=True)
    titles = models.JSONField(
        default=list,  # 默认值为空列表
        help_text="标题页签"
    )
    class Meta:
        db_table = 'node_tree'
        app_label = 'node_tree'
        managed = False


class NodeTreeSerializer(serializers.ModelSerializer):
    class Meta:
        model = NodeTree
        fields = '__all__'


class UserDefinedConfig(models.Model):
    id = models.AutoField(primary_key=True)
    var_name = models.CharField(max_length=100, blank=True, null=True)  # SQL未限制NOT NULL，允许为空
    node_level = models.CharField(max_length=50, blank=True, null=True)
    group = models.CharField(max_length=100, blank=True, null=True)
    desc = models.TextField(blank=True, null=True)
    default_value = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        db_table = 'user_defined_config'
        app_label = 'user_defined_config'
        managed = False

class UserDefinedConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserDefinedConfig
        fields = '__all__'


class UserDefinedOption(models.Model):
    id = models.AutoField(primary_key=True)  # 主键为VARCHAR类型
    var_name = models.CharField(max_length=100)  # 严格NOT NULL
    node_level = models.CharField(max_length=50)  # 严格NOT NULL
    group = models.CharField(max_length=100)  # 严格NOT NULL
    config_id = models.IntegerField()  # 关联user_defined_config的id
    name = models.CharField(max_length=100)  # 严格NOT NULL
    desc = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'user_defined_option'
        app_label = 'user_defined_option'
        managed = False

class UserDefinedOptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserDefinedOption
        fields = '__all__'



class Level1Group(models.Model):
    id = models.AutoField(primary_key=True)
    var_name = models.CharField(max_length=100)
    node_level = models.CharField(max_length=50)
    group = models.CharField(max_length=100)
    desc = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'level1_group'
        app_label = 'level1_group'
        managed = False

class Level1GroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = Level1Group
        fields = '__all__'







class Level2MainParam(models.Model):
    id = models.AutoField(primary_key=True)
    var_name = models.CharField(max_length=100)
    node_level = models.CharField(max_length=50)
    group = models.CharField(max_length=100)
    parent_param_key = models.CharField(max_length=100)
    parent_param_name = models.CharField(max_length=100)
    desc = models.TextField(blank=True, null=True)
    type = models.CharField(max_length=50)
    default_value = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        db_table = 'level2_main_param'
        app_label = 'level2_main_param'
        managed = False


class Level2MainParamSerializer(serializers.ModelSerializer):
    class Meta:
        model = Level2MainParam
        fields = '__all__'



class Level3SubParam(models.Model):
    id = models.AutoField(primary_key=True)
    group = models.CharField(max_length=100)
    parent_param_key = models.CharField(max_length=100)
    sub_param_key = models.CharField(max_length=100, unique=True)
    sub_param_name = models.CharField(max_length=100)
    param_type = models.CharField(max_length=50)
    default_value = models.CharField(max_length=100)
    desc = models.TextField(blank=True, null=True)
    var_name = models.CharField(max_length=100)
    node_level = models.CharField(max_length=50)

    class Meta:
        db_table = 'level3_sub_param'
        app_label = 'level3_sub_param'
        managed = False

class Level3SubParamSerializer(serializers.ModelSerializer):
    class Meta:
        model = Level3SubParam
        fields = '__all__'




class MainParamEnum(models.Model):
    id = models.AutoField(primary_key=True)
    param_key = models.CharField(max_length=100)
    enum_key = models.JSONField(blank=False)
    var_name = models.CharField(max_length=100)
    node_level = models.CharField(max_length=50)

    class Meta:
        db_table = 'main_param_enum'
        app_label = 'main_param_enum'
        managed = False

class MainParamEnumSerializer(serializers.ModelSerializer):
    class Meta:
        model = MainParamEnum
        fields = '__all__'




class MainParamUnit8(models.Model):
    id = models.AutoField(primary_key=True)
    param_key = models.CharField(max_length=100)
    max = models.CharField(max_length=100)
    min = models.CharField(max_length=100)
    unit = models.CharField(max_length=100)
    var_name = models.CharField(max_length=100)
    node_level = models.CharField(max_length=50)

    class Meta:
        db_table = 'main_param_unit8'
        app_label = 'main_param_unit8'
        managed = False

class MainParamUnit8Serializer(serializers.ModelSerializer):
    class Meta:
        model = MainParamUnit8
        fields = '__all__'







class SubParamEnumRelation(models.Model):
    id = models.AutoField(primary_key=True)
    parent_param_key = models.CharField(max_length=100)
    parent_enum_key = models.CharField(max_length=100)
    sub_param_key = models.JSONField(blank=False)
    var_name = models.CharField(max_length=100)
    node_level = models.CharField(max_length=50)



    class Meta:
        db_table = 'sub_param_enum_relation'
        app_label = 'sub_param_enum_relation'
        managed = False
class SubParamEnumRelationSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubParamEnumRelation
        fields = '__all__'




class ParamOriginLocation(models.Model):
    id = models.AutoField(primary_key=True)
    var_name = models.CharField(max_length=100)
    node_level = models.CharField(max_length=50)
    param_key = models.CharField(max_length=100)
    BELONG_CHOICES = (
        ('user_defined', '用户自定义'),
        ('variables', '系统变量'),
    )
    belong = models.CharField(max_length=20, choices=BELONG_CHOICES)
    location = models.CharField(max_length=100)
    type = models.CharField(max_length=50)
    class Meta:
        db_table = 'param_origin_location'
        app_label = 'param_origin_location'
        managed = False

class ParamOriginLocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = ParamOriginLocation
        fields = '__all__'



class BranchDefinedValue(models.Model):
    id = models.AutoField(primary_key=True)
    project_code = models.CharField(max_length=50)
    project_name = models.CharField(max_length=100)
    project_gitlab = models.CharField(max_length=255)
    project_branch = models.CharField(max_length=100)
    var_name = models.CharField(max_length=50)
    node_level = models.CharField(max_length=10)
    user_defined = models.JSONField(verbose_name="user_defined")
    variables = models.JSONField(verbose_name="variables")
    arrays = models.JSONField(verbose_name="arrays")
    class Meta:
        db_table = 'branch_defined_value'
        app_label = 'branch_defined_value'
        managed = False

class BranchDefinedValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = BranchDefinedValue
        fields = '__all__'



class MainSpecialEnum(models.Model):
    # id = models.AutoField(primary_key=True)
    # type = models.CharField(max_length=50, verbose_name='字段类型')
    # enum_key = models.JSONField(verbose_name='枚举字典')
    # optional_key = ArrayField(models.CharField(max_length=100), default=list, verbose_name='可选值列表')
    # project_code = models.CharField(max_length=50, blank=True, null=True, verbose_name='项目代号')
    # project_name = models.CharField(max_length=120, blank=True, null=True, verbose_name='项目名称')
    # project_gitlab = models.CharField(max_length=255, blank=True, null=True, verbose_name='GitLab地址')
    # project_branch = models.CharField(max_length=120, blank=True, null=True, verbose_name='项目分支')
    id = models.AutoField(primary_key=True, verbose_name='主键ID')
    type = models.CharField(max_length=50, verbose_name='字段类型')
    enum_key = models.JSONField(verbose_name='枚举字典')
    # 适配PostgreSQL的TEXT[]数组类型，存储字符串列表
    optional_key = ArrayField(
        models.CharField(max_length=100),  # 数组元素为字符串，最大长度100
        default=list,  # 默认值为空列表
        verbose_name='可选值列表'
    )
    project_code = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='项目代号'
    )
    project_name = models.CharField(
        max_length=120,
        blank=True,
        null=True,
        verbose_name='项目名称'
    )
    project_gitlab = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name='GitLab地址'
    )
    project_branch = models.CharField(
        max_length=120,
        blank=True,
        null=True,
        verbose_name='项目分支'
    )
    class Meta:
        db_table = 'main_special_enum'
        app_label = 'main_special_enum'
        managed = False

class MainSpecialEnumSerializer(serializers.ModelSerializer):
    class Meta:
        model = MainSpecialEnum
        fields = '__all__'