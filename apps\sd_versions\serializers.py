from rest_framework import serializers


class GetSdVersionPlansSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)


class CreateSdVersionPlanSerializer(serializers.Serializer):
    title = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    begin_date = serializers.DateField(required=False, allow_null=True)
    end_date = serializers.DateField(required=False, allow_null=True)
    project_name = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255)


class UpdateSdVersionPlanSerializer(serializers.Serializer):
    title = serializers.CharField(max_length=255)
    desc = serializers.Char<PERSON>ield(required=False, allow_blank=True, allow_null=True)
    begin_date = serializers.DateField(required=False, allow_null=True)
    end_date = serializers.DateField(required=False, allow_null=True)
