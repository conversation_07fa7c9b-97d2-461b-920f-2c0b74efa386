--测试用例表
CREATE TABLE IF NOT EXISTS public.test_cases2(
    id serial primary key, --id
    "name" varchar(255),  --名称
    "number" varchar(255) ,   --编号
    "module" varchar(255),  --功能模块
    module_2level varchar(255),  --二级功能模块
    module_3level varchar(255),  --三级功能模块
    "type" varchar(255),  --类型,
    action_type varchar(255),  --活动类型,
    "version" varchar(255),  --用例版本
    status varchar(255),   --状态  PENDING 待评审 REVIEWING 评审中 APPROVED 评审通过 REJECTED 评审不通过 DEPRECATED 废弃 CANCEL 已撤销
    cached_status varchar(255),   --缓存状态
    priority varchar(255),   --优先级
    source varchar(255),   --来源
    generation_method varchar(255),   --生成方法
    test_method varchar(255),   --测试方法
    execute_mode varchar(255),   --执行方式
    function_safe_attrib varchar(255),   --功能安全属性
    preconditions text,  --前置条件
    steps text,  --步骤
    expected text,  --期望结果
    remark text, --备注
    "cycle" int,   --执行次数
    vision_revert Bool,  --视觉检测取反
    vision_algorithm Bool,  --关联视觉算法
    tag varchar(255),   --标签
    project_name varchar(255),   --项目名称
    project_number varchar(255),   --项目编号
    project_id varchar(255),   --项目id
    product_type_id int,   --产品类型ID
    public_test_case_id int,  --公共测试用例ID
    creator_name varchar(255),   --创建人姓名
    creator_employee_number varchar(255),   --创建人工号
    creator_email varchar(255),   --创建人邮箱
    is_deleted boolean default false ,  --是否被删除
    es_source varchar(255),   --企业标准用例来源
    es_id varchar(255),   --企业标准用例ID
    create_time timestamp, --创建时间
    update_time timestamp, --更新时间
    UNIQUE (project_number, "number")
);
ALTER TABLE public.test_cases2 OWNER TO atpms;

--测试用例存档表
CREATE TABLE IF NOT EXISTS public.test_case_archives(
    id serial primary key, --id
    test_case_id int, --测试用例id
    content text, --用例内容
    tag varchar(255),   --标签
    create_time timestamp, --创建时间
    UNIQUE (test_case_id, tag)
);
ALTER TABLE public.test_case_archives OWNER TO atpms;

--测试步骤表
CREATE TABLE IF NOT EXISTS public.test_steps(
    id serial primary key, --id
    test_case_id int, --所属的测试用例id
    "order" int, --序号
    "type" varchar(255),  --类型,
    params text, --步骤参数
    "desc" text, --步骤描述
    expectation text, --期望结果
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.test_steps OWNER TO atpms;

CREATE TABLE IF NOT EXISTS public.test_case_requirement_map(
    test_case_id int, --测试用例id
    test_case_version varchar(255), --测试用例版本
    requirement_id varchar(255), --需求id
    requirement_version varchar(255), --需求版本
    requirement_number varchar(255), --需求编号
    create_time timestamp, --创建时间
    primary key (test_case_id, test_case_version, requirement_id, requirement_version)
);
ALTER TABLE public.test_case_requirement_map OWNER TO atpms;

--公共测试用例表
CREATE TABLE IF NOT EXISTS public.public_test_cases(
   id serial primary key, --id
    "name" varchar(255),  --名称
    "number" varchar(255) unique,   --编号
    "module" varchar(255),  --功能模块
    module_2level varchar(255),  --二机功能模块
    module_3level varchar(255),  --三级功能模块
    "type" varchar(255),  --类型,
    action_type varchar(255),  --活动类型,
    "version" varchar(255),  --用例版本
    status varchar(255),   --状态
    related_requirements bool,   --关联需求
    priority varchar(255),   --优先级
    source varchar(255),   --来源
    generation_method varchar(255),   --生成方法
    test_method varchar(255),   --测试方法
    execute_mode varchar(255),   --执行方式
    function_safe_attrib varchar(255),   --功能安全属性
    preconditions text,  --前置条件
    steps text,  --步骤
    expected text,  --期望结果
    remark text, --备注
    "cycle" int,   --执行次数
    vision_revert Bool,  --视觉检测取反
    vision_algorithm Bool,  --关联视觉算法
    tag varchar(255),   --标签
    project_name varchar(255),   --项目名称
    project_number varchar(255),   --项目编号
    project_id varchar(255),   --项目id
    creator_name varchar(255),   --创建人姓名
    creator_employee_number varchar(255),   --创建人工号
    creator_email varchar(255),   --创建人邮箱
    es_source varchar(255),   --企业标准用例来源
    es_id varchar(255),   --企业标准用例ID
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.public_test_cases OWNER TO atpms;


--测试步骤表
CREATE TABLE IF NOT EXISTS public.public_test_steps(
    id serial primary key, --id
    test_case_id int, --所属的公共测试用例id
    "order" int, --序号
    "type" varchar(255),  --类型,
    params text, --步骤参数
    "desc" text, --步骤描述
    expectation text, --期望结果
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.public_test_steps OWNER TO atpms;


--测试用例统计
CREATE TABLE IF NOT EXISTS public.test_case_stats_weekly(
    id serial primary key, --id
    content text, --统计内容
    stats_time date, --统计时间
    create_time timestamp --创建时间
);
ALTER TABLE public.test_case_stats_weekly OWNER TO atpms;


--测试用例操作日志
CREATE TABLE IF NOT EXISTS public.test_case_operation_log(
    id serial primary key, --id
    project_number varchar(255),
    action_type varchar(255),
    "module" varchar(255),
    "type" varchar(255),
    test_case_id int,
    execute_mode varchar(255),
    operate_method varchar(255),
    operate_time timestamp
);
ALTER TABLE public.test_case_operation_log OWNER TO atpms;