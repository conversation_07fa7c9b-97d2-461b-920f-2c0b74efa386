from rest_framework import serializers


class GetSdRequirementsSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    plan = serializers.IntegerField(required=False)


class CreateSdRequirementSerializer(serializers.Serializer):
    title = serializers.Char<PERSON>ield(max_length=255)
    plan = serializers.IntegerField(required=False, allow_null=True)
    desc = serializers.CharField()
    verify = serializers.CharField()
    verify_method = serializers.ListField(child=serializers.CharField(max_length=255), min_length=1)
    estimate = serializers.DecimalField(max_digits=12, decimal_places=2, default=0)
    pri = serializers.IntegerField(default=3)
    source = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    source_note = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)
    project_name = serializers.Char<PERSON>ield(max_length=255)
    project_number = serializers.Char<PERSON>ield(max_length=255)


class UpdateSdRequirementSerializer(serializers.Serializer):
    plan = serializers.IntegerField(required=False, allow_null=True)
    estimate = serializers.DecimalField(max_digits=12, decimal_places=2)
    pri = serializers.IntegerField()
    source = serializers.CharField(required=False)
    source_note = serializers.CharField(required=False)


class ChangeSdRequirementSerializer(serializers.Serializer):
    title = serializers.CharField(max_length=255)
    desc = serializers.CharField()
    verify = serializers.CharField()
    verify_method = serializers.ListField(child=serializers.CharField(max_length=255), min_length=1)


class GetSdRequirementsVersionDescSerializer(serializers.Serializer):
    requirement = serializers.IntegerField()
    version = serializers.IntegerField()
