import os
import sys
import datetime
import logging
import json

import django

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)
os.chdir(BASE_DIR)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'atpms.settings')
django.setup()

from django.db import close_old_connections

from utils.sql_helper import sql_fetchall_dict, sql_execute

logger = logging.getLogger("test_case_stats")


def stats():
    sql = """
        select project_number, action_type, "module", execute_mode, count(id)
        from public.test_cases2
        where not(is_deleted)
        group by project_number, action_type, "module", execute_mode
        ;
    """
    r = sql_fetchall_dict(sql)

    data = {}
    for i in r:
        if data.get(i["project_number"]) is None:
            data[i["project_number"]] = []
        data[i["project_number"]].append(i)

    sql = """
        insert into public.test_case_stats_weekly (content, stats_time, create_time)
            values (%(content)s, %(stats_time)s, %(create_time)s)
        ;
    """

    params = {"content": json.dumps(data), "stats_time": datetime.date.today(), "create_time": datetime.datetime.now()}
    sql_execute(
        sql,
        params
    )


if __name__ == '__main__':
    try:
        stats()
    except Exception:
        close_old_connections()
