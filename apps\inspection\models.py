from django.db import models, transaction


class InspectionItem(models.Model):
    parent_id = models.IntegerField(default=0)
    path = models.CharField(max_length=255)
    level = models.IntegerField(default=1)

    name = models.CharField(max_length=255)
    code = models.CharField(max_length=255, unique=True)
    desc = models.TextField(blank=True, default='')
    type = models.CharField(max_length=255, default='item')

    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inspection_items'
        managed = False
        app_label = "inspection"


class InspectionModel:
    def create_item(self, **kwargs):
        with transaction.atomic():
            parent_id = kwargs.get("parent_id")
            if parent_id == 0:
                path = "/"
                level = 1
            else:
                parent = InspectionItem.objects.get(id=parent_id)
                path = parent.path + "/"
                level = parent.level + 1
                if parent.type == "item":
                    parent.type = "group"
                    parent.save()
            item = InspectionItem(
                parent_id=parent_id,
                path=path,
                level=level,
                name=kwargs.get("name"),
                code=kwargs.get("code"),
                desc=kwargs.get("desc"),
            )
            item.save()
            item.path += str(item.id)
            item.save()

        return item

    def get_items(self, **kwargs):

        items = InspectionItem.objects.all().order_by('level', 'id')

        data_tree = {}
        for i in items:
            data_tree[i.id] = {
                "id": i.id,
                "name": i.name,
                "code": i.code,
                "parent_id": i.parent_id,
                "path": i.path,
                "level": i.level,
                "desc": i.desc,
                "type": i.type,
                "children": []
            }
        tree = []
        for item_id, item in data_tree.items():
            if item["parent_id"] == 0:
                tree.append(item)
            elif item["parent_id"] in data_tree:
                data_tree[item["parent_id"]]["children"].append(item)

        return {"results": tree}

    def get_item(self, pk):
        item = InspectionItem.objects.get(id=pk)
        return {
            "id": item.id,
            "name": item.name,
            "code": item.code,
            "parent_id": item.parent_id,
            "path": item.path,
            "level": item.level,
            "desc": item.desc,
            "type": item.type
        }

    def update_item(self, pk, **kwargs):
        item = InspectionItem.objects.get(id=pk)
        item.name = kwargs.get("name")
        item.desc = kwargs.get("desc")
        item.save()

    def delete_item(self, pk):
        exists = InspectionItem.objects.filter(parent_id=pk).exists()
        if exists:
            return False, "该项下还有子项，无法删除。"

        item = InspectionItem.objects.get(id=pk)
        item.delete()
        return True, ""
