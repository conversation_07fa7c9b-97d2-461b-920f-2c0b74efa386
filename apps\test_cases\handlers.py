from django.dispatch import receiver

from .signals import (test_case_create_signal, test_case_delete_signal, test_case_update_signal,
                      test_case_review_signal, test_case_reviewed_signal)
from .models import TestCaseOperationLogModel


@receiver(test_case_create_signal)
def test_case_create_handler(sender, signal, **kwargs):
    project_number = kwargs.get("project_number")
    action_type = kwargs.get("action_type")
    module = kwargs.get("module")
    type_ = kwargs.get("type")
    execute_mode = kwargs.get("execute_mode")
    test_case_id = kwargs.get("test_case_id")

    TestCaseOperationLogModel.objects.create(
        project_number=project_number,
        action_type=action_type,
        module=module,
        type=type_,
        execute_mode=execute_mode,
        test_case_id=test_case_id,
        operate_method="CREATE",
    )


@receiver(test_case_delete_signal)
def test_case_delete_handler(sender, signal, **kwargs):
    project_number = kwargs.get("project_number")
    action_type = kwargs.get("action_type")
    module = kwargs.get("module")
    type_ = kwargs.get("type")
    execute_mode = kwargs.get("execute_mode")
    test_case_id = kwargs.get("test_case_id")

    TestCaseOperationLogModel.objects.create(
        project_number=project_number,
        action_type=action_type,
        module=module,
        type=type_,
        execute_mode=execute_mode,
        test_case_id=test_case_id,
        operate_method="DELETE",
    )


@receiver(test_case_update_signal)
def test_case_update_handler(sender, signal, **kwargs):
    project_number = kwargs.get("project_number")
    action_type = kwargs.get("action_type")
    module = kwargs.get("module")
    type_ = kwargs.get("type")
    execute_mode = kwargs.get("execute_mode")
    test_case_id = kwargs.get("test_case_id")

    TestCaseOperationLogModel.objects.create(
        project_number=project_number,
        action_type=action_type,
        module=module,
        type=type_,
        execute_mode=execute_mode,
        test_case_id=test_case_id,
        operate_method="UPDATE",
    )


@receiver(test_case_review_signal)
def test_case_review_handler(sender, signal, **kwargs):
    test_cases = kwargs.get("test_cases")
    for tc in test_cases:
        project_number = tc.get("project_number")
        action_type = tc.get("action_type")
        module = tc.get("module")
        type_ = tc.get("type")
        execute_mode = tc.get("execute_mode")
        test_case_id = tc.get("id")

        TestCaseOperationLogModel.objects.create(
            project_number=project_number,
            action_type=action_type,
            module=module,
            type=type_,
            execute_mode=execute_mode,
            test_case_id=test_case_id,
            operate_method="REVIEW",
        )


@receiver(test_case_reviewed_signal)
def test_case_reviewed_handler(sender, signal, **kwargs):
    test_cases = kwargs.get("test_cases")
    for tc in test_cases:
        project_number = tc.get("project_number")
        action_type = tc.get("action_type")
        module = tc.get("module")
        type_ = tc.get("type")
        execute_mode = tc.get("execute_mode")
        test_case_id = tc.get("id")

        TestCaseOperationLogModel.objects.create(
            project_number=project_number,
            action_type=action_type,
            module=module,
            type=type_,
            execute_mode=execute_mode,
            test_case_id=test_case_id,
            operate_method="REVIEWED",
        )
