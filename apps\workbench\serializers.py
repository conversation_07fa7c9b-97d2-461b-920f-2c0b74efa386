from rest_framework import serializers


class GetProductVersionsSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    status_list = serializers.ListField(child=serializers.Char<PERSON>ield(max_length=255), required=False, min_length=0)
    is_team = serializers.BooleanField(default=False)
    team_members = serializers.ListField(child=serializers.CharField(max_length=255), required=False, min_length=0)
    project_number = serializers.CharField(max_length=255)


class GetProductVersionsSerializer_init(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    status_list = serializers.ListField(child=serializers.Char<PERSON>ield(max_length=255), required=False, min_length=0)
    is_team = serializers.BooleanField(default=False)
    team_members = serializers.ListField(child=serializers.CharField(max_length=255), required=False, min_length=0)







