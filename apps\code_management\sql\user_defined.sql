-- 1. 配置项定义表
CREATE TABLE public.user_defined_config (
    id SERIAL PRIMARY KEY,
    var_name VARCHAR,
    node_level VARCHAR,
    "group" VARCHAR,
    "desc" VARCHAR,
    default_value VARCHAR
);

-- 2. 配置项选项表
CREATE TABLE public.user_defined_option (
    id SERIAL PRIMARY KEY,
    var_name VARCHAR NOT NULL,
    node_level VARCHAR NOT NULL,
    "group" VARCHAR NOT NULL,
    config_id VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    "desc" TEXT
);


