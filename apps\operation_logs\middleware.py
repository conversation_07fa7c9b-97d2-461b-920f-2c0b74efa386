import logging
import time
import traceback
import json

from django.utils.deprecation import MiddlewareMixin
from .models import OperationLog

logger = logging.getLogger("operation_logs")


class OperationLogMiddleware(MiddlewareMixin):
    def process_request(self, request):
        try:
            request.start_time = time.time()

            request.request_data = None
            method = request.method
            path = request.path
            if (path.startswith("/test_cases")
                    and method in ['POST', 'PUT', 'DELETE']):
                request.request_data = json.dumps(json.loads(request.body))
        except Exception:
            logger.error(traceback.format_exc())

    def process_response(self, request, response):
        try:
            response_time = time.time() - request.start_time
            is_success = response.status_code < 400

            user = request.user if request.user.is_authenticated else None
            user_email = user.email if user else None
            user_name = user.username if user else None
            action = f'{request.method} {request.path}'
            method = request.method
            path = request.path
            ip_address = request.META.get('REMOTE_ADDR')
            status_code = response.status_code
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            request_data = None
            response_data = None
            if (path.startswith("/test_cases")
                    and method in ['POST', 'PUT', 'DELETE']):
                try:
                    request_data = request.request_data
                    response_data = response.content.decode('utf-8')
                except Exception:
                    pass

            OperationLog.objects.create(
                user_email=user_email,
                user_name=user_name,
                action=action,
                method=method,
                path=path,
                ip_address=ip_address,
                status_code=status_code,
                response_time=response_time,
                is_success=is_success,
                user_agent=user_agent,
                request_data=request_data,
                response_data=response_data,
            )
        except Exception:
            logger.error(traceback.format_exc())

        return response

    def process_exception(self, request, exception):
        try:
            user = request.user if request.user.is_authenticated else None
            user_email = user.email if user else None
            user_name = user.username if user else None
            action = f'{request.method} {request.path}'
            method = request.method
            path = request.path
            ip_address = request.META.get('REMOTE_ADDR')
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            OperationLog.objects.create(
                user_email=user_email,
                user_name=user_name,
                action=action,
                method=method,
                path=path,
                ip_address=ip_address,
                status_code=500,
                response_time=time.time() - request.start_time,
                is_success=False,
                exception=str(exception),
                user_agent=user_agent
            )
        except Exception:
            logger.error(traceback.format_exc())
