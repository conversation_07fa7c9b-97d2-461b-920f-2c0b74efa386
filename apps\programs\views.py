import traceback
import logging
import os
import uuid

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.files.storage import FileSystemStorage
from django.conf import settings

from .serializers import (
    ProgramListSerializer, ProgramSerializer, ProgramUpdateSerializer,
    ProgramVersionListSerializer, ProgramVersionSerializer, ProgramVersionUpdateSerializer,
    LastProgramSerializer
)
from .models import ProgramModel, ProgramVersionModel

logger = logging.getLogger("machine")

program_fs = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, "programs"), base_url="/media/programs")


class ProgramsView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = ProgramListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = ProgramModel()
            content = model.list(**serializer.validated_data)

            for i in content["results"]:
                if i["icon"]:
                    i["icon"] = program_fs.url(i["icon"])

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = ProgramSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            icon = serializer.validated_data.get("icon")
            if icon:
                dir_name = str(uuid.uuid4())
                icon_path = program_fs.save(os.path.join(dir_name, icon.name), icon)
            else:
                icon_path = None

            model = ProgramModel()
            model.create(
                **serializer.validated_data, icon_path=icon_path
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProgramDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = ProgramModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = ProgramUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            icon = serializer.validated_data.get("icon")
            if icon:
                dir_name = str(uuid.uuid4())
                icon_path = program_fs.save(os.path.join(dir_name, icon.name), icon)
            else:
                icon_path = None

            model = ProgramModel()
            model.update(pk=pk, **serializer.data, icon_path=icon_path)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            model = ProgramModel()
            model.delete(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProgramVersionsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = ProgramVersionListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = ProgramVersionModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = ProgramVersionSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = ProgramVersionModel()
            model.create(
                **serializer.validated_data,
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProgramVersionDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = ProgramVersionModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = ProgramVersionUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = ProgramVersionModel()
            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            user = request.user

            model = ProgramVersionModel()
            model.disable(pk=pk)
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class LastProgramVersionView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = LastProgramSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            program_number = serializer.validated_data.get("program_number")

            model = ProgramVersionModel()
            content = model.last_version(program_number)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
