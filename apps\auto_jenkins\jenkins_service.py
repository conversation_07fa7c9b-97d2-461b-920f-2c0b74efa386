import jenkins
import time
import json
import threading
import uuid
import os
import xml.etree.ElementTree as ET
from datetime import datetime
from django.core.cache import cache
from django.conf import settings
import logging
import requests
from jenkins import Jenkins
logger = logging.getLogger("auto_jenkins")


class JenkinsService:
    def __init__(self):
        
        # Jenkins 服务器信息 - 支持从环境变量或配置文件读取
        self.user = os.getenv('JENKINS_USER', 'jid')
        self.token = os.getenv('JENKINS_TOKEN', '110d936c0bf69044f678b609cec5c29c02')  # 使用API Token
        self.url = os.getenv('JENKINS_URL', 'http://10.1.1.82/')
        # 任务名称（普通流水线项目，不是多分支流水线）
        self.job_name = os.getenv('JENKINS_JOB_NAME', 'hw-collectx')
        self.server = Jenkins(
            self.url,
            username=self.user,
            password=self.token
        )
    
    def create_task_id(self):
        """生成唯一任务ID"""    
        return str(uuid.uuid4())
    
    def get_task_data(self, task_id):
        """从缓存获取任务数据"""
        return cache.get(f"jenkins_task:{task_id}")
    
    def update_task_data(self, task_id, data):
        """更新缓存中的任务数据"""
        cache.set(f"jenkins_task:{task_id}", data, timeout=3600)  # 1小时过期
    
    def start_jenkins_job(self, job_name: str, parameters=None):
        """触发任意 Jenkins Job 并建立任务监控

        参数:
            job_name: Jenkins 中的任务名称
            parameters: 构建参数 dict，默认为空
        返回:
            (task_id, queue_id)
        """
        if parameters is None:
            parameters = {}

        # 生成任务ID并写入初始缓存
        task_id = self.create_task_id()
        task_data = {
            "task_id": task_id,
            "status": "queued",
            "jenkins_build_number": None,
            "logs": [f"准备触发Jenkins任务: {job_name}"],
            "progress": {
                "current_stage": "等待构建开始",
                "total_stages": 3,
                "completed_stages": 0
            },
            "start_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": None,
            "error_message": None,
            "build_params": parameters,
            "jenkins_job_name": job_name
        }
        self.update_task_data(task_id, task_data)
        
        try:
            # 触发 Jenkins 构建
            logger.info(f"触发 Jenkins 构建: {job_name}, 参数: {parameters}")
            queue_id = self.server.build_job(job_name, parameters)
            logger.info(f"构建已进入队列: {queue_id}")
            # 记录队列信息
            self._update_task_status(task_id, "queued", "等待构建开始", [f"已触发Jenkins任务，队列号: {queue_id}"])

            # 后台线程监控构建
            thread = threading.Thread(
                    target=self._execute_jenkins_job_by_queue,
                    args=(task_id, job_name, queue_id),
                daemon=True
            )
            thread.start()
        
            return task_id, queue_id
        except Exception as e:
            logger.error(f"触发 Jenkins 构建失败: {str(e)}", exc_info=True)
            self._update_task_status(task_id, "failed", "触发构建失败", [str(e)])
            raise
    
    def _update_task_status(self, task_id, status, current_stage, new_logs=None, total_steps=None, completed_steps=None):
        """更新任务状态"""
        task_data = self.get_task_data(task_id)
        if not task_data:
            return
            
        task_data["status"] = status
        task_data["progress"]["current_stage"] = current_stage

        # 若提供了进度数字，则同步写入
        if total_steps is not None:
            task_data["progress"]["total_stages"] = total_steps
        if completed_steps is not None:
            task_data["progress"]["completed_stages"] = completed_steps
        
        if new_logs:
            timestamp = datetime.now().strftime('%H:%M:%S')
            for log in new_logs:
                task_data["logs"].append(f"[{timestamp}] {log}")
        
        if status in ["success", "failed"]:
            task_data["end_time"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 确保完成时写满 completed_stages
            task_data["progress"]["completed_stages"] = task_data["progress"].get("total_stages", 0)
        
        self.update_task_data(task_id, task_data)
    
    def _get_build_number_from_queue(self, task_id, queue_id, timeout=300):
        """通过队列ID获取构建号"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                queue_item = self._get_queue_item(queue_id)
                if queue_item and 'executable' in queue_item and queue_item['executable']:
                    build_number = queue_item['executable']['number']
                    self._update_task_status(task_id, "running", "构建已开始", 
                                           [f"构建已开始，构建号: {build_number}"])
                    return build_number
            except Exception as e:
                self._update_task_status(task_id, "running", "等待构建开始", 
                                       [f"获取队列信息: {str(e)}"])
            
            time.sleep(2)
        return None
    
    def _call_wfapi(self, endpoint):
        """调用 Jenkins Workflow API"""
        try:
            logger.info(f"调用 wfapi: {endpoint}")
            url = f"{self.url.rstrip('/')}{endpoint}"
            req = requests.Request("GET", url)
            response = self.server.jenkins_request(req)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"wfapi调用成功，返回数据长度: {len(str(result))}")
                return result
            elif response.status_code == 404:
                logger.warning(f"wfapi endpoint不存在: {endpoint}")
                return None
            else:
                logger.error(f"wfapi调用失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"调用wfapi异常: {str(e)}")
            return None
    
    def _get_pipeline_steps_with_polling(self, task_id, job_name, build_number, poll_interval=3, max_timeout=1800):
        """轮询获取流水线步骤信息"""
        start_time = time.time()
        last_log_update = time.time()
        last_progress_update = ""
        
        while time.time() - start_time < max_timeout:
            try:
                # 检查构建是否完成
                build_info = self.server.get_build_info(job_name, build_number)
                is_building = build_info.get('building', False)
                
                # 获取流水线步骤
                api_job_path = job_name.replace('/', '/job/')
                wf_data = self._call_wfapi(f"/job/{api_job_path}/{build_number}/wfapi/describe")
                
                if wf_data:
                    steps = self._parse_wfapi_steps(wf_data)
                    
                    # 计算进度数字
                    total_steps = len(steps)
                    completed_steps = sum(1 for st in steps if st['step_status'] in ['SUCCESS', 'FAILURE', 'FAILED'])
                    
                    # 更新进度
                    current_progress = self._get_current_progress(steps)
                    if current_progress != last_progress_update:
                        last_progress_update = current_progress
                        self._update_task_status(
                            task_id,
                            "running",
                            "执行中",
                            [current_progress],
                            total_steps=total_steps,
                            completed_steps=completed_steps
                        )
                    
                    # 如果构建完成，返回步骤信息
                    if not is_building:
                        self._update_task_status(task_id, "running", "构建完成", 
                                               [f"构建完成，分析结果中..."])
                        return steps
                else:
                    # 如果无法获取 wfapi 数据，至少更新状态
                    if time.time() - last_log_update > 30:  # 30秒更新一次
                        self._update_task_status(task_id, "running", "执行中", 
                                               [f"构建进行中，构建号: {build_number}"])
                        last_log_update = time.time()
                
                time.sleep(poll_interval)
                
            except Exception as e:
                logger.error(f"轮询步骤信息失败: {str(e)}")
                time.sleep(poll_interval)
        
        # 超时处理
        logger.warning(f"轮询超时，构建号: {build_number}")
        self._update_task_status(task_id, "running", "轮询超时", 
                               [f"获取步骤信息超时，但构建可能仍在进行"])
        return []
    
    def _get_current_progress(self, steps):
        """获取当前进度描述"""
        if not steps:
            return "准备中..."
        
        total_steps = len(steps)
        completed_steps = sum(1 for step in steps if step['step_status'] in ['SUCCESS', 'FAILURE', 'FAILED'])
        running_steps = [step for step in steps if step['step_status'] in ['IN_PROGRESS', 'RUNNING']]
        
        if running_steps:
            return f"正在执行: {running_steps[0]['step_name']} ({completed_steps}/{total_steps})"
        else:
            return f"进度: {completed_steps}/{total_steps} 步骤完成"
    
    def _parse_wfapi_steps(self, wf_data):
        """解析 wfapi 返回的步骤数据"""
        steps = []
        try:
            if 'stages' in wf_data:
                for stage in wf_data['stages']:
                    step_name = stage.get('name', '未知步骤')
                    step_status = stage.get('status', 'UNKNOWN')
                    step_duration = stage.get('durationMillis', 0)
                    
                    # 解析子步骤
                    stage_steps = []
                    if 'stageFlowNodes' in stage:
                        for node in stage['stageFlowNodes']:
                            if node.get('name') and node.get('name') != step_name:
                                stage_steps.append({
                                    'name': node.get('name'),
                                    'status': node.get('status', 'UNKNOWN'),
                                    'duration': node.get('durationMillis', 0)
                                })
                    
                    steps.append({
                        'step_name': step_name,
                        'step_status': step_status,
                        'step_duration': step_duration,
                        'sub_steps': stage_steps
                    })
        except Exception as e:
            logger.error(f"解析wfapi步骤数据失败: {str(e)}")
        
        return steps
    
    def _trigger_build(self, parameters):
        """触发构建任务"""
        try:
            logger.info(f"触发构建任务: {self.job_name}, 参数: {parameters}")
            
            # 使用 python-jenkins 触发构建
            queue_id = self.server.build_job(self.job_name, parameters)
            
            logger.info(f"构建触发成功，队列ID: {queue_id}")
            return queue_id
                
        except Exception as e:
            logger.error(f"触发构建失败: {str(e)}")
            return None
    
    def _get_queue_item(self, queue_id):
        """获取队列项目信息（优先精确查询）"""
        try:
            # python-jenkins 提供精准接口，可直接返回单个 queue item
            item = self.server.get_queue_item(queue_id)
            if item:
                return item
        except Exception as e:
            logger.warning(f"server.get_queue_item 失败，回退到扫描列表: {str(e)}")

        # 回退方案：遍历队列列表
        try:
            for itm in self.server.get_queue_info():
                if itm.get('id') == queue_id:
                    return itm
        except Exception as e:
            logger.error(f"扫描队列列表失败: {str(e)}")
            return None

    def check_job_exists(self, job_name):
        """检查任务是否存在"""
        try:
            return self.server.job_exists(job_name)
        except Exception as e:
            logger.error(f"检查任务是否存在失败: {str(e)}")  
            return False

    def _generate_parameter_definitions(self, pipeline_config):
        """从 pipeline_config 生成参数定义XML（仅支持string类型）"""
        param_definitions = []
        
        try:
            # 强制添加 task_id 参数定义
            param_definitions.append(f"""        <hudson.model.StringParameterDefinition>
          <name>task_id</name>
          <defaultValue></defaultValue>
          <description>build task id</description>
        </hudson.model.StringParameterDefinition>""")
        
            # 从 pipeline_config 的 parameters 数组中提取参数
            parameters = pipeline_config.get('parameters', [])
            
            for param in parameters:
                param_name = param.get('name', '')
                param_type = param.get('type', 'string')
                default_value = param.get('default_value', '')
                description = param.get('description', '')
                
                # 只处理string类型参数
                if param_type == 'string':
                    # 处理Windows路径中的反斜杠和XML特殊字符
                    if isinstance(default_value, str):
                        default_value = default_value.replace("\\", "\\\\")
                        # XML 特殊字符转义
                        default_value = default_value.replace("&", "&amp;")
                        default_value = default_value.replace("<", "&lt;")
                        default_value = default_value.replace(">", "&gt;")
                        default_value = default_value.replace('"', "&quot;")
                        default_value = default_value.replace("'", "&apos;")
                    
                    param_definitions.append(f"""        <hudson.model.StringParameterDefinition>
          <name>{param_name}</name>
          <defaultValue>{default_value}</defaultValue>
          <description>{description}</description>
        </hudson.model.StringParameterDefinition>""")
                else:
                    logger.warning(f"跳过非string类型参数: {param_name} (类型: {param_type})")
            
            # 从 pipeline_config 的 environment 对象中提取环境变量作为参数
            environment = pipeline_config.get('environment', {})
            if environment:
                for env_key, env_value in environment.items():
                    # 处理环境变量值中的反斜杠和XML特殊字符
                    if isinstance(env_value, str):
                        env_value = env_value.replace("\\", "\\\\")
                        # XML 特殊字符转义
                        env_value = env_value.replace("&", "&amp;")
                        env_value = env_value.replace("<", "&lt;")
                        env_value = env_value.replace(">", "&gt;")
                        env_value = env_value.replace('"', "&quot;")
                        env_value = env_value.replace("'", "&apos;")
                    
                    param_definitions.append(f"""        <hudson.model.StringParameterDefinition>
          <name>{env_key}</name>
          <defaultValue>{env_value}</defaultValue>
          <description>环境变量: {env_key}</description>
        </hudson.model.StringParameterDefinition>""")
                    logger.info(f"添加环境变量参数: {env_key} = {env_value}")
            
        except Exception as e:
            logger.error(f"生成参数定义失败: {str(e)}")
        
        return '\n'.join(param_definitions)
    
    
    def create_or_update_pipeline_job(self, job_name, jenkinsfile_content, git_url, pipeline_config,branch, engineering_path=None, engineering_group=None):
        """创建或更新流水线任务"""
        try:
            # 如果有工程组，构建完整的job路径
            if engineering_group:
                full_job_name = f"{engineering_group}/{job_name}"
                logger.info(f"创建或更新流水线任务: {job_name} (工程组: {engineering_group})")
            else:
                full_job_name = job_name
                logger.info(f"创建或更新流水线任务: {job_name}")
            
        
                        # 生成参数定义
            param_definitions = self._generate_parameter_definitions(pipeline_config)

            # -------- 参数定义 --------
            if param_definitions:
                properties_section = f"""  <properties>
    <hudson.model.ParametersDefinitionProperty>
      <parameterDefinitions>
{param_definitions}
      </parameterDefinitions>
    </hudson.model.ParametersDefinitionProperty>
  </properties>"""
            else:
                properties_section = "  <properties/>"

            # -------- Jenkinsfile Script --------
            safe_jenkinsfile_content = jenkinsfile_content.replace("]]>", "]]]]>]]><![CDATA[>")
            script_section = f"<script><![CDATA[{safe_jenkinsfile_content}]]></script>"

            # 构建最终 XML 配置
            xml_config = f'''<?xml version='1.1' encoding='UTF-8'?>
<flow-definition plugin="workflow-job">
  <description>Auto-generated pipeline job</description>
{properties_section}
  <triggers/>
  <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition" plugin="workflow-cps">
    {script_section}
    <sandbox>true</sandbox>
  </definition>
  <disabled>false</disabled>
</flow-definition>'''
                            
            # 创建或更新任务 (使用完整路径)
            if self.server.job_exists(full_job_name):
                logger.info(f"更新现有任务: {full_job_name}")
                self.server.reconfig_job(full_job_name, xml_config)
            else:
                logger.info(f"创建新任务: {full_job_name}")
                self.server.create_job(full_job_name, xml_config)

            
            logger.info(f"流水线任务创建/更新成功: {full_job_name}")
            return True
            
        except Exception as e:
            print(e)
            logger.error(f"创建或更新流水线任务失败: {str(e)}")
            return False

    def get_job_info(self, job_name):
        """获取任务信息"""
        try:
            return self.server.get_job_info(job_name)
        except Exception as e:
            logger.error(f"获取任务信息失败: {str(e)}")
            return None


    def _execute_jenkins_job_by_queue(self, task_id, job_name, queue_id):
        """通过队列ID监控Jenkins任务执行"""
        try:
            # 更新状态：等待构建开始
            self._update_task_status(task_id, "queued", "等待构建开始", 
                                   [f"已触发Jenkins任务,队列号: {queue_id}"])
            
            # 获取构建号
            build_number = self._get_build_number_from_queue(task_id, queue_id)
            if not build_number:
                self._update_task_status(task_id, "failed", "获取构建号失败", 
                                       ["无法获取Jenkins构建号,任务超时"])
                return
            
            # 更新构建号
            task_data = self.get_task_data(task_id)
            if task_data:
                task_data["jenkins_build_number"] = build_number
                self.update_task_data(task_id, task_data)
            
            # 开始轮询监控流水线状态
            self._update_task_status(task_id, "running", "执行中", 
                                   [f"构建已开始，构建号: {build_number}"])
            
            steps = self._get_pipeline_steps_with_polling(task_id, job_name, build_number)
            
            # 分析最终结果
            if steps:
                success_count = sum(1 for step in steps if step['step_status'] == 'SUCCESS')
                failed_count = sum(1 for step in steps if step['step_status'] in ['FAILURE', 'FAILED'])
                
                if failed_count > 0:
                    final_status = "failed"
                    final_message = f"构建失败，成功 {success_count} 个，失败 {failed_count} 个步骤"
                else:
                    final_status = "success"
                    final_message = f"构建成功，共完成 {success_count} 个步骤"
                
                self._update_task_status(task_id, final_status, "完成", [final_message])
            else:
                self._update_task_status(task_id, "failed", "完成", ["未获取到步骤信息"])
                
        except Exception as e:
            error_msg = f"监控Jenkins任务执行失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            self._update_task_status(task_id, "failed", "执行失败", [error_msg]) 
    

    def get_enhanced_task_status(self, task_id):
        """增强的任务状态查询 - 优先从缓存查询，缓存无数据时尝试从Jenkins查询"""
        try:
            # 首先尝试从缓存获取
            task_data = self.get_task_data(task_id)
            
            if task_data:
                logger.info(f"从缓存获取到任务数据: {task_id}")
                return {
                    "success": True,
                    "source": "cache",
                    "data": task_data
                }
            
            logger.warning(f"缓存中未找到任务数据，尝试其他方式: {task_id}")
            return {
                "success": False,
                "source": "none",
                "error": "任务不存在或已过期，且无法从其他来源恢复数据"
            }
            
        except Exception as e:
            logger.error(f"增强任务状态查询失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }


    def get_jenkins_nodes(self):
        """获取Jenkins所有节点列表"""
        try:
            logger.info("获取Jenkins节点列表")
            nodes = self.server.get_nodes()
            
            node_list = []
            for node in nodes:
                node_name = node.get('name', '')
                if not node_name:
                    continue
                
                try:
                    node_info = self.server.get_node_info(node_name)
                    is_offline = node_info.get('offline', True)
                    display_name = node_name
                    if node_name in ['master', 'Built-In Node']:
                        display_name = 'Built-In Node (主节点)'
                    
                    node_data = {
                        'name': node_name,
                        'display_name': display_name,
                        'status': 'offline' if is_offline else 'online',
                        'num_executors': node_info.get('numExecutors', 0)
                    }
                except Exception as e:
                    logger.warning(f"获取节点 {node_name} 详细信息失败: {str(e)}")
                    display_name = node_name
                    if node_name in ['master', 'Built-In Node']:
                        display_name = 'Built-In Node (主节点)'
                    
                    node_data = {
                        'name': node_name,
                        'display_name': display_name,
                        'status': 'unknown',
                        'num_executors': 1
                    }
                
                node_list.append(node_data)
            
            # 排序：Built-In Node和master排在前面，其他按名称排序
            node_list.sort(key=lambda x: (x['name'] not in ['master', 'Built-In Node'], x['name']))
            
            logger.info(f"成功获取 {len(node_list)} 个Jenkins节点")
            return {
                "success": True,
                "data": node_list
            }
            
        except Exception as e:
            logger.error(f"获取Jenkins节点列表失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            } 