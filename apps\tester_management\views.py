import logging
import traceback

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.paginator import Paginator
from django.db import transaction

from .serializers import (
    TesterRoleListSerializer, TesterRoleSerializer, TesterSkillListSerializer, TesterSkillSerializer,
    TesterListSerializer, TesterSerializer, TesterSkillUpdateSerializer, TesterSkillTypeListSerializer,
    TesterSkillTypeSerializer, TesterSkillTypeUpdateSerializer,
)
from .models import (
    TesterRole, TesterSkill, Tester, get_user_skills, get_role_skills, TesterSkillScore, TesterRoleSkillMap,
    TesterSkillTypes,
)

logger = logging.getLogger("machine")


class TesterRoleView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TesterRoleListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            page = serializer.validated_data.get("page", 1)
            pagesize = serializer.validated_data.get("pagesize", 10)
            name_re = serializer.validated_data.get("name_re")

            obj_list = TesterRole.objects.all()
            obj_list = obj_list.order_by("-id")

            if name_re is not None:
                obj_list = obj_list.filter(name__icontains=name_re)

            paginator = Paginator(obj_list, pagesize)

            page_obj = paginator.get_page(page)

            content = {
                "count": paginator.count,
                "results": list(page_obj.object_list.values())
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = TesterRoleSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            TesterRole.objects.create(**serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TesterRoleDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:

            obj = TesterRole.objects.get(id=pk)

            return Response({"err_code": 0, "data": TesterRoleSerializer(obj).data}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:

            serializer = TesterRoleSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            name = serializer.validated_data.get("name")
            code = serializer.validated_data.get("code")
            desc = serializer.validated_data.get("desc")

            obj = TesterRole.objects.get(id=pk)

            obj.name = name
            obj.code = code
            obj.desc = desc

            obj.save()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            result = TesterRole.objects.get(id=pk)
            result.delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TesterSkillView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TesterSkillListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            page = serializer.validated_data.get("page", 1)
            pagesize = serializer.validated_data.get("pagesize", 10)
            name_re = serializer.validated_data.get("name_re")
            department_re = serializer.validated_data.get("department_re")

            obj_list = TesterSkill.objects.all()
            obj_list = obj_list.order_by("-id")

            if name_re is not None:
                obj_list = obj_list.filter(name__icontains=name_re)

            if department_re is not None:
                obj_list = obj_list.filter(department__icontains=department_re)

            paginator = Paginator(obj_list, pagesize)

            page_obj = paginator.get_page(page)

            content = {
                "count": paginator.count,
                "results": TesterSkillSerializer(page_obj.object_list, many=True).data
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = TesterSkillSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            code = f"HW-SKILL-0000"
            tester_skill = TesterSkill.objects.last()
            if tester_skill is not None:
                last_code = tester_skill.code
                if last_code is not None and last_code.__contains__("-"):
                    index = int(last_code.split("-")[2])
                    code = f"HW-SKILL-{str(index + 1).zfill(4)}"

            TesterSkill.objects.create(
                department=serializer.validated_data.get("department"),
                name=serializer.validated_data.get("name"),
                code=code,
                type_id=serializer.validated_data.get("type_id"),
                desc=serializer.validated_data.get("desc")
            )
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TesterSkillDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            obj = TesterSkill.objects.get(id=pk)

            return Response({"err_code": 0, "data": TesterSkillSerializer(obj).data}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            obj = TesterSkill.objects.get(id=pk)

            serializer = TesterSkillUpdateSerializer(obj, data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            department = serializer.validated_data.get("department")
            name = serializer.validated_data.get("name")
            type_id = serializer.validated_data.get("type_id")
            desc = serializer.validated_data.get("desc")

            obj.department = department
            obj.name = name
            obj.type_id = type_id
            obj.desc = desc

            obj.save()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            result = TesterSkill.objects.get(id=pk)
            result.delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TesterSkillTypeView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TesterSkillTypeListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            page = serializer.validated_data.get("page", 1)
            pagesize = serializer.validated_data.get("pagesize", 10)
            name_re = serializer.validated_data.get("name_re")

            obj_list = TesterSkillTypes.objects.all()
            obj_list = obj_list.order_by("-id")

            if name_re is not None:
                obj_list = obj_list.filter(name__icontains=name_re)

            paginator = Paginator(obj_list, pagesize)

            page_obj = paginator.get_page(page)

            content = {
                "count": paginator.count,
                "results": TesterSkillTypeSerializer(page_obj.object_list, many=True).data
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = TesterSkillTypeSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            TesterSkillTypes.objects.create(
                name=serializer.validated_data.get("name"),
                desc=serializer.validated_data.get("desc")
            )
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TesterSkillTypeDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            obj = TesterSkillTypes.objects.get(id=pk)

            return Response({"err_code": 0, "data": TesterSkillTypeSerializer(obj).data}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            obj = TesterSkillTypes.objects.get(id=pk)

            serializer = TesterSkillTypeUpdateSerializer(obj, data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            name = serializer.validated_data.get("name")
            code = serializer.validated_data.get("code")
            desc = serializer.validated_data.get("desc")

            obj.name = name
            obj.code = code
            obj.desc = desc

            obj.save()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            result = TesterSkillTypes.objects.get(id=pk)
            result.delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TesterView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TesterListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            page = serializer.validated_data.get("page", 1)
            pagesize = serializer.validated_data.get("pagesize", 10)
            name_re = serializer.validated_data.get("name_re")
            department_re = serializer.validated_data.get("department_re")

            obj_list = Tester.objects.all()
            obj_list = obj_list.order_by("-id")

            if name_re is not None:
                obj_list = obj_list.filter(name__icontains=name_re)

            if department_re is not None:
                obj_list = obj_list.filter(department__icontains=department_re)

            paginator = Paginator(obj_list, pagesize)

            page_obj = paginator.get_page(page)

            content = {
                "count": paginator.count,
                "results": TesterSerializer(page_obj.object_list, many=True).data
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            # user = request.user
            # if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
            #     return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)
            print(request.data)
            serializer = TesterSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            serializer.save()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TesterDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                try:
                    tester = Tester.objects.get(id=pk)
                    if tester.email != user.email:
                        return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)
                except Tester.DoesNotExist:
                    return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            obj = Tester.objects.get(id=pk)

            return Response({"err_code": 0, "data": TesterSerializer(obj).data}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            obj = Tester.objects.get(id=pk)

            serializer = TesterSerializer(obj, data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            serializer.save()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            obj = Tester.objects.get(id=pk)
            obj.delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TesterGradesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                try:
                    tester = Tester.objects.get(id=pk)
                    if tester.email != user.email:
                        return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)
                except Tester.DoesNotExist:
                    return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            data = get_user_skills(pk)

            return Response({"err_code": 0, "data": data}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request, pk=None):
        try:
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            scores = request.data.get("scores")

            with transaction.atomic():
                TesterSkillScore.objects.filter(tester_id=pk).delete()

                for i in scores:
                    TesterSkillScore.objects.create(
                        tester_id=pk,
                        role_id=i.get("role_id"),
                        skill_id=i.get("skill_id"),
                        score=i.get("score")
                    )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TesterRoleSkillsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:

            data = get_role_skills(pk)

            return Response({"err_code": 0, "data": data}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request, pk=None):
        try:

            skills = request.data.get("skills")

            with transaction.atomic():
                TesterRoleSkillMap.objects.filter(role_id=pk).delete()

                for i in skills:
                    TesterRoleSkillMap.objects.create(
                        role_id=pk,
                        skill_id=i.get("skill_id"),
                        max_score=i.get("max_score"),
                        weight=i.get("weight"),
                        order=i.get("order")
                    )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
