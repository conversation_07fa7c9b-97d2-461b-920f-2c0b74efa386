from django.db import models


class InspecItem(models.Model):
    name = models.Char<PERSON>ield(max_length=255)
    code = models.CharField(max_length=255, unique=True)
    desc = models.TextField(default='', blank=True)
    is_active = models.BooleanField(default=True)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inspec_items'
        managed = False
        app_label = "inspecs"


class InspecModel:
    def __init__(self):
        pass

    def get_items(self, **kwargs):
        page = kwargs.get('page', 1)
        pagesize = kwargs.get('pagesize', 10)
        offset = (page - 1) * pagesize

        queryset = InspecItem.objects.all()

        total_count = queryset.count()

        queryset = queryset.filter(is_active=True)

        queryset = queryset.order_by('-create_time')[offset:offset + pagesize]

        results = queryset.all().values(
            'id', 'name', 'code', 'desc', 'create_time', 'update_time'
        )

        for i in results:
            i['create_time'] = i['create_time'].strftime('%Y-%m-%d %H:%M:%S')
            i['update_time'] = i['update_time'].strftime('%Y-%m-%d %H:%M:%S')

        return {
            'count': total_count,
            'results': results,
        }

    def create_item(self, **kwargs):
        InspecItem.objects.create(
            name=kwargs.get('name'),
            code=kwargs.get('code'),
            desc=kwargs.get('desc'),
        )

    def get_item(self, pk):
        try:
            item = InspecItem.objects.get(pk=pk, is_active=True)
            return {
                'id': item.id,
                'name': item.name,
                'code': item.code,
                'desc': item.desc,
                'create_time': item.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                'update_time': item.update_time.strftime('%Y-%m-%d %H:%M:%S'),
            }
        except InspecItem.DoesNotExist:
            return None

    def update_item(self, pk, **kwargs):
        try:
            item = InspecItem.objects.get(pk=pk, is_active=True)
            item.name = kwargs.get('name', item.name)
            item.desc = kwargs.get('desc', item.desc)
            item.save()
        except InspecItem.DoesNotExist:
            raise ValueError("Item not found")

    def delete_item(self, pk):
        try:
            item = InspecItem.objects.get(pk=pk, is_active=True)
            item.is_active = False
            item.save()
        except InspecItem.DoesNotExist:
            return None
