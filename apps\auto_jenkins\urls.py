from django.urls import path
from .views import (
    PipelineProjectsView, 
    PipelineProjectDetailView,
    PipelineBuildView,
    JenkinsTaskStatusView,
    JenkinsTaskLogsView,
    JenkinsNodesView,
    GitLabWebHookView,
    JenkinsCallbackView,
    PipelineBuildUpdateView,
    PipelineBuildCheckView,
    PipelineBuildListAll
)

urlpatterns = [
    # Pipeline项目管理
    path("projects/", PipelineProjectsView.as_view(), name="pipeline_projects"),
    path("projects/<str:project_name>/", PipelineProjectDetailView.as_view(), name="pipeline_project_detail"),
    # Pipeline构建执行
    path("projects/<str:project_name>/build/", PipelineBuildView.as_view(), name="pipeline_build"),
    # 任务状态查询
    path("tasks/<str:task_id>/status/", JenkinsTaskStatusView.as_view(), name="task_status"),
    path("tasks/<str:task_id>/logs/", JenkinsTaskLogsView.as_view(), name="task_logs"),
    # Jenkins节点管理
    path("jenkins/nodes/", JenkinsNodesView.as_view(), name="jenkins_nodes"),
    # GitLab WebHook接收
    path("webhook/gitlab/", GitLabWebHookView.as_view(), name="gitlab_webhook"),
    # Jenkins回调接口
    path("jenkins/callback/", JenkinsCallbackView.as_view(), name="jenkins_callback"),
    # 构建记录更新接口
    path("builds/update/", PipelineBuildUpdateView.as_view(), name="pipeline_build_update"),
    # 上位机获取upload_url的api
    path("builds/upload-status/", PipelineBuildCheckView.as_view(), name="pipeline_build_check"),
    # 获取构建记录列表的api
    path("builds/list/", PipelineBuildListAll.as_view(), name="pipeline_build_list"),
]
