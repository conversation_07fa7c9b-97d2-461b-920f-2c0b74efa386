import traceback
import logging
import hashlib
import tempfile
import shutil
import os
import uuid
import time
import urllib.parse
import requests

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.http import HttpResponse

from .serializers import (
    DiffToolUsageRecordListSerializer, DiffToolUsageRecordSerializer, DiffToolUsageRecordVerifySerializer,
    DiffPackageShareUrlSerializer
)
from .models import DiffPackageGenerationRecordsModel, DiffPackageShareUrlsModel
from .h56c_diff_tool.bin2hex import diff_bin_file, diff_hex_file, diff_zip_file
from utils.NextcloudHelper import upload

logger = logging.getLogger("diff_tool")

CLOUD_DIR = "automated_test/WPTSN11/PackingFiles"


class DiffToolUsageRecordsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = DiffToolUsageRecordListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DiffPackageGenerationRecordsModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            user = request.user

            serializer = DiffToolUsageRecordSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DiffPackageGenerationRecordsModel()

            project_name = serializer.validated_data.get("project_name")
            project_number = serializer.validated_data.get("project_number")
            old_package_version = serializer.validated_data.get("old_package_version")
            old_package = serializer.validated_data.get("old_package")
            new_package_version = serializer.validated_data.get("new_package_version")
            new_package = serializer.validated_data.get("new_package")
            diff_package_version = serializer.validated_data.get("diff_package_version")
            diff_package_name = serializer.validated_data.get("diff_package_name")
            input_file_type = serializer.validated_data.get("input_file_type")

            if project_number != "ILTCF07":
                return Response({"err_code": 1, "msg": f"暂无项目{project_name}({project_number})差分包制作工具！"},
                                status.HTTP_400_BAD_REQUEST)

            logger.info(f"start diff {old_package.name} {new_package.name} {diff_package_name} "
                        f"-version={diff_package_version} -input_file_type={input_file_type}")

            if input_file_type == "bin":
                if ((os.path.splitext(old_package.name)[1].lower() != ".bin") or
                        (os.path.splitext(new_package.name)[1].lower() != ".bin")):
                    logger.error(f"输入文件不是bin文件")
                    return Response({"err_code": 1, "msg": "输入文件不是bin文件"}, status.HTTP_400_BAD_REQUEST)
                if os.path.splitext(diff_package_name)[1].lower() != ".hex":
                    diff_package_name += ".hex"
            elif input_file_type == "hex":
                if ((os.path.splitext(old_package.name)[1].lower() != ".hex") or
                        (os.path.splitext(new_package.name)[1].lower() != ".hex")):
                    logger.error(f"输入文件不是hex文件")
                    return Response({"err_code": 1, "msg": "输入文件不是hex文件"}, status.HTTP_400_BAD_REQUEST)
                if os.path.splitext(diff_package_name)[1].lower() != ".hex":
                    diff_package_name += ".hex"
            elif input_file_type == "zip":
                if ((os.path.splitext(old_package.name)[1].lower() != ".zip") or
                        (os.path.splitext(new_package.name)[1].lower() != ".zip")):
                    logger.error(f"输入文件不是zip文件")
                    return Response({"err_code": 1, "msg": "输入文件不是zip文件"}, status.HTTP_400_BAD_REQUEST)
                if os.path.splitext(diff_package_name)[1].lower() != ".zip":
                    diff_package_name += ".zip"

            old_package_name = old_package.name
            md5 = hashlib.md5()
            for chunk in old_package.chunks():
                md5.update(chunk)
            old_package_md5 = md5.hexdigest()
            new_package_name = new_package.name
            md5 = hashlib.md5()
            for chunk in new_package.chunks():
                md5.update(chunk)
            new_package_md5 = md5.hexdigest()

            old_package_name_t = old_package_md5 + "_" + old_package_name
            new_package_name_t = new_package_md5 + "_" + new_package_name
            diff_package_name_t = diff_package_name

            temp_dir = tempfile.mkdtemp()
            try:
                old_package_path = os.path.join(temp_dir, old_package_name_t)
                with open(old_package_path, 'wb+') as destination:
                    for chunk in old_package.chunks():
                        destination.write(chunk)

                new_package_path = os.path.join(temp_dir, new_package_name_t)
                with open(new_package_path, 'wb+') as destination:
                    for chunk in new_package.chunks():
                        destination.write(chunk)

                diff_package_path = os.path.join(temp_dir, diff_package_name_t)

                start = time.time()
                logger.info("差分开始")
                if input_file_type == "bin":
                    r = diff_bin_file(old_package_path, new_package_path, diff_package_path, diff_package_version,
                                      temp_dir)
                elif input_file_type == "hex":
                    r = diff_hex_file(old_package_path, new_package_path, diff_package_path, diff_package_version,
                                      temp_dir)
                elif input_file_type == "zip":
                    r = diff_zip_file(old_package_path, new_package_path, diff_package_path, diff_package_version,
                                      temp_dir)
                else:
                    logger.error(f"input_file_type不是bin, hex, zip中的一个")
                    return Response({"err_code": 1, "msg": "input_file_type不是bin, hex, zip中的一个！"},
                                    status.HTTP_400_BAD_REQUEST)
                logger.info(f"差分结束 花费{time.time() - start}")

                if not r:
                    logger.error(f"制作差分包失败")
                    return Response({"err_code": 2, "msg": "制作差分包失败！"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
                else:

                    with open(diff_package_path, "rb") as f:
                        md5 = hashlib.md5()
                        md5.update(f.read())
                        diff_package_md5 = md5.hexdigest()
                    if model.exists(diff_package_md5=diff_package_md5):
                        return Response({"err_code": 1, "msg": "已存在相同差分包MD5! 不能重复制作差分包！"}, status.HTTP_400_BAD_REQUEST)

                    logger.info("差分包制作成功！")

                    fn_d = str(uuid.uuid4())
                    fn = diff_package_name
                    start = time.time()
                    logger.info("差分包上传文件服务器开始")
                    f, url = upload(diff_package_path, f"{CLOUD_DIR}/{fn_d}/{fn}", f"{CLOUD_DIR}/{fn_d}")
                    logger.info(f"差分包上传文件服务器结束 花费{time.time() - start}")
                    if not f:
                        logger.error(f"差分包上传文件服务器失败")
                        return Response({"err_code": 2, "msg": "差分包上传文件服务器失败！"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
                    else:
                        diff_package_url = url
            finally:
                shutil.rmtree(temp_dir)

            model.create(
                project_name=project_name, project_number=project_number, input_file_type=input_file_type,
                old_package_name=old_package_name, old_package_md5=old_package_md5,
                old_package_version=old_package_version,
                new_package_name=new_package_name, new_package_md5=new_package_md5,
                new_package_version=new_package_version,
                diff_package_name=diff_package_name, diff_package_md5=diff_package_md5,
                diff_package_path=diff_package_url, diff_package_version=diff_package_version,
                operator_name=user.username, operator_email=user.email,
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DiffPackageMadeByJenkinsView(APIView):
    def post(self, request):
        try:
            serializer = DiffToolUsageRecordSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DiffPackageGenerationRecordsModel()

            project_name = serializer.validated_data.get("project_name")
            project_number = serializer.validated_data.get("project_number")
            old_package_version = serializer.validated_data.get("old_package_version")
            old_package = serializer.validated_data.get("old_package")
            new_package_version = serializer.validated_data.get("new_package_version")
            new_package = serializer.validated_data.get("new_package")
            diff_package_version = serializer.validated_data.get("diff_package_version")
            diff_package_name = serializer.validated_data.get("diff_package_name")
            input_file_type = serializer.validated_data.get("input_file_type")

            if project_number != "ILTCF07":
                return Response({"err_code": 1, "msg": f"暂无项目{project_name}({project_number})差分包制作工具！"},
                                status.HTTP_400_BAD_REQUEST)

            logger.info(f"start diff {old_package.name} {new_package.name} {diff_package_name} "
                        f"-version={diff_package_version} -input_file_type={input_file_type}")

            if input_file_type == "bin":
                if ((os.path.splitext(old_package.name)[1].lower() != ".bin") or
                        (os.path.splitext(new_package.name)[1].lower() != ".bin")):
                    logger.error(f"输入文件不是bin文件")
                    return Response({"err_code": 1, "msg": "输入文件不是bin文件"}, status.HTTP_400_BAD_REQUEST)
                if os.path.splitext(diff_package_name)[1].lower() != ".hex":
                    diff_package_name += ".hex"
            elif input_file_type == "hex":
                if ((os.path.splitext(old_package.name)[1].lower() != ".hex") or
                        (os.path.splitext(new_package.name)[1].lower() != ".hex")):
                    logger.error(f"输入文件不是hex文件")
                    return Response({"err_code": 1, "msg": "输入文件不是hex文件"}, status.HTTP_400_BAD_REQUEST)
                if os.path.splitext(diff_package_name)[1].lower() != ".hex":
                    diff_package_name += ".hex"
            elif input_file_type == "zip":
                if ((os.path.splitext(old_package.name)[1].lower() != ".zip") or
                        (os.path.splitext(new_package.name)[1].lower() != ".zip")):
                    logger.error(f"输入文件不是zip文件")
                    return Response({"err_code": 1, "msg": "输入文件不是zip文件"}, status.HTTP_400_BAD_REQUEST)
                if os.path.splitext(diff_package_name)[1].lower() != ".zip":
                    diff_package_name += ".zip"

            old_package_name = old_package.name
            md5 = hashlib.md5()
            for chunk in old_package.chunks():
                md5.update(chunk)
            old_package_md5 = md5.hexdigest()
            new_package_name = new_package.name
            md5 = hashlib.md5()
            for chunk in new_package.chunks():
                md5.update(chunk)
            new_package_md5 = md5.hexdigest()

            temp_dir = tempfile.mkdtemp()
            try:
                old_package_path = os.path.join(temp_dir, old_package_name)
                with open(old_package_path, 'wb+') as destination:
                    for chunk in old_package.chunks():
                        destination.write(chunk)

                new_package_path = os.path.join(temp_dir, new_package_name)
                with open(new_package_path, 'wb+') as destination:
                    for chunk in new_package.chunks():
                        destination.write(chunk)

                diff_package_path = os.path.join(temp_dir, diff_package_name)

                if input_file_type == "bin":
                    r = diff_bin_file(old_package_path, new_package_path, diff_package_path, diff_package_version,
                                      temp_dir)
                elif input_file_type == "hex":
                    r = diff_hex_file(old_package_path, new_package_path, diff_package_path, diff_package_version,
                                      temp_dir)
                elif input_file_type == "zip":
                    r = diff_zip_file(old_package_path, new_package_path, diff_package_path, diff_package_version,
                                      temp_dir)
                else:
                    logger.error(f"input_file_type不是bin, hex, zip中的一个")
                    return Response({"err_code": 1, "msg": "input_file_type不是bin, hex, zip中的一个！"},
                                    status.HTTP_400_BAD_REQUEST)

                if not r:
                    logger.error(f"制作差分包失败")
                    return Response({"err_code": 2, "msg": "制作差分包失败！"}, status.HTTP_400_BAD_REQUEST)
                else:

                    with open(diff_package_path, "rb") as f:
                        md5 = hashlib.md5()
                        md5.update(f.read())
                        diff_package_md5 = md5.hexdigest()
                    if model.exists(diff_package_md5=diff_package_md5):
                        return Response({"err_code": 1, "msg": "已存在相同差分包MD5! 不能重复制作差分包！"}, status.HTTP_400_BAD_REQUEST)

                    logger.info("差分包制作成功！")

                    fn_d = str(uuid.uuid4())
                    fn = diff_package_name
                    start = time.time()
                    logger.info("差分包上传文件服务器开始")
                    f, url = upload(diff_package_path, f"{CLOUD_DIR}/{fn_d}/{fn}", f"{CLOUD_DIR}/{fn_d}")
                    logger.info(f"差分包上传文件服务器结束 花费{time.time() - start}")
                    if not r:
                        logger.error(f"差分包上传文件服务器失败")
                        return Response({"err_code": 2, "msg": "差分包上传文件服务器失败！"}, status.HTTP_400_BAD_REQUEST)
                    else:
                        diff_package_url = url
            finally:
                shutil.rmtree(temp_dir)

            model.create(
                project_name=project_name, project_number=project_number, input_file_type=input_file_type,
                old_package_name=old_package_name, old_package_md5=old_package_md5,
                old_package_version=old_package_version,
                new_package_name=new_package_name, new_package_md5=new_package_md5,
                new_package_version=new_package_version,
                diff_package_name=diff_package_name, diff_package_md5=diff_package_md5,
                diff_package_path=diff_package_url, diff_package_version=diff_package_version,
                operator_name="Jenkins", operator_email="<EMAIL>",
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DiffToolUsageRecordDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = DiffPackageGenerationRecordsModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            user = request.user
            model = DiffPackageGenerationRecordsModel()

            r = model.retrieve(pk=pk)
            if not r:
                return Response({"err_code": 1, "msg": "记录不存在！"}, status.HTTP_400_BAD_REQUEST)

            if r.get("operator_email") != '<EMAIL>' and r.get("operator_email") != user.email:
                return Response({"err_code": 1, "msg": "创建人不符合！"}, status.HTTP_400_BAD_REQUEST)

            model.delete(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DiffToolUsageRecordVerifyView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = DiffToolUsageRecordVerifySerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            id = serializer.validated_data.get("id")
            diff_package_status = serializer.validated_data.get("diff_package_status")

            user = request.user

            model = DiffPackageGenerationRecordsModel()

            r = model.retrieve(id)
            if not r:
                return Response({"err_code": 1, "msg": "对应id差分包记录不存在！"}, status.HTTP_400_BAD_REQUEST)
            if r.get("operator_email") != user.email:
                return Response({"err_code": 1, "msg": "操作人不符！"}, status.HTTP_400_BAD_REQUEST)
            if r.get("diff_package_status") != 0:
                return Response({"err_code": 1, "msg": "差分包状态不为未验证！"}, status.HTTP_400_BAD_REQUEST)

            model.update_verify_status(id=id, diff_package_status=diff_package_status)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DiffPackageShareUrlsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            user = request.user

            serializer = DiffPackageShareUrlSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DiffPackageShareUrlsModel()

            diff_record_id = serializer.validated_data.get("diff_record_id")
            expiration_time = serializer.validated_data.get("expiration_time")
            token = str(uuid.uuid4())

            r = DiffPackageGenerationRecordsModel().retrieve(diff_record_id)
            if not r:
                return Response({"err_code": 1, "msg": "差分包记录不存在"}, status.HTTP_400_BAD_REQUEST)

            if r.get("diff_package_status") != 1:
                return Response({"err_code": 1, "msg": "差分包未验证通过"}, status.HTTP_400_BAD_REQUEST)

            model.create(
                diff_record_id=diff_record_id, token=token, expiration_time=expiration_time,
                creator_name=user.username, creator_email=user.email,
            )

            return Response({"err_code": 0, "data": {"token": token}, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DiffPackageShareUrlDetailView(APIView):
    def get(self, request, token=None):
        try:
            model = DiffPackageShareUrlsModel()
            result = model.retrieve(token=token)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DiffPackageDownloadView(APIView):
    def get(self, request, token=None):
        try:
            model = DiffPackageShareUrlsModel()
            r = model.retrieve(token)
            if not r:
                return HttpResponse("链接失效", status=400)

            url = r.get("diff_package_path")
            filename = r.get("diff_package_name")
            filename = urllib.parse.quote(filename)

            resp = requests.get(url + "/download/" + filename, stream=True)

            response = HttpResponse(
                resp.content,
                content_type="application/octet-stream"
            )

            response['Content-Disposition'] = f'attachment; filename={filename}'

            return response
        except Exception:
            logger.error(traceback.format_exc())
            return HttpResponse("服务器错误", status=500)
