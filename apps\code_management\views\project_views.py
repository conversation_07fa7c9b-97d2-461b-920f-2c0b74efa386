import logging
import traceback
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db import transaction
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.cache import cache
from django.core.serializers import serialize
import json
import os
import shutil
import time
from pathlib import Path
from django.core.cache import cache

logger = logging.getLogger("code_management")
from apps.code_management.grpc.host_machine_grpc import host_machine_client
from apps.code_management.grpc.git_grpc import git_client
from apps.code_management.models.project_models import ProjectInfoSerializer, CodeProjectInfo, CodeBranchInfo, BranchInfoSerializer
from users.models import UserFSInfo
from utils.fs_service import FSService
from utils.fs_app import fs_app


local_path = "/home/<USER>/hwcp"
config_path = "/home/<USER>/hwcp/gRPC_Server/HwcpTest"
config_url = "http://*********/python-team/HwcpTest.git"
config_branch = "main"
sdk_git_path = "http://*********/"


def create_workspace_dir(path):
    """
        创建服务器工作目录
    """
    try:
        # 先检查目录是否已存在
        if os.path.exists(path):
            if os.path.isdir(path):
                logger.info(f"工作目录已存在: {path}")
                return True
            else:
                # 路径存在但不是目录，记录错误
                logger.info(f"路径已存在但不是目录: {path}")
                return False

        # 目录不存在，创建它
        os.makedirs(path)
        logger.info(f"工作目录创建成功: {path}")
        return True
    except Exception as e:
        logger.info(f"创建工作目录失败:%s", e)
        return False


def delete_directory(directory_path, max_retries=3, retry_delay=1, log_progress=True):
    """
    安全地删除一个目录，包括非空目录

    Args:
        directory_path: 要删除的目录路径
        max_retries: 删除失败时的最大重试次数
        retry_delay: 重试之间的延迟时间(秒)
        log_progress: 是否记录详细的进度日志

    Returns:
        bool: 删除成功返回True，失败返回False
    """
    # 验证路径是否存在
    if not os.path.exists(directory_path):
        logger.info(f"目录不存在，无需删除: {directory_path}")
        return True

    # 验证是否为目录
    if not os.path.isdir(directory_path):
        logger.info(f"指定路径不是目录: {directory_path}")
        return False

    # 获取目录大小用于日志
    try:
        dir_size = sum(f.stat().st_size for f in Path(directory_path).glob('**/*') if f.is_file())
        size_mb = dir_size / (1024 * 1024)
        logger.info(f"准备删除目录: {directory_path} (约 {size_mb:.2f} MB)")
    except Exception as e:
        logging.warning(f"无法获取目录大小: {e}")
        size_mb = 0

    # 执行删除操作
    retry_count = 0
    while retry_count <= max_retries:
        try:
            if log_progress:
                logger.info(f"正在删除目录: {directory_path} (尝试 {retry_count + 1}/{max_retries + 1})")

            # 使用shutil.rmtree删除目录，ignore_errors=False确保捕获所有错误
            shutil.rmtree(directory_path, ignore_errors=False)

            # 验证删除结果
            if not os.path.exists(directory_path):
                logger.info(f"目录删除成功: {directory_path}")
                return True
            else:
                logger.info(f"目录仍然存在，可能是删除过程中重新创建了: {directory_path}")
                retry_count += 1
                time.sleep(retry_delay)

        except PermissionError as e:
            logger.info(f"权限错误，无法删除目录: {e}")
            # 尝试更改权限并重试
            try:
                for root, dirs, files in os.walk(directory_path):
                    for dir in dirs:
                        os.chmod(os.path.join(root, dir), 0o777)
                    for file in files:
                        os.chmod(os.path.join(root, file), 0o777)
                retry_count += 1
                time.sleep(retry_delay)
            except Exception as perm_e:
                logger.info(f"更改权限失败: {perm_e}")
                return False

        except Exception as e:
            logging.error(f"删除目录时发生错误: {e}")
            retry_count += 1
            if retry_count <= max_retries:
                logger.info(f"将在 {retry_delay} 秒后重试 ({retry_count}/{max_retries})")
                time.sleep(retry_delay)
            else:
                logger.info(f"达到最大重试次数，删除失败: {directory_path}")
                return False

    logger.info(f"删除目录失败，已尝试 {max_retries} 次: {directory_path}")
    return False



class SdkInfoView(APIView):
    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)
            project_code = params.get("project_code")
            project_name = params.get("project_name")
            version_rule = params.get("version_rule")
            project_gitlab = params.get("project_gitlab")
            # 拼接project_gitlab
            logger.info("project_code:%s, project_name: %s, version_rule:%s, project_gitlab:%s",
                        project_code, project_name, version_rule, project_gitlab)

            # 非空校验
            # 参数校验
            if not project_code or not project_gitlab:
                return Response({
                    "sdk_status": 0,
                    "message": "project_code 和 project_gitlab 为必填参数"
                })

            try:
                projects = CodeProjectInfo.objects.only("chip", "modules").get(
                    project_code=project_code,
                    project_gitlab=project_gitlab
                )
            except CodeProjectInfo.DoesNotExist:
                return Response({"message": "未找到项目"})
            except CodeProjectInfo.MultipleObjectsReturned:
                return Response({"message": "存在多个匹配项目，请检查数据"})
            logger.info("查询出的数据：%s", projects)

            chip = projects.chip.strip() if isinstance(projects.chip, str) else projects.chip
            modules = projects.modules if isinstance(projects.modules, list) else []

            logger.info("chip: %s, modules: %s", chip, modules)

            if chip and modules:
                sdk_info = [{
                    "chip": chip,
                    "modules": modules
                }]
            else:
                return Response({
                    "sdk_status": 2,
                    "message": "未找到有效的芯片和功能模块数据"
                })

            return Response({
                "sdk_status": 1,
                "message": "查询操作成功",
                "sdk_info": sdk_info
            })

        except Exception as e:
            logger.info(f"查询数据失败: {str(e)}")
            return Response({"sdk_status": 0, "message": f"查询操作失败:{str(e)}"})





class ConfigSubmitView(APIView):

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    # 创建sdk仓库
    def post(self, request):
        branch_status = ""
        # 区分分支是否新建
        try:
            with transaction.atomic():
                # 1. 解析参数
                params = request.data.get('params', {})
                logger.info("Received params: %s", params)

                required_fields = ['project_code', 'project_name', 'selected_chip', 'selected_modules', 'workspace',
                                   'workgroup']
                missing_fields = [field for field in required_fields if not params.get(field)]
                if missing_fields:
                    raise ValueError(f"缺少必要字段: {', '.join(missing_fields)}")

                project_code = params['project_code']
                project_name = params['project_name']
                chip = params['selected_chip']
                modules = params['selected_modules']
                workspace = params['workspace']
                workgroup = params['workgroup']
                sdk_branch = params.get('sdk_branch')
                sdk_tag = params.get('sdk_tag')

                # 2. 用户名处理
                username = request.user.username if request.user.is_authenticated else 'anonymous'

                # 3. sdk 版本校验
                if sdk_branch and not sdk_tag:
                    sdk_version = sdk_branch
                elif sdk_tag and not sdk_branch:
                    sdk_version = sdk_tag
                else:
                    raise ValueError("请提供 sdk_branch 或 sdk_tag 中的一个，不能同时为空或都不为空")

                # 4. 构造路径
                project_gitlab = f"{sdk_git_path}{workgroup}/{workspace}"
                project_workspace_path = f"{local_path}/{project_code}/{workgroup}/{workspace}"

                logger.info("准备写入路径: %s", project_workspace_path)

                # 5. 检查是否已存在
                if CodeProjectInfo.objects.filter(project_gitlab=project_gitlab).exists():
                    raise ValueError("当前仓库已存在，请新建仓库")

                config_data = {
                    'project_code': project_code,
                    'project_name': project_name,
                    'sdk_version': sdk_version,
                    'project_gitlab': project_gitlab,
                    'chip': chip,
                    'modules': modules,
                    'project_space': project_workspace_path,
                    'project_group': workgroup,
                    'create_person': username
                }
                logger.info("将写入仓库信息表: %s", config_data)

                # 6. 保存信息
                serializer = ProjectInfoSerializer(data=config_data)
                serializer.is_valid(raise_exception=True)
                serializer.save()
                logger.info("仓库信息表写入成功")

                # 7. gRPC 创建仓库
                logger.info("调用 grpc 创建仓库: %s / %s", workspace, workgroup)
                response = git_client.create_project_serve(workspace, workgroup)
                logger.info("gRPC 返回: %s", response)

                if response.code == 200:
                    return Response({"config_status": 1, "message": "创建仓库成功"})
                elif response.code == 409:
                    logger.warning("gRPC: 仓库已存在")
                    return Response({"config_status": 2, "message": "仓库在 GitLab 上已存在"})
                else:
                    raise ValueError("新建仓库失败，gRPC 返回异常")

        except Exception as e:
            logger.exception("配置提交异常")
            return Response({"config_status": 0, "message": f"配置提交失败: {str(e)}"})

    # 创建普通仓库
    def get(self, request):
        # 项目信息创建仓库
        try:
            with transaction.atomic():
                # 1. 参数获取与校验
                params = request.query_params
                logger.info("Received params: %s", params)

                field_names = [
                    'selected_group', 'project_space', 'project_description',
                    'version_rule', 'project_code', 'project_name'
                ]
                missing = [f for f in field_names if not params.get(f)]
                if missing:
                    raise ValueError(f"缺少必要字段: {', '.join(missing)}")

                project_group = params['selected_group']
                space_name = params['project_space']  # 原始 project_space 名
                project_description = params['project_description']
                version_rule = params['version_rule']
                project_code = params['project_code']
                project_name = params['project_name']

                username = request.user.username if request.user.is_authenticated else 'anonymous'

                # 2. 构建地址
                project_gitlab = f"{sdk_git_path}{project_group}/{space_name}"
                project_local_path = f"{local_path}/{project_code}/{project_group}/{space_name}"

                logger.info("构建路径完成：gitlab_url=%s, local_path=%s, user=%s",
                            project_gitlab, project_local_path, username)

                # 3. 写入数据库：get_or_create
                logger.info("准备写入项目信息表: \n"
                            "project_code: %s\n"
                            "project_name: %s\n"
                            "project_gitlab: %s\n"
                            "project_space: %s\n"
                            "project_group: %s\n"
                            "project_description: %s\n"
                            "version_rule: %s\n"
                            "create_person: %s",
                            project_code, project_name, project_gitlab,
                            project_local_path, project_group, project_description,
                            version_rule, username)

                project, created = CodeProjectInfo.objects.get_or_create(
                    project_code=project_code,
                    project_name=project_name,
                    project_gitlab=project_gitlab,
                    defaults={
                        'project_space': project_local_path,
                        'project_group': project_group,
                        'project_description': project_description,
                        'version_rule': version_rule,
                        'create_person': username
                    }
                )

                if not created:
                    raise ValueError("该项目仓库已存在，请更换信息创建")

                logger.info("项目信息写入成功")

                # 4. 创建仓库（gRPC）
                logger.info("调用 gRPC 创建仓库：space=%s, group=%s", space_name, project_group)
                response = git_client.create_project_serve(space_name, project_group)
                logger.info("gRPC 响应: %s", response)

                if response.code == 200:
                    return Response({"config_status": 1, "message": "创建仓库成功"})
                elif response.code == 409:
                    return Response({"config_status": 1, "message": "仓库已存在，成功引入"})
                else:
                    raise ValueError("gRPC 创建仓库失败，code: %s" % response.code)

        except Exception as e:
            logger.exception("创建项目时发生异常")
            return Response({"config_status": 0, "message": "创建失败: " + str(e)})




class ProjectInfoView(APIView):

    def get(self, request):
        try:
            params = request.query_params
            project_code = params.get("project_code", "").strip()
            logger.info("接收参数: project_code = %s", project_code)

            # 查询数据库（条件过滤）
            queryset = CodeProjectInfo.objects.only(
                "id", "project_code", "project_name", "project_gitlab", "version_rule"
            )
            projects = queryset.filter(project_code=project_code) if project_code else queryset.all()

            logger.info("共查询到 %d 条项目记录", projects.count())

            # 构造响应数据
            table_data = [
                {
                    "id": p.id,
                    "code": p.project_code,
                    "description": p.project_name,
                    "gitlab": p.project_gitlab,
                    "versionRules": p.version_rule
                }
                for p in projects
            ]

            return Response({
                "select_status": 1,
                "message": "查询操作成功",
                "table_data": table_data
            })

        except Exception as e:
            logger.exception("查询仓库信息失败")
            return Response({
                "select_status": 0,
                "message": f"查询操作失败: {str(e)}"
            })


class DeleteProjectView(APIView):
    # 人员认证
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]



    # 删除数据库数据
    def post(self, request):
        try:
            username = request.user.username if request.user.is_authenticated else 'anonymous'
            params = request.data.get('params', {})
            logger.info("receive info:%s", params)
            project_code = params.get('project_code')
            project_name = params.get('project_name')
            project_gitlab = params.get('project_gitlab')

            with transaction.atomic():
                projects = CodeProjectInfo.objects.filter(
                    project_code=project_code,
                    project_name=project_name,
                    project_gitlab=project_gitlab
                )

                if projects:
                    for project in projects:
                        project_space = project.project_space
                        # 删除服务器目录
                        del_dir = delete_directory(project_space)
                        if del_dir:
                            project.delete()
                        else:
                            raise ValueError("删除服务器工作目录失败")
                    logger.info(f"已删除仓库信息表数据: {project_gitlab}, 已删除服务器仓库目录: {project_space}, 操作人: {username}")
                branch_projects = CodeBranchInfo.objects.filter(
                    project_code=project_code,
                    project_name=project_name,
                    project_gitlab=project_gitlab
                )
                if branch_projects:
                    for branch_project in branch_projects:
                        branch_project.delete()
                    logger.info(f"已删除仓库信息表中数据: {branch_project}, 操作人: {username}")
            return Response({"delete_status": 1, "message": "删除成功"})

        except Exception as e:
            logger.info("失败: %s", str(e))
            return Response({"delete_status": 0, "message": f"操作失败:{str(e)}"})




class ProjectSubmitView(APIView):

    def get(self, request):
        cache_key = 'subgroup_paths'
        cached_data = cache.get(cache_key)
        logger.info("subgroup_paths 在缓存中存在")

        if not cached_data:
            try:
                # 调用 git grpc 获取项目组信息
                get_temp_response = git_client.get_temp_serve()
                logger.info("git_grpc get_temp_serve response: %s", get_temp_response)

                if get_temp_response.code == 200:
                    subgroup_paths = list(get_temp_response.subgroup_paths)
                    logger.info("subgroup_paths: %s", subgroup_paths)
                    # 存入缓存
                    cache.set('subgroup_paths', subgroup_paths, timeout=3 * 24 * 3600)
                    logger.info("加载 subgroup_paths 数据到缓存中")
                    return Response({
                        "project_status": 1,
                        "message": "组获取成功",
                        "subgroup_paths": subgroup_paths
                    }, status=200)
                else:
                    logger.info("git_grpc 返回非 200，code: %s", get_temp_response.code)
                    return Response({
                        "project_status": 0,
                        "message": "组获取失败"
                    })

            except Exception as e:
                logger.info("git_grpc get_temp_serve 发生异常: %s", str(e))
                return Response({
                    "project_status": 0,
                    "message": f"组获取失败: {str(e)}"
                })
        else:
            return Response({
                "project_status": 1,
                "message": "组获取成功",
                "subgroup_paths": cached_data
            }, status=200)


class GetBranchesView(APIView):
    # 获取分支/tag功能
    def get(self, request):
        logger.info("request data: %s", request.query_params)

        sdk_path = request.query_params.get('sdkGitLab')
        if not sdk_path:
            return Response({'error': '请输入有效的 GitLab 地址'}, status=400)

        try:
            project = str(sdk_path).split("/")[-1]
            logger.info("send git_grpc project params: %s", project)

            # 调用 gRPC 获取分支
            branch_response = git_client.get_branch_serve(project)
            logger.info("received GetProjectBranches branch_response: %s", branch_response)

            if branch_response.success:
                branches_list = list(branch_response.branches)
                logger.info(f"分支 branches_list: {branches_list}")
                # 调用gprc 获取tag
                tag_project = str(sdk_path).split(sdk_git_path)[-1]
                logger.info("send git_grpc tag_project params: %s", tag_project)
                tag_response = git_client.get_tag_serve(tag_project)
                logger.info("received GetProjectTags response: %s", tag_response)
                tags_list = list()
                if tag_response.success:
                    # tags = list(tag_response.tags)
                    for i, tag in enumerate(tag_response.tags):
                        # logger.info(f"标签 {i + 1}: {tag.name}")
                        tags_list.append(tag.name)
                    logger.info(f"标签 tag_list: {tags_list}")

                    return Response({
                        "status": 1,
                        "message": tag_response.message,
                        "branches": branches_list,
                        "tags": tags_list
                    })

                else:
                    raise ValueError("获取sdk仓库tag信息失败")


            else:
                raise ValueError("获取sdk仓库分支信息失败")

        except Exception as e:
            logger.info("获取分支失败，异常信息: %s", str(e))
            return Response({
                "status": 0,
                "message": f"获取分支失败: {str(e)}"
            })





class InitGrpcView(APIView):
    # 初始化
    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("接收到初始化参数: %s", params)

            gitlab = params.get('sdkGitLab')
            branch = params.get('sdkBranch')
            tag = params.get('sdkTag')
            workgroup = params.get('group')
            workspace = params.get('space')
            project_code = params.get('project_code')
            project_name = params.get('project_name')
            if not gitlab:
                raise ValueError("缺少 GitLab 地址")

            if not branch and not tag:
                raise ValueError("缺少分支名或标签")
            # 将sdk拉取到服务的仓亏空间 "/home/<USER>/hwcp/.."
            repo_name = gitlab.rstrip("/").split("/")[-1]
            repo_path = f"{local_path}/{project_code}/{workgroup}/{workspace}/{repo_name}"
            # 创建仓库
            result = create_workspace_dir(repo_path)

            if result:
                if branch and not tag:
                    # 克隆主代码仓库
                    logger.info("开始克隆 SDK 项目: gitlab=%s, branch=%s, repo_path=%s", gitlab, branch, repo_path)
                    result = git_client.clone_serve(gitlab, repo_path, branch, None)

                    if result is None or not result.success:
                        logger.info("克隆 SDK 仓库失败: %s", result)
                        raise ValueError("克隆 SDK 仓库失败")
                elif tag and not branch:
                    # 克隆标签代码
                    logger.info("开始克隆 SDK 项目: gitlab=%s, tag=%s, repo_path=%s", gitlab, tag, repo_path)
                    result = git_client.clone_serve(gitlab, repo_path, None, tag)

                    if result is None or not result.success:
                        logger.info("克隆 SDK 仓库失败: %s", result)
                        raise ValueError("克隆 SDK 仓库失败")
                else:
                    raise ValueError("初始化信息错误，请检查")

                # 初始化 SDK 工程
                sdk_path = f"{repo_path}/Application"
                logger.info("开始初始化 SDK 工程，路径: %s", sdk_path)
                # 获取项目配置树
                response = host_machine_client.getProjectTree_serve(sdk_path)
                logger.info("开始初始化获取芯片和模块: %s", response)
                module_list = list(response.module_list)

                # if "GPIO" in module_list:
                #     module_list.remove("GPIO")
                #     logger.info("移除GPIO的模块")

                chip_list = list(response.chip_list)
                logger.info("获取功能模块: %s, 获取芯片平台：%s", module_list, chip_list)
                return Response({
                    "init_status": 1,
                    "message": "初始化成功",
                    "func": module_list,
                    "chip_list": chip_list,
                    "pick_chips": module_list
                })
            else:
                # 创建目录失败
                logger.info("创建项目仓库路径失败，请检查")
                raise ValueError(" 创建sdk仓库失败")

        except Exception as e:
            logger.info("初始化失败: %s", str(e))
            return Response({"init_status": 0, "message": f"初始化失败: {str(e)}"})