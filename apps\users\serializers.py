from rest_framework import serializers


class UserListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)


class UserLoginSerializer(serializers.Serializer):
    code = serializers.CharField(max_length=255)
    model = serializers.CharField(max_length=255)
    product_code = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    auth_code = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class UserLogin2Serializer(serializers.Serializer):
    username = serializers.Char<PERSON>ield(max_length=255)
    password = serializers.Char<PERSON>ield(max_length=255)


class FSOrganizationSerializer(serializers.Serializer):
    department_id = serializers.Char<PERSON>ield(max_length=255, default="0")


class FSUserSearchSerializer(serializers.Serializer):
    name = serializers.Char<PERSON><PERSON>(max_length=255)
