--程序表
CREATE TABLE IF NOT EXISTS public.programs(
    id serial primary key, --id
    "name" varchar(255),  --程序名称
    "number" varchar(255) unique ,  --程序编号
    "desc" text, --程序描述
    "type" int default 0, --类型 0 不接受管理 1 接受管理
    icon varchar(255),  --程序图标
    extra_args json, --额外参数
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.programs OWNER TO atpms;

--程序版本表
CREATE TABLE IF NOT EXISTS public.program_versions(
    id serial primary key, --id
    program_id int,  --程序id
    version varchar(255), --版本号
    path varchar(255), --程序路径
    linux_path varchar(255), --程序linux路径
    "desc" text, --版本描述
    is_disabled bool default false,
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.program_versions OWNER TO atpms;