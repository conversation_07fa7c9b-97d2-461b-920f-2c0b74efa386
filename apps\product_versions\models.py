import datetime

from django.db import models

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone
from .signals import product_version_update


class ProductVersionModel:
    def __init__(self):
        self.table_name = "public.product_versions"

    def create(self, **kwargs):
        now = datetime.datetime.now()

        params = {
            "project_name": kwargs.get("project_name"),
            "project_number": kwargs.get("project_number"),
            "project_id": kwargs.get("project_id"),
            "type": kwargs.get("type"),
            "type_name": kwargs.get("type_name"),
            "use": kwargs.get("use"),
            "use_name": kwargs.get("use_name"),
            "version_number": kwargs.get("version_number"),
            "version_name": kwargs.get("version_name"),
            "version_package_address": kwargs.get("version_package_address"),
            "plan_version": kwargs.get("plan_version"),
            "customer_version": kwargs.get("customer_version"),
            "desc": kwargs.get("desc"),
            "publisher_name": kwargs.get("publisher_name"),
            "publisher_email": kwargs.get("publisher_email"),
            "publish_time": kwargs.get("publish_time"),
            "status": "SUBMITTED",
            "remark": kwargs.get("remark", ""),  # 添加默认值
            "qac_url": kwargs.get("qac_url"),
            "tessy_url": kwargs.get("tessy_url"),
            "sub_version_info": kwargs.get("sub_version_info"),
            "create_time": now,
            "update_time": now,
        }

        sql = """
                  INSERT INTO {table_name} (project_name, project_number, project_id, "type", type_name, use, use_name, 
                      version_number, version_name, version_package_address, plan_version, customer_version,
                      "desc", publisher_name, publisher_email, publish_time, status, remark, qac_url, tessy_url, 
                       sub_version_info, create_time, update_time) 
                  VALUES (%(project_name)s, %(project_number)s, %(project_id)s, %(type)s, %(type_name)s, %(use)s, %(use_name)s,
                      %(version_number)s, %(version_name)s, %(version_package_address)s, %(plan_version)s, %(customer_version)s, 
                      %(desc)s, %(publisher_name)s, %(publisher_email)s, 
                      %(publish_time)s, %(status)s, %(remark)s, %(qac_url)s, %(tessy_url)s, %(sub_version_info)s, 
                       %(create_time)s, %(update_time)s)
                  RETURNING id
                  ;
              """.format(table_name=self.table_name)

        r = sql_fetchone(sql, params)

        product_version_update.send(
            self.__class__,
            project_number=kwargs.get("project_number"),
            version_name=kwargs.get("version_name"),
            status="SUBMITTED",
            old_status=None,
        )

        return r[0] if r else None

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        version_number = kwargs.get("version_number")
        if version_number is not None and version_number != '':
            sql_where_list.append("version_number = %(version_number)s")
            params["version_number"] = version_number

        version_name = kwargs.get("version_name")
        if version_name is not None and version_name != '':
            sql_where_list.append("version_name = %(version_name)s")
            params["version_name"] = version_name

        status = kwargs.get("status")
        if status is not None and status != '':
            sql_where_list.append("status = %(status)s")
            params["status"] = status

        status_ex = kwargs.get("status_ex")
        if status_ex is not None and status_ex != '':
            sql_where_list.append("status <> %(status_ex)s")
            params["status_ex"] = status_ex

        status_list = kwargs.get("status_list")
        if status_list is not None and status_list != '':
            sql_where_list.append("status in %(status_list)s")
            params["status_list"] = tuple(status_list)

        version_type = kwargs.get("type")
        if version_type is not None and version_type != '':
            sql_where_list.append("type = %(type)s")
            params["type"] = version_type

        type_ex = kwargs.get("type_ex")
        if type_ex is not None and type_ex != '':
            sql_where_list.append("type <> %(type_ex)s")
            params["type_ex"] = type_ex

        type_list = kwargs.get("type_list")
        if type_list is not None and type_list != '':
            sql_where_list.append("type in %(type_list)s")
            params["type_list"] = tuple(type_list)

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY create_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, project_name, project_number, project_id, "type", type_name, use, use_name, version_number,
                version_name, version_package_address, plan_version, customer_version, "desc", publisher_name, 
                publisher_email, publish_time, finish_time, status, remark, qac_url, tessy_url, create_time, update_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        for result in content["results"]:
            if result.get("publish_time") and result.get("finish_time"):
                result["duration"] = round((result['finish_time'] - result['publish_time']).total_seconds() / 86400, 2)
                result["finish_time"] = result["finish_time"].strftime("%Y-%m-%d %H:%M:%S")
            else:
                result["duration"] = ""
                result["finish_time"] = ""

            result["publish_time"] = result["publish_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, project_name, project_number, project_id, "type", type_name, use, use_name, version_number, 
            version_name, "desc", publisher_name, publisher_email, publish_time, status, remark,
              qac_url, tessy_url, create_time, update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["publish_time"] = result["publish_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def retrieve_by_version_name(self, version_name):
        sql = """
            SELECT id, project_name, project_number, project_id, "type", type_name, use, use_name, version_number, 
                version_name, "desc", publisher_name, publisher_email, publish_time, status,
                qac_url, tessy_url, create_time, update_time
            FROM {table_name}
            WHERE version_name = %(version_name)s
            LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, {"version_name": version_name})

        if result:
            result["publish_time"] = result["publish_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
    """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update_status(self, pk, status):
        r = self.retrieve(pk)

        if not r:
            return

        if r.get("status") == status:
            return

        now = datetime.datetime.now()
        params = {
            "id": pk,
            "status": status,
            "update_time": now,
        }

        sql = """
            UPDATE {table_name}     
                SET 
                status = %(status)s,
                update_time = %(update_time)s
                WHERE id = %(id)s
                ;
        """.format(
            table_name=self.table_name
        )
        if status == "PASSED":
            sql = """
                UPDATE {table_name}     
                    SET 
                    status = %(status)s,
                    finish_time = %(update_time)s,
                    update_time = %(update_time)s
                    WHERE id = %(id)s
                    ;
            """.format(
                table_name=self.table_name
            )

        sql_execute(sql, params)

        product_version_update.send(
            self.__class__,
            project_number=r.get("project_number"),
            version_name=r.get("version_name"),
            status=status,
            old_status=r.get("status"),
        )

    def update_remark(self, pk, remark):
        r = self.retrieve(pk)

        if not r:
            return

        now = datetime.datetime.now()
        params = {
            "id": pk,
            "remark": remark,
            "update_time": now,
        }

        sql = """
            UPDATE {table_name}     
                SET 
                remark = %(remark)s,  -- 更新 remark 字段
                update_time = %(update_time)s
                WHERE id = %(id)s
                ;
        """.format(
            table_name=self.table_name
        )
        sql_execute(sql, params)

    def update_attachment_url(self, pk, attachment_url):
        r = self.retrieve(pk)

        if not r:
            return

        now = datetime.datetime.now()
        params = {
            "id": pk,
            "attachment_url": attachment_url,
            "update_time": now,
        }

        sql = """
            UPDATE {table_name}     
                SET 
                attachment_url = %(attachment_url)s,
                update_time = %(update_time)s
                WHERE id = %(id)s
                ;
        """.format(
            table_name=self.table_name
        )
        sql_execute(sql, params)


class PersonProductVersionMap(models.Model):
    person_name = models.CharField(max_length=255)
    person_email = models.CharField(max_length=255)
    product_version_id = models.IntegerField()

    class Meta:
        db_table = 'person_product_version_map'
        managed = False
        app_label = 'product_versions'
