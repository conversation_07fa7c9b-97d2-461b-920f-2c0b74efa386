CREATE TABLE dbc_files (
    id SERIAL PRIMARY KEY,
    project_id VARCHAR(255),
    project_name VA<PERSON>HA<PERSON>(255),
    project_number VARCHAR(50),
    file_name VARCHAR(255) NOT NULL UNIQUE,
    file_path TEXT NOT NULL,
    file_url TEXT NOT NULL,
    content TEXT NOT NULL,
    version VARCHAR(50),
    description TEXT,
    upload_user_name VARCHAR(50),
    upload_user_email VARCHAR(50),
    status SMALLINT DEFAULT 1,
    tags TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE can_ecus (
    id SERIAL PRIMARY KEY,
    dbc_id INTEGER NOT NULL REFERENCES dbc_files(id) ON DELETE CASCADE,
    ecu_name VARCHAR(100) NOT NULL,
    ecu_type VARCHAR(50),
    description TEXT,
    create_time DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_ecu_name UNIQUE (dbc_id, ecu_name)
);
CREATE INDEX idx_can_ecus_dbc_id ON can_ecus(dbc_id);

CREATE TABLE can_messages (
    id SERIAL PRIMARY KEY,
    dbc_id INTEGER NOT NULL REFERENCES dbc_files(id) ON DELETE CASCADE,
    message_id INTEGER NOT NULL,
    message_name VARCHAR(100) NOT NULL,
    dlc SMALLINT NOT NULL CHECK (dlc BETWEEN 0 AND 64),
    cycle_time INTEGER,
    is_fd BOOLEAN DEFAULT FALSE,
    is_extended BOOLEAN DEFAULT FALSE,
    send_type VARCHAR(20) CHECK (send_type IN ('Cyclic', 'Event', 'OnChange')) DEFAULT 'Cyclic',
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_message_id UNIQUE (dbc_id, message_id)
);
CREATE INDEX idx_can_messages_dbc_id ON can_messages(dbc_id);

CREATE TABLE can_signals (
    id SERIAL PRIMARY KEY,
    message_id INTEGER NOT NULL REFERENCES can_messages(id) ON DELETE CASCADE,
    signal_name VARCHAR(100) NOT NULL,
    start_bit INTEGER NOT NULL CHECK (start_bit >= 0),
    bit_length INTEGER NOT NULL CHECK (bit_length > 0),
    byte_order VARCHAR(10) CHECK (byte_order IN ('Motorola', 'Intel')) DEFAULT 'Intel',
    is_signed BOOLEAN DEFAULT FALSE,
    is_float BOOLEAN DEFAULT FALSE,
    factor NUMERIC(20, 10) DEFAULT 1.0,
    offset NUMERIC(20, 10) DEFAULT 0.0,
    min_value NUMERIC(20, 10),
    max_value NUMERIC(20, 10),
    unit VARCHAR(50),
    initial_value NUMERIC(20, 10),
    receiver_ecus TEXT,
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_signal_name UNIQUE (message_id, signal_name)
);
CREATE INDEX idx_can_signals_message_id ON can_signals(message_id);


CREATE TABLE signal_value_descriptions (
    id SERIAL PRIMARY KEY,
    signal_id INTEGER NOT NULL REFERENCES can_signals(id) ON DELETE CASCADE,
    raw_value INTEGER NOT NULL,
    phys_description VARCHAR(255) NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_signal_value UNIQUE (signal_id, raw_value)
);
CREATE INDEX idx_signal_value_desc_signal_id ON signal_value_descriptions(signal_id);