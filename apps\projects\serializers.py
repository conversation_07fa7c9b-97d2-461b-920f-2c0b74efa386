from rest_framework import serializers


class ProjectListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class ProjectSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255)
    number = serializers.CharField(max_length=255)
    related_people = serializers.CharField()
    msg_effective_time_start = serializers.TimeField(required=False, default="")
    msg_effective_time_end = serializers.TimeField(required=False, default="")


class ProjectList2Serializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)


class ProjectVersionReleaseSerializer(serializers.Serializer):
    projectId = serializers.CharField(max_length=255)
    type = serializers.CharField(max_length=255)
    property = serializers.CharField(max_length=255)
    num = serializers.CharField(max_length=255)


class ProjectExtraInfoByNumberSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=255)


class ProjectConfigSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    number = serializers.CharField(max_length=255)
    dbc = serializers.FileField(required=False)
    configs = serializers.JSONField()


class TestCaseStatsSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=255)
    action_type = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class ProjectModulesListSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)


class ProjectModulesSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)
    module_ids = serializers.ListSerializer(child=serializers.IntegerField(), min_length=0)


class ProjectInspectionItemListSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)


class ProjectProductVersionTRSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)


class ProjectInspectionItemSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)
    item_ids = serializers.ListSerializer(child=serializers.IntegerField(), min_length=0)
