class AppDatabaseRouter:
    # 定义应用与数据库的映射关系
    APP_DB_MAPPING = {
        'code_project_info': 'default',
        'code_branch_info': 'default',
        'chip': 'hwcp',
        'chip_pin_config': 'hwcp',
        'chip_gpio': 'hwcp',
        'chip_adc': 'hwcp',
        'chip_pwm': 'hwcp',
        'chip_iic': 'hwcp',
        'chip_spi': 'hwcp',
        'chip_uart': 'hwcp',
        'chip_exit': 'hwcp',
        'chip_map': 'hwcp',
        'chip_pin_type': 'hwcp',
        'chip_module': 'hwcp',
        'node_tree': 'hwcp',
        'user_defined_config': 'hwcp',
        'user_defined_option': 'hwcp',
        'level1_group': 'hwcp',
        'level2_main_param': 'hwcp',
        'level3_sub_param': 'hwcp',
        'main_param_enum': 'hwcp',
        'sub_param_enum_relation': 'hwcp',
        'main_special_enum': 'hwcp'
    }

    def _get_db(self, app_label):
        """获取应用对应的数据库，没有匹配时返回None"""
        return self.APP_DB_MAPPING.get(app_label)

    def db_for_read(self, model, **hints):
        """读取操作的数据库路由"""
        return self._get_db(model._meta.app_label)

    def db_for_write(self, model, **hints):
        """写入操作的数据库路由"""
        return self._get_db(model._meta.app_label)

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """控制迁移操作的数据库路由"""
        target_db = self._get_db(app_label)

        # 如果应用在映射中，返回是否匹配目标数据库
        if target_db:
            return db == target_db

        return None