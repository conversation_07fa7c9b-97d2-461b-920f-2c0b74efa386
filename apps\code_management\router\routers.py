class AppDatabaseRouter:
    # 定义应用与数据库的映射关系
    APP_DB_MAPPING = {
        'code_project_info': 'default',
        'code_branch_info': 'default',
        'branch_defined_value': 'hwcp',
        'branch_memory_defind': 'hwcp',
        'branch_memory_table_defind': 'hwcp',
        'chip': 'hwcp',
        'chip_adc': 'hwcp',
        'chip_dataflash': 'hwcp',
        'chip_exit': 'hwcp',
        'chip_gpio': 'hwcp',
        'chip_iic': 'hwcp',
        'chip_map': 'hwcp',
        'chip_module': 'hwcp',
        'chip_pin_config': 'hwcp',
        'chip_pin_type': 'hwcp',
        'chip_pwm': 'hwcp',
        'chip_spi': 'hwcp',
        'chip_uart': 'hwcp',
        'dataflash': 'hwcp',
        'group_config_table': 'hwcp',
        'level1_group': 'hwcp',
        'level2_main_param': 'hwcp',
        'level3_sub_param': 'hwcp',
        'luminance_cure': 'hwcp',
        'luminance_defind_value': 'hwcp',
        'main_param_enum': 'hwcp',
        'main_param_unit8': 'hwcp',
        'main_special_enum': 'hwcp',
        'memory_config_info': 'hwcp',
        'memory_info': 'hwcp',
        'memory_table_info': 'hwcp',
        'node_tree': 'hwcp',
        'param_info_table': 'hwcp',
        'param_origin_location': 'hwcp',
        'sub_param_enum_relation': 'hwcp',
        'user_defined_config': 'hwcp',
        'user_defined_option': 'hwcp',
        'CodePath': 'hwcp'
    }

    def _get_db(self, app_label):
        """获取应用对应的数据库，没有匹配时返回None"""
        return self.APP_DB_MAPPING.get(app_label)

    def db_for_read(self, model, **hints):
        """读取操作的数据库路由"""
        return self._get_db(model._meta.app_label)

    def db_for_write(self, model, **hints):
        """写入操作的数据库路由"""
        return self._get_db(model._meta.app_label)

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """控制迁移操作的数据库路由"""
        target_db = self._get_db(app_label)

        # 如果应用在映射中，返回是否匹配目标数据库
        if target_db:
            return db == target_db

        return None