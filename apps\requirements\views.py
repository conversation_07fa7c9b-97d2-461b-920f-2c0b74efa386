import traceback
import logging
from datetime import datetime

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from .serializers import (
    RequirementListSerializer, RequirementRelateTestCasesSerializer, RequirementRelatedTestCasesSerializer,
    RemoteRequirementsByTestCaseSerializer,
)
from utils.fs_service import FSService
from users.models import UserFSInfo
from test_cases.models import TestCaseRequirementMap
from .models import RequirementModel

logger = logging.getLogger("project")

fs_service = FSService()


class RequirementsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = RequirementListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            f, data = fs_service.get_requirement_list(token=token, **serializer.validated_data)
            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            content = {
                "count": data.get("data").get("total") if data.get("data") else 0,
                "results": data.get("data").get("records") if data.get("data") else [],
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class RequirementDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            f, data = fs_service.get_requirement_detail(token=token, requirement_id=pk)
            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            content = data.get("data")

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class RequirementRelateTestCasesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = RequirementRelateTestCasesSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            requirement_id = serializer.validated_data.get("requirement_id")
            requirement_version = serializer.validated_data.get("requirement_version")
            requirement_number = serializer.validated_data.get("requirement_number")
            test_cases = serializer.validated_data.get("test_cases")

            for tc in test_cases:
                TestCaseRequirementMap.objects.get_or_create(
                    test_case_id=tc.get("id"),
                    test_case_version=tc.get("version"),
                    requirement_id=requirement_id,
                    requirement_version=requirement_version,
                    defaults={
                        "requirement_number": requirement_number,
                        'create_time': datetime.now()
                    }
                )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class RequirementRemoveTestCasesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = RequirementRelateTestCasesSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            requirement_id = serializer.validated_data.get("requirement_id")
            requirement_version = serializer.validated_data.get("requirement_version")
            test_cases = serializer.validated_data.get("test_cases")

            for tc in test_cases:
                TestCaseRequirementMap.objects.filter(
                    test_case_id=tc.get("id"),
                    test_case_version=tc.get("version"),
                    requirement_id=requirement_id,
                    requirement_version=requirement_version,
                ).delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class RequirementRelatedTestCasesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = RequirementRelatedTestCasesSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            requirement_id = serializer.validated_data.get("requirement_id")
            requirement_version = serializer.validated_data.get("requirement_version")

            content = RequirementModel().get_test_cases_by_requirement(requirement_id, requirement_version)

            return Response({"err_code": 0, "msg": "ok", "data": content}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class RemoteRequirementsByTestCaseView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = RemoteRequirementsByTestCaseSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            test_cases = serializer.validated_data.get("test_cases")
            project_id = serializer.validated_data.get("project_id")
            test_type = serializer.validated_data.get("test_type")

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            f, data = fs_service.get_requirements_by_case_numbers(token, project_id, test_type, test_cases)
            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            content = {}
            for k in data.get("data"):
                reqs = data.get("data").get(k)
                content[k] = []
                for req in reqs:
                    module = req.get("module")
                    num = req.get("num")
                    version = req.get("version")
                    num = num.split("-")
                    num.insert(-1, module)
                    num = "-".join(num)
                    # num = num + "(" + version + ")"
                    content[k].append(num)

            return Response({"err_code": 0, "msg": "ok", "data": content}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class RemoteRequirementsByTestCaseView2(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = RemoteRequirementsByTestCaseSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            test_cases = serializer.validated_data.get("test_cases")
            project_id = serializer.validated_data.get("project_id")
            test_type = serializer.validated_data.get("test_type")

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            f, data = fs_service.get_requirements_by_case_numbers(token, project_id, test_type, test_cases)
            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            content = {}
            for k in data.get("data"):
                reqs = data.get("data").get(k)
                content[k] = []
                for req in reqs:
                    module = req.get("module")
                    num = req.get("num")
                    version = req.get("version")
                    num = num.split("-")
                    num.insert(-1, module)
                    num = "-".join(num)
                    content[k].append({
                        "id": req.get("id"),
                        "number": num,
                        "version": version
                    })

            return Response({"err_code": 0, "msg": "ok", "data": content}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
