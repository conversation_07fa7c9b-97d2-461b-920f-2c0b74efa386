
from django.db import models
from rest_framework import serializers
from django.contrib.postgres.fields import ArrayField


class CodePath(models.Model):
    id = models.AutoField(primary_key=True)
    node_level = models.CharField(max_length=100)  # 严格NOT NULL
    var_name = models.CharField(max_length=100)    # 严格NOT NULL
    chip = models.CharField(max_length=100)        # 新增的chip字段，严格NOT NULL
    full_path = models.CharField(max_length=255)   # 严格NOT NULL
    file_name = models.CharField(max_length=255)   # 严格NOT NULL

    class Meta:
        db_table = 'code_paths'  # 明确指定schema和表名
        app_label = 'code_paths'
        managed = False  # 不允许Django管理表结构

class CodePathSerializer(serializers.ModelSerializer):
    class Meta:
        model = CodePath
        fields = '__all__'  # 序列化所有字段
