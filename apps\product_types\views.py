import logging
import traceback

from django.core.paginator import Paginator
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication

from .ProductType import ProductType
from .serializers import (
    ProductTypeListSerializer, ProductTypeSerializer, ProductTypeUpdateSerializer,ProductTypeByMNumberSerializer
)

logger = logging.getLogger("product_types")


class ProductTypeView(APIView):
    # 登录认证【如果是调试模式可以屏蔽以下代码】
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = ProductTypeListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            page = serializer.validated_data.get("page", 1)
            pagesize = serializer.validated_data.get("pagesize", 10)
            name_re = serializer.validated_data.get("name_re")
            number_re = serializer.validated_data.get("number_re")

            obj_list = ProductType.objects.all()
            if name_re is not None and name_re != "" :
                obj_list = ProductType.objects.filter(name__iregex=name_re)
            if number_re is not None and number_re != "" :
                obj_list = ProductType.objects.filter(m_number__iregex=number_re)


            obj_list = obj_list.order_by("-id")

            paginator = Paginator(obj_list, pagesize)

            page_obj = paginator.get_page(page)

            content = {
                "count": paginator.count,
                "results": list(page_obj.object_list.values())
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            # 指定人员才有权限增加产品类型数据
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            serializer = ProductTypeSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            ProductType.objects.create(**serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProductTypeDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            obj = ProductType.objects.get(id=pk)
            return Response({"err_code": 0, "data": obj.to_dict()}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            serializer = ProductTypeUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            name = serializer.validated_data.get("name")
            desc = serializer.validated_data.get("desc")

            obj = ProductType.objects.get(id=pk)

            obj.name = name
            obj.desc = desc

            obj.save()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            result = ProductType.objects.get(id=pk)
            result.delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

