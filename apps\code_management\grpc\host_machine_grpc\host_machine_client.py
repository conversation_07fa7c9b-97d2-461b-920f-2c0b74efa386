import grpc
import json

from . import grpc_proto_pb2
from . import grpc_proto_pb2_grpc
# import grpc_proto_pb2
# import grpc_proto_pb2_grpc


# 定义全局服务地址
# SERVICE_ADDRESS = '**********:50052'
# SERVICE_ADDRESS = 'localhost:50052'
SERVICE_ADDRESS = '**********:50052'

# 连接复用（可选：在程序启动时创建全局channel）
def get_stub():
    channel = grpc.insecure_channel(SERVICE_ADDRESS)
    return grpc_proto_pb2_grpc.HWCPServiceStub(channel)

def initProject_serve(sdk_path):
    try:
        print("sdk_path:", sdk_path)
        stub = get_stub()
        response = stub.InitProject(grpc_proto_pb2.InitProjectParams(sdk_path=sdk_path))
        print("初始化状态:", response)
        return response
    except Exception as e:
        print("调用 serve 异常，初始化失败:", e)
        return None  # 或自行定义错误对象

def getProjectTree_serve(sdk_path):
    # sdk_path = "F:\project\HWCP\hiwaysdk_2.0\Application"
    # project_path = "F:\project\HWCP\code\project_2"
    # branch_status = "0"
    try:
        print("sdk_path:", sdk_path)
        stub = get_stub()
        response = stub.GetProjectTreeData(
            grpc_proto_pb2.GetProjectTreeDataParams(sdk_path=sdk_path)
        )
        print("返回的功能模块:", response)
        return response
    except Exception as e:
        print("调用 serve 异常，获取功能模块...", e)
        return []

def push_configChipFunctions_serve(sdk_path, chip_type, functions, project_path, branch_status ):
    # sdk_path = "F:\project\HWCP\hiwaysdk_2.0\Application"
    # project_path = "F:\project\HWCP\code\project_2"
    # branch_status = "0"
    try:
        print("sdk_path:", sdk_path)
        print("chip_type:", chip_type)
        print("functions:", functions)
        print("project_path:", project_path)
        print("branch_status:", branch_status)
        stub = get_stub()
        # 这里用 join 拼接字符串
        functions_str = "&".join(functions)
        print("functions_str:", functions_str)
        # functions_str = "Brightness&DataFlash&Temperature&Touch"
        # print("functions_str:", functions_str)

        response = stub.ConfigChipFunctions(
            grpc_proto_pb2.ConfigChipFunctionsParams(
                work_space_info=grpc_proto_pb2.WorkSpaceInfo(
                    sdk_path=sdk_path,
                    project_path=project_path,
                    branch_status=branch_status

                ),
                chip_type=chip_type,
                functions=functions_str,

            )
        )
        print("返回的配置详情信息状态:", response)
        return response
    except Exception as e:
        print("调用 serve 异常，获取配置详情信息信息失败...", e)
        return None

def get_configInfo_serve(sdk_path, sub_function,project_path, branch_status ):
    # sdk_path = "F:\project\HWCP\hiwaysdk_2.0\Application"
    # project_path = "F:\project\HWCP\code\project_2"
    # branch_status = "0"
    try:
        # print("sub_function:", sub_function)
        stub = get_stub()

        response = stub.GetSubFunctionItems(
            grpc_proto_pb2.SubFunction(
                work_space_info=grpc_proto_pb2.WorkSpaceInfo(
                    sdk_path=sdk_path,
                    project_path=project_path,
                    branch_status=branch_status

                ),
                project_tree_item=sub_function,

            )
        )
        print("配置子项信息状态:", response)
        return response
    except Exception as e:
        print("调用 serve 异常，配置子项信息获取失败...", e)
        return None


def get_configParms_serve(sdk_path,project_path, branch_status, module_name, config_module_name, config_para_path, config_para_type, config_para_name, config_para_value):
    # sdk_path = "F:\project\HWCP\hiwaysdk_2.0\Application"
    # project_path = "F:\project\HWCP\code\project_2"
    # branch_status = "0"
    try:
        stub = get_stub()
        # 本地调式
        # sdk_path = "F:\project\HWCP\hiwaysdk_2.0\Application"
        # project_path = "F:\project\HWCP\T29\code"
        # branch_status = "0"
        # channel = grpc.insecure_channel("**********:50052")
        # stub = grpc_proto_pb2_grpc.HWCPServiceStub(channel)
        # # #
        response = stub.UpdateConfigItem(
            grpc_proto_pb2.ConfigParams(
                work_space_info=grpc_proto_pb2.WorkSpaceInfo(
                    sdk_path=sdk_path,
                    project_path=project_path,
                    branch_status=branch_status

                ),
                config_para_path=config_para_path,
                config_module_name=config_module_name,
                config_para_type=config_para_type,
                config_para_name=config_para_name,
                config_para_value=config_para_value,
                module_name=module_name
            )
        )
        print("提交配置子项状态:", response)
        return response
    except Exception as e:
        print("调用 serve 异常，提交配置子项信息失败...", e)
        return None




def modify_chip_parms(sdk_path, project_path, branch_status, chip_name, config_module, item, name, pin_id):
    try:
        stub = get_stub()
        # sdk_path=r"F:\project\HWCP\hiwaysdk_2.0\Application"
        # project_path=r"F:\project\HWCP\code\project_2"
        # branch_status="0"
        # print("---------------------------", str(item))
        # channel = grpc.insecure_channel("**********:50052")
        # stub = grpc_proto_pb2_grpc.HWCPServiceStub(channel)
        response = stub.UpdateGPIOConfigItem(
            grpc_proto_pb2.UpdateGPIOConfigItemParams(
                config_module=str(config_module),
                config_item=str(item),
                config_value="",
                chip_name=str(chip_name),
                pin_id=str(pin_id),
                name=str(name),
                work_space_info=grpc_proto_pb2.WorkSpaceInfo(
                    sdk_path=sdk_path,
                    project_path=project_path,
                    branch_status=branch_status

                ),
            )
        )
        print("修改mcu引脚信息状态:", response)
        return response
    except Exception as e:
        print("调用 serve 异常，修改mcu引脚信息失败...", e)
        return None



if __name__ == "__main__":
    # res = getProjectTree_serve(sdk_path="12")
    # print(res)
    #
    # tree = push_configChipFunctions_serve(sdk_path="123", chip_type="RH850F1KM", functions="123", project_path="123", branch_status="123")
    # print(tree)
    #
    # info = get_configInfo_serve(sdk_path="123", sub_function="Brightness/Config_Brightness", project_path="123", branch_status="566")
    # print(info)
    #
    # update = get_configParms_serve(sdk_path="123", config_path="Brightness/Config_Brightness/背光IC选择", config_value="NONE", project_path="123", branch_status="456")
    # print(update)

    sdk_path = r"F:\project\HWCP\test\hiwaysdk_2.0\Application"
    project_path = r"F:\project\HWCP\test\project"
    branch_status = "0"
    config_module = "SPI"
    config_item = "spi_type"
    config_value = "硬件SPI"
    chip_name = "FC4150"
    pin_id = "7"
    name = "debug"
    modify = modify_chip_parms(sdk_path, config_module, config_item, config_value, chip_name, pin_id, name, project_path, branch_status)