import logging

from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger("machine")


class OperationLogMiddleware(MiddlewareMixin):
    def process_request(self, request):
        user = request.user if request.user.is_authenticated else 'Anonymous'
        method = request.method
        path = request.path
        logger.info(f'User: {user}, Method: {method}, Path: {path}')

    def process_response(self, request, response):
        status_code = response.status_code
        logger.info(f'Status Code: {status_code}')
        return response

    def process_exception(self, request, exception):
        logger.error(f'Exception: {exception}', exc_info=True)
        return None
