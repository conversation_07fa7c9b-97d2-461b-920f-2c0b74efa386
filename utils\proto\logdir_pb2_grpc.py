# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from . import logdir_pb2 as logdir__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in logdir_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class LogDirServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GenerateDatesToKeep = channel.unary_unary(
                '/logdir.LogDirService/GenerateDatesToKeep',
                request_serializer=logdir__pb2.Empty.SerializeToString,
                response_deserializer=logdir__pb2.LogDirResponse.FromString,
                _registered_method=True)
        self.DeleteOldDirs = channel.unary_unary(
                '/logdir.LogDirService/DeleteOldDirs',
                request_serializer=logdir__pb2.LogDirRequest.SerializeToString,
                response_deserializer=logdir__pb2.OperationStatus.FromString,
                _registered_method=True)


class LogDirServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GenerateDatesToKeep(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteOldDirs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LogDirServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GenerateDatesToKeep': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateDatesToKeep,
                    request_deserializer=logdir__pb2.Empty.FromString,
                    response_serializer=logdir__pb2.LogDirResponse.SerializeToString,
            ),
            'DeleteOldDirs': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteOldDirs,
                    request_deserializer=logdir__pb2.LogDirRequest.FromString,
                    response_serializer=logdir__pb2.OperationStatus.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'logdir.LogDirService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('logdir.LogDirService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class LogDirService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GenerateDatesToKeep(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.LogDirService/GenerateDatesToKeep',
            logdir__pb2.Empty.SerializeToString,
            logdir__pb2.LogDirResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteOldDirs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.LogDirService/DeleteOldDirs',
            logdir__pb2.LogDirRequest.SerializeToString,
            logdir__pb2.OperationStatus.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class GitlabServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ListAllProjects = channel.unary_unary(
                '/logdir.GitlabService/ListAllProjects',
                request_serializer=logdir__pb2.Empty.SerializeToString,
                response_deserializer=logdir__pb2.ListAllProjectsResponse.FromString,
                _registered_method=True)
        self.SyncProjects = channel.unary_unary(
                '/logdir.GitlabService/SyncProjects',
                request_serializer=logdir__pb2.Project.SerializeToString,
                response_deserializer=logdir__pb2.ProjectSyncRequest.FromString,
                _registered_method=True)
        self.CreateOrGetMergeRequest = channel.unary_unary(
                '/logdir.GitlabService/CreateOrGetMergeRequest',
                request_serializer=logdir__pb2.CreateOrGetMergeRequestRequest.SerializeToString,
                response_deserializer=logdir__pb2.CreateOrGetMergeRequestResponse.FromString,
                _registered_method=True)
        self.QueryMRStatus = channel.unary_unary(
                '/logdir.GitlabService/QueryMRStatus',
                request_serializer=logdir__pb2.QueryMRStatusRequest.SerializeToString,
                response_deserializer=logdir__pb2.QueryMRStatusResponse.FromString,
                _registered_method=True)
        self.MergeMR = channel.unary_unary(
                '/logdir.GitlabService/MergeMR',
                request_serializer=logdir__pb2.MergeMRRequest.SerializeToString,
                response_deserializer=logdir__pb2.MergeMRResponse.FromString,
                _registered_method=True)
        self.GitCommit = channel.unary_unary(
                '/logdir.GitlabService/GitCommit',
                request_serializer=logdir__pb2.GitCommitRequest.SerializeToString,
                response_deserializer=logdir__pb2.GitCommitResponse.FromString,
                _registered_method=True)
        self.GitPush = channel.unary_unary(
                '/logdir.GitlabService/GitPush',
                request_serializer=logdir__pb2.GitPushRequest.SerializeToString,
                response_deserializer=logdir__pb2.GitPushResponse.FromString,
                _registered_method=True)
        self.GetProjectBranches = channel.unary_unary(
                '/logdir.GitlabService/GetProjectBranches',
                request_serializer=logdir__pb2.GetProjectBranchesRequest.SerializeToString,
                response_deserializer=logdir__pb2.GetProjectBranchesResponse.FromString,
                _registered_method=True)
        self.CloneRepository = channel.unary_unary(
                '/logdir.GitlabService/CloneRepository',
                request_serializer=logdir__pb2.CloneRepositoryRequest.SerializeToString,
                response_deserializer=logdir__pb2.CloneRepositoryResponse.FromString,
                _registered_method=True)
        self.GetSubgroupPaths = channel.unary_unary(
                '/logdir.GitlabService/GetSubgroupPaths',
                request_serializer=logdir__pb2.GetSubgroupPathsRequest.SerializeToString,
                response_deserializer=logdir__pb2.GetSubgroupPathsResponse.FromString,
                _registered_method=True)
        self.CreateRepository = channel.unary_unary(
                '/logdir.GitlabService/CreateRepository',
                request_serializer=logdir__pb2.CreateRepositoryRequest.SerializeToString,
                response_deserializer=logdir__pb2.CreateRepositoryResponse.FromString,
                _registered_method=True)
        self.CreateBranch = channel.unary_unary(
                '/logdir.GitlabService/CreateBranch',
                request_serializer=logdir__pb2.CreateBranchRequest.SerializeToString,
                response_deserializer=logdir__pb2.CreateBranchResponse.FromString,
                _registered_method=True)
        self.GetProjectTags = channel.unary_unary(
                '/logdir.GitlabService/GetProjectTags',
                request_serializer=logdir__pb2.GetProjectTagsRequest.SerializeToString,
                response_deserializer=logdir__pb2.GetProjectTagsResponse.FromString,
                _registered_method=True)
        self.CreateWebhook = channel.unary_unary(
                '/logdir.GitlabService/CreateWebhook',
                request_serializer=logdir__pb2.CreateWebhookRequest.SerializeToString,
                response_deserializer=logdir__pb2.CreateWebhookResponse.FromString,
                _registered_method=True)


class GitlabServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def ListAllProjects(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SyncProjects(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateOrGetMergeRequest(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueryMRStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MergeMR(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GitCommit(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GitPush(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetProjectBranches(self, request, context):
        """获取项目的分支
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CloneRepository(self, request, context):
        """克隆项目到仓库 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSubgroupPaths(self, request, context):
        """拿到固定的组 比如muc-team下面的子组
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateRepository(self, request, context):
        """创建仓库
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateBranch(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetProjectTags(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateWebhook(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GitlabServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ListAllProjects': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAllProjects,
                    request_deserializer=logdir__pb2.Empty.FromString,
                    response_serializer=logdir__pb2.ListAllProjectsResponse.SerializeToString,
            ),
            'SyncProjects': grpc.unary_unary_rpc_method_handler(
                    servicer.SyncProjects,
                    request_deserializer=logdir__pb2.Project.FromString,
                    response_serializer=logdir__pb2.ProjectSyncRequest.SerializeToString,
            ),
            'CreateOrGetMergeRequest': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateOrGetMergeRequest,
                    request_deserializer=logdir__pb2.CreateOrGetMergeRequestRequest.FromString,
                    response_serializer=logdir__pb2.CreateOrGetMergeRequestResponse.SerializeToString,
            ),
            'QueryMRStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.QueryMRStatus,
                    request_deserializer=logdir__pb2.QueryMRStatusRequest.FromString,
                    response_serializer=logdir__pb2.QueryMRStatusResponse.SerializeToString,
            ),
            'MergeMR': grpc.unary_unary_rpc_method_handler(
                    servicer.MergeMR,
                    request_deserializer=logdir__pb2.MergeMRRequest.FromString,
                    response_serializer=logdir__pb2.MergeMRResponse.SerializeToString,
            ),
            'GitCommit': grpc.unary_unary_rpc_method_handler(
                    servicer.GitCommit,
                    request_deserializer=logdir__pb2.GitCommitRequest.FromString,
                    response_serializer=logdir__pb2.GitCommitResponse.SerializeToString,
            ),
            'GitPush': grpc.unary_unary_rpc_method_handler(
                    servicer.GitPush,
                    request_deserializer=logdir__pb2.GitPushRequest.FromString,
                    response_serializer=logdir__pb2.GitPushResponse.SerializeToString,
            ),
            'GetProjectBranches': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProjectBranches,
                    request_deserializer=logdir__pb2.GetProjectBranchesRequest.FromString,
                    response_serializer=logdir__pb2.GetProjectBranchesResponse.SerializeToString,
            ),
            'CloneRepository': grpc.unary_unary_rpc_method_handler(
                    servicer.CloneRepository,
                    request_deserializer=logdir__pb2.CloneRepositoryRequest.FromString,
                    response_serializer=logdir__pb2.CloneRepositoryResponse.SerializeToString,
            ),
            'GetSubgroupPaths': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSubgroupPaths,
                    request_deserializer=logdir__pb2.GetSubgroupPathsRequest.FromString,
                    response_serializer=logdir__pb2.GetSubgroupPathsResponse.SerializeToString,
            ),
            'CreateRepository': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateRepository,
                    request_deserializer=logdir__pb2.CreateRepositoryRequest.FromString,
                    response_serializer=logdir__pb2.CreateRepositoryResponse.SerializeToString,
            ),
            'CreateBranch': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateBranch,
                    request_deserializer=logdir__pb2.CreateBranchRequest.FromString,
                    response_serializer=logdir__pb2.CreateBranchResponse.SerializeToString,
            ),
            'GetProjectTags': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProjectTags,
                    request_deserializer=logdir__pb2.GetProjectTagsRequest.FromString,
                    response_serializer=logdir__pb2.GetProjectTagsResponse.SerializeToString,
            ),
            'CreateWebhook': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateWebhook,
                    request_deserializer=logdir__pb2.CreateWebhookRequest.FromString,
                    response_serializer=logdir__pb2.CreateWebhookResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'logdir.GitlabService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('logdir.GitlabService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class GitlabService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def ListAllProjects(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/ListAllProjects',
            logdir__pb2.Empty.SerializeToString,
            logdir__pb2.ListAllProjectsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SyncProjects(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/SyncProjects',
            logdir__pb2.Project.SerializeToString,
            logdir__pb2.ProjectSyncRequest.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateOrGetMergeRequest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/CreateOrGetMergeRequest',
            logdir__pb2.CreateOrGetMergeRequestRequest.SerializeToString,
            logdir__pb2.CreateOrGetMergeRequestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def QueryMRStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/QueryMRStatus',
            logdir__pb2.QueryMRStatusRequest.SerializeToString,
            logdir__pb2.QueryMRStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MergeMR(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/MergeMR',
            logdir__pb2.MergeMRRequest.SerializeToString,
            logdir__pb2.MergeMRResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GitCommit(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/GitCommit',
            logdir__pb2.GitCommitRequest.SerializeToString,
            logdir__pb2.GitCommitResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GitPush(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/GitPush',
            logdir__pb2.GitPushRequest.SerializeToString,
            logdir__pb2.GitPushResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetProjectBranches(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/GetProjectBranches',
            logdir__pb2.GetProjectBranchesRequest.SerializeToString,
            logdir__pb2.GetProjectBranchesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CloneRepository(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/CloneRepository',
            logdir__pb2.CloneRepositoryRequest.SerializeToString,
            logdir__pb2.CloneRepositoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSubgroupPaths(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/GetSubgroupPaths',
            logdir__pb2.GetSubgroupPathsRequest.SerializeToString,
            logdir__pb2.GetSubgroupPathsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateRepository(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/CreateRepository',
            logdir__pb2.CreateRepositoryRequest.SerializeToString,
            logdir__pb2.CreateRepositoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateBranch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/CreateBranch',
            logdir__pb2.CreateBranchRequest.SerializeToString,
            logdir__pb2.CreateBranchResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetProjectTags(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/GetProjectTags',
            logdir__pb2.GetProjectTagsRequest.SerializeToString,
            logdir__pb2.GetProjectTagsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateWebhook(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.GitlabService/CreateWebhook',
            logdir__pb2.CreateWebhookRequest.SerializeToString,
            logdir__pb2.CreateWebhookResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class ProjectScannerServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ScanProject = channel.unary_unary(
                '/logdir.ProjectScannerService/ScanProject',
                request_serializer=logdir__pb2.ProjectScanRequest.SerializeToString,
                response_deserializer=logdir__pb2.ProjectScanResponse.FromString,
                _registered_method=True)
        self.AnalyzeCode = channel.unary_unary(
                '/logdir.ProjectScannerService/AnalyzeCode',
                request_serializer=logdir__pb2.CodeAnalyzeRequest.SerializeToString,
                response_deserializer=logdir__pb2.CodeAnalyzeResponse.FromString,
                _registered_method=True)


class ProjectScannerServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def ScanProject(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AnalyzeCode(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ProjectScannerServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ScanProject': grpc.unary_unary_rpc_method_handler(
                    servicer.ScanProject,
                    request_deserializer=logdir__pb2.ProjectScanRequest.FromString,
                    response_serializer=logdir__pb2.ProjectScanResponse.SerializeToString,
            ),
            'AnalyzeCode': grpc.unary_unary_rpc_method_handler(
                    servicer.AnalyzeCode,
                    request_deserializer=logdir__pb2.CodeAnalyzeRequest.FromString,
                    response_serializer=logdir__pb2.CodeAnalyzeResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'logdir.ProjectScannerService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('logdir.ProjectScannerService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ProjectScannerService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def ScanProject(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.ProjectScannerService/ScanProject',
            logdir__pb2.ProjectScanRequest.SerializeToString,
            logdir__pb2.ProjectScanResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AnalyzeCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/logdir.ProjectScannerService/AnalyzeCode',
            logdir__pb2.CodeAnalyzeRequest.SerializeToString,
            logdir__pb2.CodeAnalyzeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
