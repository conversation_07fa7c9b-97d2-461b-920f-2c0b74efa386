from django.db import models
from django.contrib.auth.models import AbstractUser


class User(AbstractUser):
    username = models.Char<PERSON><PERSON>(max_length=255, unique=False, null=False)
    employee_number = models.Char<PERSON>ield(max_length=255, unique=True, null=False)
    phone_number = models.CharField(max_length=255)
    open_id = models.CharField(max_length=255)
    job_title = models.Char<PERSON>ield(max_length=255)
    job_level = models.Char<PERSON>ield(max_length=255)
    avatar = models.TextField()

    USERNAME_FIELD = 'employee_number'

    REQUIRED_FIELDS = ["username", "email"]

    class Meta:
        db_table = 'user'


class UserFSInfo(models.Model):
    employee_number = models.Char<PERSON>ield(max_length=255, primary_key=True, null=False)
    access_token = models.TextField()
    refresh_token = models.TextField()
    token_type = models.Char<PERSON><PERSON>(max_length=255)
    expires_in = models.IntegerField()
    scope = models.Char<PERSON><PERSON>(max_length=255)
    jti = models.Char<PERSON>ield(max_length=255)
    update_time = models.DateTimeField()

    class Meta:
        db_table = 'user_fs_info'
