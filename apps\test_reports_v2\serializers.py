from rest_framework import serializers


class TestReportsListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    project_number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    m_version = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)

