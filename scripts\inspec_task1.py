import os
import sys
import datetime

import django

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)
os.chdir(BASE_DIR)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'atpms.settings')
django.setup()

from inspecs.models import InspecItem, InspecPerson, InspecTime, InspecRecord
from django.db import transaction, close_old_connections


def create_inspec_time(today):
    inspec_time = InspecTime.objects.filter(date=today).first()

    if inspec_time:
        inspec_person = InspecPerson.objects.filter(on_shift=True).first()
        return inspec_person, inspec_time

    with transaction.atomic():
        o_inspec_person = InspecPerson.objects.filter(on_shift=True).first()

        if not o_inspec_person:
            n_inspec_person = InspecPerson.objects.filter(is_available=True, is_active=True).first()
        else:
            n_inspec_person = InspecPerson.objects.filter(
                is_available=True, is_active=True,
                order__gt=o_inspec_person.order
            ).order_by('order').first()
            if not n_inspec_person:
                n_inspec_person = InspecPerson.objects.filter(is_available=True, is_active=True).order_by(
                    'order').first()

            o_inspec_person.on_shift = False
            o_inspec_person.save()

        if not n_inspec_person:
            raise Exception("No available inspection person found.")

        n_inspec_person.on_shift = True
        n_inspec_person.save()

        inspec_time = InspecTime.objects.create(
            date=today
        )

    return n_inspec_person, inspec_time


def create_inspec_records(person_id, time_id):
    if InspecRecord.objects.filter(person_id=person_id, time_id=time_id).exists():
        return

    items = InspecItem.objects.filter(is_active=True).order_by('id').all()
    with transaction.atomic():
        for item in items:
            InspecRecord.objects.create(
                item_id=item.id,
                time_id=time_id,
                person_id=person_id,
            )


def main():
    try:
        today = datetime.date.today()

        if today.weekday() >= 5:  # Only run on weekdays
            return

        inspec_person, inspec_time = create_inspec_time(today)

        create_inspec_records(inspec_person.id, inspec_time.id)
    except Exception:
        close_old_connections()


if __name__ == '__main__':
    main()
