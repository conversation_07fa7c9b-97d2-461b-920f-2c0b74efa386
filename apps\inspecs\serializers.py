from rest_framework import serializers


class GetInspecItemSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    # code = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class CreateInspecItemSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    # code = serializers.Char<PERSON>ield(max_length=255)
    desc = serializers.CharField()


class UpdateInspecItemSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255)
    desc = serializers.CharField()


class GetInspecPersonSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    user_name = serializers.Char<PERSON><PERSON>(max_length=255, required=False, allow_blank=True, allow_null=True)
    status = serializers.BooleanField(required=False, default=None, allow_null=True)


class CreateInspecPersonSerializer(serializers.Serializer):
    user_email = serializers.CharField(max_length=255)
    user_name = serializers.CharField(max_length=255)


class UpdateInspecPersonSerializer(serializers.Serializer):
    is_available = serializers.BooleanField(default=True)


class GetInspecRecordSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    start_time = serializers.DateTimeField(required=False, allow_null=True)
    end_time = serializers.DateTimeField(required=False, allow_null=True)
    item_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    # item_code = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class UpdateInspecRecordSerializer(serializers.Serializer):
    is_pass = serializers.BooleanField(default=True)
    desc = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class DispatchTaskSerializer(serializers.Serializer):
    person_id = serializers.IntegerField()


class RecordsDownloadSerializer(serializers.Serializer):
    start_time = serializers.DateTimeField(required=False, allow_null=True)
    end_time = serializers.DateTimeField(required=False, allow_null=True)
