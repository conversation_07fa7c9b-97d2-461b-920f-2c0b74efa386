
from django.db import models
from rest_framework import serializers
from django.contrib.postgres.fields import ArrayField


class ChipDataflash(models.Model):
    id = models.AutoField(primary_key=True)
    var_name = models.CharField(max_length=100)
    node_level = models.Char<PERSON>ield(max_length=50)
    group = models.CharField(max_length=100)
    param_key = models.CharField(max_length=255)
    name = models.Char<PERSON>ield(max_length=255)
    desc = models.CharField(max_length=255)
    default = models.CharField(max_length=255)
    type = models.CharField(max_length=100)
    chip = models.CharField(max_length=100)


    class Meta:
        db_table = 'dataflash'  # 对应数据库表名
        app_label = 'dataflash'  # 可根据实际应用调整
        managed = False  # 不允许Django自动管理表结构


class ChipDataflashSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipDataflash
        fields = '__all__'  # 序列化所有字段


class MemoryInfo(models.Model):
    id = models.AutoField(primary_key=True)
    var_name = models.Char<PERSON>ield(max_length=100, null=False)
    node_level = models.Char<PERSON>ield(max_length=100, null=False)
    group_key = models.CharField(max_length=255, null=False)
    group_name = models.CharField(max_length=255, null=False)
    param_key = models.CharField(max_length=255, null=False)
    param_name = models.CharField(max_length=255, null=False)
    desc = models.CharField(max_length=255, null=True, blank=True)
    type = models.CharField(max_length=100, null=False)
    default_value = models.CharField(max_length=100, null=True, blank=True)
    min = models.CharField(max_length=100, null=True, blank=True)
    max = models.CharField(max_length=100, null=True, blank=True)
    config_type = models.CharField(max_length=100, null=True)

    class Meta:
        db_table = 'memory_info'  # 对应数据库表名
        app_label = 'memory_info'  # 请替换为实际应用标签
        managed = False  # 不允许Django自动管理表结构

class MemoryInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = MemoryInfo
        fields = '__all__'  # 序列化所有字段


class MemoryTableInfo(models.Model):
    id = models.AutoField(primary_key=True)
    var_name = models.CharField(max_length=100, null=False)  # 变量名称
    node_level = models.CharField(max_length=100, null=False)  # 节点层级
    index_row = models.IntegerField(null=False)  # 序号
    address_index = models.CharField(max_length=50, null=False, db_column="address_index")  # 地址信息索引号（兼容字段名）
    address_info = models.CharField(max_length=20, null=False)  # 存储地址
    data_length = models.CharField(max_length=20, null=False)  # 数据长度
    default_value = ArrayField(models.TextField(null=False), default=list)# 存储默认值（数组以文本形式存储）
    read_from_flash = models.CharField(max_length=50, null=False)  # 是否从Flash读取
    desc = models.CharField(max_length=100, null=True, blank=True, db_column="desc")  # 描述（兼容关键字字段）

    class Meta:
        db_table = 'memory_table_info'  # 对应数据库表名
        app_label = 'memory'  # 替换为实际应用标签
        managed = False  # 不允许Django自动管理表结构

class MemoryTableInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = MemoryTableInfo
        fields = '__all__'  # 序列化所有字段


class ParamInfoTable(models.Model):
    id = models.AutoField(primary_key=True)
    var_name = models.CharField(max_length=100, null=False)  # 变量名称
    node_level = models.CharField(max_length=100, null=False)  # 节点层级
    index_col = models.IntegerField(null=False)  # 参数列序号
    param_key = models.CharField(max_length=50, null=False)  # 参数键名
    param_name = models.CharField(max_length=50, null=False)  # 参数名称
    param_type = models.CharField(max_length=20, null=False)  # 参数类型（如enum、uint32_t）

    class Meta:
        db_table = 'param_info_table'  # 对应数据库表名
        app_label = 'memory'  # 替换为实际应用标签
        managed = False  # 不允许Django自动管理表结构

class ParamInfoTableSerializer(serializers.ModelSerializer):
    class Meta:
        model = ParamInfoTable
        fields = '__all__'  # 序列化所有字段



class MemoryConfigInfo(models.Model):
    id = models.AutoField(primary_key=True)
    var_name = models.CharField(max_length=100, null=False)  # 变量名称
    node_level = models.CharField(max_length=100, null=False)  # 节点层级
    group_key = models.CharField(max_length=50, null=False)  # 分组键名
    belong = models.CharField(max_length=50, null=False)  # 所属类型
    xml_path = models.CharField(max_length=255, null=False)  # XML路径
    chip = models.CharField(max_length=50, null=False)  # 芯片型号

    class Meta:
        db_table = 'memory_config_info'  # 对应数据库表名
        app_label = 'memory'  # 替换为实际应用标签
        managed = False  # 不允许Django自动管理表结构

class MemoryConfigInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = MemoryConfigInfo
        fields = '__all__'  # 序列化所有字段




class BranchMemoryDefind(models.Model):
    # 自增主键
    id = models.AutoField(primary_key=True)

    # 公共字段
    project_code = models.CharField(
        max_length=100,
        null=False,
        verbose_name="项目编码"
    )
    project_name = models.CharField(
        max_length=200,
        null=False,
        verbose_name="项目名称"
    )
    project_gitlab = models.CharField(
        max_length=255,
        null=False,
        verbose_name="GitLab地址"
    )
    project_branch = models.CharField(
        max_length=100,
        null=False,
        verbose_name="项目分支"
    )
    node_level = models.CharField(
        max_length=100,
        null=False,
        verbose_name="节点层级"
    )
    var_name = models.CharField(
        max_length=100,
        null=False,
        verbose_name="变量名称"
    )

    # 业务字段
    memory_info = models.JSONField(
        verbose_name="文本默认值",
        blank=True
    )


    class Meta:
        db_table = "branch_memory_defind"  # 对应数据库表名
        app_label = "branch_memory_defind"  # 替换为实际应用标签
        managed = False  # 不允许Django自动管理表结构



# 序列化器（用于API数据交互）
class BranchMemoryDefindSerializer(serializers.ModelSerializer):
    class Meta:
        model = BranchMemoryDefind
        fields = "__all__"





class BranchMemoryTableDefind(models.Model):
    # 自增主键（对应SERIAL类型）
    id = models.AutoField(primary_key=True, verbose_name="ID")

    # 公共关联字段（与其他表保持一致，确保关联查询）
    project_code = models.CharField(
        max_length=100,
        null=False,
        verbose_name="项目编码"
    )
    project_name = models.CharField(
        max_length=200,
        null=False,
        verbose_name="项目名称"
    )
    project_gitlab = models.CharField(
        max_length=255,
        null=False,
        verbose_name="GitLab地址"
    )
    project_branch = models.CharField(
        max_length=100,
        null=False,
        verbose_name="项目分支"
    )
    node_level = models.CharField(
        max_length=100,
        null=False,
        verbose_name="节点层级"
    )
    var_name = models.CharField(
        max_length=100,
        null=False,
        verbose_name="变量名称"
    )

    # 业务字段
    row_index = models.CharField(
        max_length=50,
        null=False,
        verbose_name="行索引"
    )
    memory_table = models.JSONField(
        max_length=255,
        null=True,  # 数据库表中未显式设置NOT NULL，允许为空
        blank=True,
        verbose_name="内存表数据"
    )

    class Meta:
        # 数据库表名映射（若模型名与表名一致可省略，此处显式指定）
        db_table = "branch_memory_table_defind"
        app_label = "branch_memory_table_defind"  # 替换为实际应用标签
        managed = False  # 不允许Django自动管理表结构


    def __str__(self):
        # 便于在Admin后台或日志中识别记录
        return f"{self.project_name}-{self.var_name}-{self.row_index}"
