"""
Django settings for atpms project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
import os
import sys
import json
from datetime import timed<PERSON>ta

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

sys.path.insert(0, os.path.join(BASE_DIR, "apps"))

CONF_PATH = os.path.join(BASE_DIR, "config", "config.json")

with open(CONF_PATH, "rb") as f:
    CONF = json.load(f)

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-x33bf@c#71#j!af3)mkgcp5gsnq73v7x1pa2t)o3^!$ru=cro3'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["*"]

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'django_crontab',
    "users"
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # 'operation_logs.middleware.OperationLogMiddleware',
]

REST_FRAMEWORK = {
    # Use Django's standard `django.contrib.auth` permissions,
    # or allow read-only access for unauthenticated users.
    'DEFAULT_PERMISSION_CLASSES': [
        # 'rest_framework.permissions.DjangoModelPermissionsOrAnonReadOnly',
        # 'rest_framework_simplejwt.authentication.JWTAuthentication',
    ]
}

CRONJOBS = [
    ('0 1 * * *', 'scripts.inspec_task1'),
    ('30 5 * * *', 'scripts.inspec_task2'),
]

CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_HEADERS = ('*')
CORS_EXPOSE_HEADERS = ['Content-Disposition']

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(hours=24),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=2),
    "ROTATE_REFRESH_TOKENS": False,
    "BLACKLIST_AFTER_ROTATION": False,
    "UPDATE_LAST_LOGIN": False,

    "ALGORITHM": "HS256",
    "SIGNING_KEY": SECRET_KEY,
    "VERIFYING_KEY": "",
    "AUDIENCE": None,
    "ISSUER": None,
    "JSON_ENCODER": None,
    "JWK_URL": None,
    "LEEWAY": 0,

    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "USER_AUTHENTICATION_RULE": "rest_framework_simplejwt.authentication.default_user_authentication_rule",

    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "TOKEN_USER_CLASS": "rest_framework_simplejwt.models.TokenUser",

    "JTI_CLAIM": "jti",

    "SLIDING_TOKEN_REFRESH_EXP_CLAIM": "refresh_exp",
    "SLIDING_TOKEN_LIFETIME": timedelta(minutes=5),
    "SLIDING_TOKEN_REFRESH_LIFETIME": timedelta(days=1),

    "TOKEN_OBTAIN_SERIALIZER": "rest_framework_simplejwt.serializers.TokenObtainPairSerializer",
    "TOKEN_REFRESH_SERIALIZER": "rest_framework_simplejwt.serializers.TokenRefreshSerializer",
    "TOKEN_VERIFY_SERIALIZER": "rest_framework_simplejwt.serializers.TokenVerifySerializer",
    "TOKEN_BLACKLIST_SERIALIZER": "rest_framework_simplejwt.serializers.TokenBlacklistSerializer",
    "SLIDING_TOKEN_OBTAIN_SERIALIZER": "rest_framework_simplejwt.serializers.TokenObtainSlidingSerializer",
    "SLIDING_TOKEN_REFRESH_SERIALIZER": "rest_framework_simplejwt.serializers.TokenRefreshSlidingSerializer",
}

ROOT_URLCONF = 'atpms.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'atpms.wsgi.application'

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',  # 数据库引擎
        'NAME': 'atpms',  # 数据库名
        # 'USER': 'atpms',  # 账号
        # 'PASSWORD': 'TgeIJgD9',  # 密码
        # 'HOST': '*********',  # HOST
        'USER': 'postgres',
        'PASSWORD': 'cO0b9jtx',  # 密码
        'HOST': '*********',  # HOST
        'PORT': 5432,  # 端口
    },
    'hwcp': {
        'ENGINE': 'django.db.backends.postgresql',  # 数据库引擎
        'NAME': 'hwcp',  # 数据库名
        'USER': 'postgres',  # 账号
        'PASSWORD': 'Hw@2023',  # 密码
        'HOST': '*********',  # HOST
        'PORT': 5432,  # 端口
    }
}

# settings.py
DATABASE_ROUTERS = ['code_management.router.routers.AppDatabaseRouter']

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}


AUTH_USER_MODEL = 'users.User'

LOGIN_URL = '/users/login2'

APPEND_SLASH = False

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media/')

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 配置日志
LOG_DIR = os.path.join(BASE_DIR, "logs")
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {  # 日志显示格式
        'standard': {
            'format': '[%(asctime)s][%(threadName)s:%(thread)d][task_id:%(name)s][%(filename)s:%(lineno)d][%(levelname)s][%(message)s]'
        },
        'simple': {
            'format': '%(asctime)s - %(levelname)s - %(name)s - %(threadName)s - %(filename)s:%(lineno)d in %(funcName)s - %(message)s'
        },
    },
    'filters': {  # 对日志进行过滤
        'require_debug_true': {  # debug模式下输出日志
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'filters': ['require_debug_true'],
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
        'django': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", "django.log"),  # 日志文件的位置
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        'error': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", "error.log"),  # 日志文件的位置
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "user": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'user.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "user_debug": {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'user_debug.log'),
            'formatter': 'simple',
            'filters': ['require_debug_true'],
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1,
        },
        "machine": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'machine.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "code_management": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'code_management.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
            'encoding': 'utf-8',
        },
        "product_versions": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'product_versions.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "diff_tool": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'diff_tool.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "project": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'project.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "machine_debug": {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'machine_debug.log'),
            'formatter': 'simple',
            'filters': ['require_debug_true'],
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1,
        },
        "fs": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'fs.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "fs_app": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'fs_app.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "auto_test": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'auto_test.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "operation_logs": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'operation_logs.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "testqueue": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'testqueue.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "softtrack": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'softtrack.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "auto_jenkins": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'auto_jenkins.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
        "celery": {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, "logs", 'celery.log'),
            'formatter': 'simple',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 10,
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'django', "error"],
            'propagate': True,
            'level': 'INFO',
        },
        "user": {
            'handlers': ["user", "user_debug", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "machine": {
            'handlers': ["machine", "machine_debug", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "code_management": {
            'handlers': ["code_management", "error"],
            'level': 'DEBUG',
            'propagate': True,
         },
        "product_versions": {
            'handlers': ["product_versions", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "diff_tool": {
            'handlers': ["diff_tool", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "project": {
            'handlers': ["project", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "fs": {
            'handlers': ["fs", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "fs_app": {
            'handlers': ["fs_app", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "auto_test": {
            'handlers': ["auto_test", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "operation_logs": {
            'handlers': ["operation_logs", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "testqueue": {
            'handlers': ["testqueue", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "softtrack": {
            'handlers': ["softtrack", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "auto_jenkins": {
            'handlers': ["auto_jenkins", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
        "celery": {
            'handlers': ["celery", "error"],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}


CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/1'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_RESULT_EXPIRES = 7 * 24 * 60 * 60  # 7天后过期
CELERY_TIMEZONE = 'Asia/Shanghai'