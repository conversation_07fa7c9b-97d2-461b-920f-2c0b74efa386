CREATE TABLE IF NOT EXISTS public.test_products(
    id serial primary key,
    "name" varchar(255) not null,
    "type" varchar(50), -- e.g., "assembly", "component",
    m_number varchar(255),
    hardware_versions J<PERSON>NB NOT NULL DEFAULT '[]'::<PERSON><PERSON><PERSON><PERSON>,
    software_versions J<PERSON>NB NOT NULL DEFAULT '[]'::<PERSON><PERSON><PERSON><PERSON>,
    project_name varchar(255),
    project_number varchar(255),
    project_id varchar(255),
    creator_name varchar(255),
    creator_email varchar(255),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE public.test_products OWNER TO atpms;

CREATE TABLE IF NOT EXISTS public.test_product_components(
    id SERIAL PRIMARY KEY,
    parent_id INTEGER NOT NULL,
    child_id INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (parent_id) REFERENCES public.test_products(id)
        ON DELETE CASCADE,
    <PERSON>OREI<PERSON><PERSON> KEY (child_id) REFERENCES public.test_products(id)
        ON DELETE CASCADE,

    CONSTRAINT no_self_reference CHECK (parent_id <> child_id),
    CONSTRAINT unique_relationship UNIQUE (parent_id, child_id)
);
ALTER TABLE public.test_product_components OWNER TO atpms;

