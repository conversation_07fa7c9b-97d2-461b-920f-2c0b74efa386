import os
import sys
import datetime
import logging

import django

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)
os.chdir(BASE_DIR)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'atpms.settings')
django.setup()

from django.db import close_old_connections

from utils.sql_helper import sql_fetchall_dict, sql_execute
from utils.fs_app import fs_app

logger = logging.getLogger("machine_reservation_check")


def first_check():
    now = datetime.datetime.now()
    sql = """
        select mr.id, mr.start_time, mr.end_time, u.email, m.name as machine_name, mr.project, mr.content,
            u.username
        from public.machine_reservations as mr left join public.user as u
        on mr.employee_number = u.employee_number left join public.machines as m 
        on mr.machine_id = m.id left join public.machine_reservation_notification as n
        on mr.id = n.machine_reservation_id
        where mr.start_time >= %(start)s AND mr.start_time <= %(end)s AND mr.status = 1
        AND n.id is NULL 
        ;
    """
    params = {
        "start": now,
        "end": now + datetime.timedelta(minutes=10)
    }
    results = sql_fetchall_dict(sql, params)

    for result in results:
        title = "快到机台预约时间，请及时签到使用机台"
        machine = result.get("machine_name")
        start_time = result.get("start_time").strftime("%Y-%m-%d %H:%M:%S")
        end_time = result.get("end_time").strftime("%Y-%m-%d %H:%M:%S")
        project = str(result.get("project"))
        content = str(result.get("content"))
        # applicant = str(result.get("username"))
        executive_personnel = str(result.get("executive_personnel"))
        r_id = str(result.get("id"))
        f, data = fs_app.send_machine_reserve_msg(
            result.get("email"), title, machine, start_time, end_time, project, content, executive_personnel, r_id
        )
        if f:
            sql = """
                    insert into public.machine_reservation_notification (machine_reservation_id, create_time)
                    values (%(machine_reservation_id)s, %(create_time)s);
                """
            params = {
                "machine_reservation_id": result.get("id"),
                "create_time": now
            }
            sql_execute(sql, params)


def second_check():
    now = datetime.datetime.now()
    sql = """
        select mr.id, mr.start_time, mr.end_time, u.email, m.name as machine_name, mr.project, mr.content,
            u.username
        from public.machine_reservations as mr left join public.user as u
        on mr.employee_number = u.employee_number left join public.machines as m 
        on mr.machine_id = m.id left join public.machine_reservation_notification2 as n
        on mr.id = n.machine_reservation_id
        where mr.start_time <= %(start)s AND mr.status = 1
        AND n.id is NULL 
        ;
    """
    params = {
        "start": now
    }
    results = sql_fetchall_dict(sql, params)

    for result in results:
        title = "机台预约时间已到，请及时签到使用机台"
        machine = result.get("machine_name")
        start_time = result.get("start_time").strftime("%Y-%m-%d %H:%M:%S")
        end_time = result.get("end_time").strftime("%Y-%m-%d %H:%M:%S")
        project = str(result.get("project"))
        content = str(result.get("content"))
        # applicant = str(result.get("username"))
        executive_personnel = str(result.get("executive_personnel"))
        r_id = str(result.get("id"))
        f, data = fs_app.send_machine_reserve_msg(
            result.get("email"), title, machine, start_time, end_time, project, content, executive_personnel, r_id
        )
        if f:
            sql = """
                insert into public.machine_reservation_notification2 (machine_reservation_id, create_time)
                values (%(machine_reservation_id)s, %(create_time)s);
            """
            params = {
                "machine_reservation_id": result.get("id"),
                "create_time": now
            }
            sql_execute(sql, params)


def third_check():
    now = datetime.datetime.now()

    sql = """
        update public.machine_reservations
            set status = 4,
                update_time = %(update_time)s
        where status = 1 and start_time < %(start)s
        ;
    """
    params = {
        "update_time": now,
        "start": now - datetime.timedelta(minutes=10)
    }
    sql_execute(sql, params)

    sql = """
        select mr.id, mr.start_time, mr.end_time, u.email, m.name as machine_name, mr.project, mr.content,
            u.username
        from public.machine_reservations as mr left join public.user as u
        on mr.employee_number = u.employee_number left join public.machines as m 
        on mr.machine_id = m.id left join public.machine_reservation_notification3 as n
        on mr.id = n.machine_reservation_id
        where mr.status = 4
        AND n.id is NULL 
        ;
    """
    params = None
    results = sql_fetchall_dict(sql, params)

    for result in results:
        title = "机台预约超期未签到，已释放。"
        machine = result.get("machine_name")
        start_time = result.get("start_time").strftime("%Y-%m-%d %H:%M:%S")
        end_time = result.get("end_time").strftime("%Y-%m-%d %H:%M:%S")
        project = str(result.get("project"))
        content = str(result.get("content"))
        # applicant = str(result.get("username"))
        executive_personnel = str(result.get("executive_personnel"))
        f, data = fs_app.send_machine_reservation_time_out_msg(
            result.get("email"), title, machine, start_time, end_time, project, content, executive_personnel
        )
        if f:
            sql = """
                insert into public.machine_reservation_notification3 (machine_reservation_id, create_time)
                values (%(machine_reservation_id)s, %(create_time)s);
            """
            params = {
                "machine_reservation_id": result.get("id"),
                "create_time": now
            }
            sql_execute(sql, params)


def reservation_approval_check():
    sql = """
        update public.machine_reservations
            set status = 6,
                update_time = %(update_time)s
            where status = 0 and start_time < %(start)s
        ;
    """
    now = datetime.datetime.now()
    params = {
        "update_time": now,
        "start": now
    }
    sql_execute(sql, params)


def machine_reservation_time_check():
    first_check()

    second_check()

    third_check()

    reservation_approval_check()

    sql = """
        update public.machine_reservations
            set status = 5,
                update_time = %(update_time)s
            where status = 3 and end_time < %(end)s
        ;
    """
    now = datetime.datetime.now()
    params = {
        "update_time": now,
        "end": now
    }
    sql_execute(sql, params)


if __name__ == '__main__':
    try:
        machine_reservation_time_check()
    except Exception:
        close_old_connections()
