CREATE TABLE public.dataflash (
    id INT PRIMARY KEY,
    var_name VARCHAR(100),
    node_level VARCHAR(50),
    "group" VARCHAR(100),
    param_key VARCHAR(255),
    "name" VARCHAR(255),  -- 列名用双引号（非单引号）
    "desc" VARCHAR(255),
    "default" VARCHAR(255),  -- default 是关键字，必须用双引号
    "type" VARCHAR(100),
    chip VARCHAR(100)
);

ALTER TABLE public.dataflsh
ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY;

CREATE TABLE public.memory_info (
    id SERIAL PRIMARY KEY,
    var_name VARCHAR(100) NOT NULL,
    node_level VARCHAR(100) NOT NULL,
    group_key VARCHAR(255) NOT NULL,
    group_name VARCHAR(255) NOT NULL,
    param_key VARCHAR(255) NOT NULL,
    param_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    "desc" VARCHAR(255),
    type VARCHA<PERSON>(100) NOT NULL,
    default_value VARCHAR(100),
    "min" VARCHAR(100),
    "max" VARCHAR(100),
    config_type VARCHAR(100)
);


-- 创建地址信息表并插入完整数据
CREATE TABLE memory_table_info (
    id SERIAL PRIMARY KEY,
    var_name VARCHAR(100) NOT NULL,
    node_level VARCHAR(100) NOT NULL,
    index_row INT NOT NULL,
    "address_index" VARCHAR(50) NOT NULL,
    address_info VARCHAR(20) NOT NULL,
    data_length VARCHAR(20) NOT NULL,
    default_value TEXT[] NOT NULL,
    read_from_flash VARCHAR(50) NOT NULL,
    "desc" VARCHAR(100)
);


-- 创建参数信息表并插入数据
CREATE TABLE param_info_table (
    id SERIAL PRIMARY KEY,
    var_name VARCHAR(100) NOT NULL,
    node_level VARCHAR(100) NOT NULL,
    index_col INT NOT NULL,
    param_key VARCHAR(50) NOT NULL,
    param_name VARCHAR(50) NOT NULL,
    param_type VARCHAR(20) NOT NULL
);



-- 创建分组配置表并插入数据
CREATE TABLE memory_config_info (
    id SERIAL PRIMARY KEY,
    var_name VARCHAR(100) NOT NULL,
    node_level VARCHAR(100) NOT NULL,
    group_key VARCHAR(50) NOT NULL,
    belong VARCHAR(50) NOT NULL,
    xml_path VARCHAR(255) NOT NULL,
    chip VARCHAR(50) NOT NULL
);




CREATE TABLE branch_memory_defind (
    id SERIAL PRIMARY KEY,
    -- 公共字段（与表一一致，确保关联一致性）
    project_code VARCHAR(100) NOT NULL,
    project_name VARCHAR(200) NOT NULL,
    project_gitlab VARCHAR(255) NOT NULL,
    project_branch VARCHAR(100) NOT NULL,
    node_level VARCHAR(100) NOT NULL,
    var_name VARCHAR(100) NOT NULL,
    -- 业务字段
    memory_info VARCHAR(255) NOT NULL,  -- 参数键（如size、num）
    memory_table VARCHAR(255)  -- 参数默认值（如0x800、11，允许为空）

);



CREATE TABLE branch_memory_table_defind (
    id SERIAL PRIMARY KEY,
    -- 公共字段（与表一一致，确保关联一致性）
    project_code VARCHAR(100) NOT NULL,
    project_name VARCHAR(200) NOT NULL,
    project_gitlab VARCHAR(255) NOT NULL,
    project_branch VARCHAR(100) NOT NULL,
    node_level VARCHAR(100) NOT NULL,
    var_name VARCHAR(100) NOT NULL,
    -- 业务字段
    row_index VARCHAR(50) NOT null,
    memory_table VARCHAR(255)
);
