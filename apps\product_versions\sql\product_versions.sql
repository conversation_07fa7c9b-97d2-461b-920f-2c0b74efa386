--测试计划表
CREATE TABLE IF NOT EXISTS public.product_versions(
    id serial primary key, --id
    project_name varchar(255),   --项目名称
    project_number varchar(255),   --项目编号
    project_id varchar(255),   --项目id
    "type" varchar(255),   --版本类型
    type_name varchar(255),   --版本类型名称
    use varchar(255),   --版本用途
    use_name varchar(255),   --版本用途名称
    version_number varchar(255),   --版本号
    version_name varchar(255),   --版本名称
    version_package_address varchar(255),   --版本包地址
    plan_version varchar(255),   --计划版本号
    customer_version varchar(255),   --客户版本号
    "desc" text, --版本描述
    publisher_name varchar(255),   --发布人名称
    publisher_email varchar(255),   --发布人邮箱
    publish_time timestamp, --发布时间
    finish_time timestamp, --完成时间
    --状态 待提测 PENDING 已提测 SUBMITTED 测试中 TESTING 测试不通过 FAILED 测试通过 PASSED 已发布 RELEASED 已量产 PRODUCTION
    -- 冒烟测试通过 SMOKE_TEST_PASSED 特殊版本发布 SPECIAL_VERSION_RELEASED 废弃 DEPRECATED
    status varchar(255),
    remark varchar(255),
    qac_url varchar(255),
    tessy_url varchar(255),
--    attachment_url varchar(255),
    sub_version_info text, --子版本信息
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.product_versions OWNER TO atpms;

CREATE TABLE IF NOT EXISTS public.person_product_version_map(
    id serial primary key, --id
    person_name varchar(255) not null, --人员名称
    person_email varchar(255) not null, --人员邮箱
    product_version_id INT not null, --产品版本ID
    unique(person_email, product_version_id)
);
ALTER TABLE public.person_product_version_map OWNER TO atpms;