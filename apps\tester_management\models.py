from django.db import models

from utils.sql_helper import sql_fetchall_dict


class TesterRole(models.Model):
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=255)
    desc = models.TextField(null=True, blank=True)
    testers = models.ManyToManyField('Tester', through='TesterTesterRoleMap', related_name='roles')
    skills = models.ManyToManyField('TesterSkill', through='TesterRoleSkillMap', related_name='roles')
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'tester_roles'
        managed = False
        app_label = 'tester_management'


class Tester(models.Model):
    name = models.CharField(max_length=255)
    email = models.EmailField(max_length=255)
    department = models.CharField(max_length=255, null=True, blank=True)
    group = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    roles = models.ManyToMany<PERSON>ield(
        TesterRole, through='TesterTesterRoleMap', related_name='testers'
    )
    work_start_date = models.DateField()
    work_experience = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=255, null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'testers'
        managed = False
        app_label = 'tester_management'


class TesterTesterRoleMap(models.Model):
    tester = models.ForeignKey(Tester, on_delete=models.CASCADE, db_column='tester_id')
    role = models.ForeignKey(TesterRole, on_delete=models.CASCADE, db_column='role_id')
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'tester_tester_role_map'
        managed = False
        app_label = 'tester_management'


class TesterSkill(models.Model):
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=255, unique=True)
    department = models.CharField(max_length=255)
    type = models.ForeignKey('TesterSkillTypes', on_delete=models.SET_NULL, db_column='type_id')
    desc = models.TextField(null=True, blank=True)
    roles = models.ManyToManyField('TesterRole', through='TesterRoleSkillMap', related_name='skills')
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'tester_skills'
        managed = False
        app_label = 'tester_management'


class TesterSkillTypes(models.Model):
    name = models.CharField(max_length=255)
    desc = models.TextField(null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'tester_skill_types'
        managed = False
        app_label = 'tester_management'


class TesterSkillScore(models.Model):
    tester = models.ForeignKey('Tester', on_delete=models.CASCADE)
    role = models.ForeignKey('TesterRole', on_delete=models.CASCADE)
    skill = models.ForeignKey('TesterSkill', on_delete=models.CASCADE)
    score = models.IntegerField()
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'tester_skill_scores'
        managed = False
        app_label = 'tester_management'


class TesterRoleSkillMap(models.Model):
    role = models.ForeignKey('TesterRole', on_delete=models.CASCADE, db_column='role_id')
    skill = models.ForeignKey('TesterSkill', on_delete=models.CASCADE, db_column='skill_id')
    max_score = models.IntegerField()
    weight = models.IntegerField()
    order = models.IntegerField()
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'tester_role_skill_map'
        managed = False
        app_label = 'tester_management'


def get_user_skills(user_id):
    sql = """
    SELECT
        t.id AS tester_id,
        t.name AS tester_name,
        tr.id AS role_id,
        tr.name AS role_name,
        ts.id AS skill_id,
        ts.name AS skill_name,
        ts.desc AS skill_desc,
        COALESCE(tss.score, 0) AS score,
        trsm.max_score AS skill_max_score,
        trsm.weight AS skill_weight,
        trsm.order AS skill_order
    FROM
        testers t
        LEFT JOIN tester_tester_role_map ttrm ON t.id = ttrm.tester_id
        LEFT JOIN tester_roles tr ON ttrm.role_id = tr.id
        LEFT JOIN tester_role_skill_map trsm ON tr.id = trsm.role_id
        LEFT JOIN tester_skills ts ON trsm.skill_id = ts.id
        LEFT JOIN tester_skill_scores tss ON t.id = tss.tester_id AND tr.id = tss.role_id AND ts.id = tss.skill_id  
    WHERE
        t.id = %s
    ORDER BY
        trsm.order
    """
    r = sql_fetchall_dict(sql, (user_id,))

    data = {}
    for i in r:
        if data.get(i.get("role_id")) is None:
            data[i.get("role_id")] = []
        data[i.get("role_id")].append(i)

    return data


def get_role_skills(role_id):
    sql = """
    SELECT 
        tr.id AS role_id,
        tr.name AS role_name,
        tr.code AS role_code,
        ts.id AS skill_id,
        ts.name AS skill_name,
        ts.code AS skill_code,
        trsm.weight,
        trsm.max_score,
        trsm.order,
        ts.desc AS skill_desc
    FROM
        tester_roles tr
        RIGHT JOIN tester_role_skill_map trsm ON tr.id = trsm.role_id
        RIGHT JOIN tester_skills ts ON trsm.skill_id = ts.id
    WHERE
        tr.id = %s
    ORDER BY
        trsm.order
    """
    r = sql_fetchall_dict(sql, (role_id,))

    return r
