CREATE TABLE IF NOT EXISTS public.sd_requirement(
    id serial primary key, --id
    "title" varchar(255),  --名称,
    "module" varchar(255),  --模块,
    "plan" int,  --版本计划,
    "desc" text, --描述
    "verify" text, --验证
    "verify_method" jsonb, --验证方法
    "pri" int default 3, --优先级
    "estimate" decimal(12, 2) default 0, --预估工时
    "source" varchar(255), --来源
    "source_note" varchar(255), --来源备注
    "status" varchar(255) DEFAULT 'draft', --状态 'draft','active','closed','changed'
    "stage" varchar(255) DEFAULT 'wait', --阶段 'wait','planned','projected','developing', 'developed','testing','tested','verified','released'
    "version" int, --版本
    project_name varchar(255), --项目名称
    project_number varchar(255), --项目编号

    close_reason varchar(255), --关闭原因
    closer_name varchar(255),   --关闭人姓名
    closer_email varchar(255),   --关闭人邮箱
    close_time timestamp, --关闭时间
    assignee_name varchar(255),   --指派人姓名
    assignee_email varchar(255),   --指派人邮箱
    assign_time timestamp, --指派时间
    creator_name varchar(255),   --创建人姓名
    creator_email varchar(255),   --创建人邮箱
    create_time timestamp, --创建时间
    updater_name varchar(255),   --更新人姓名
    updater_email varchar(255),   --更新人邮箱
    update_time timestamp, --更新时间

    deleted boolean default false --是否删除
);
ALTER TABLE public.sd_requirement OWNER TO atpms;

CREATE TABLE IF NOT EXISTS public.sd_requirement_desc(
    "requirement" int,  --需求,
    "version" int default 1,  --需求版本,
    "title" varchar(255),
    "desc" text, --描述
    "verify" text, --验证
    "verify_method" jsonb, --验证方法
    creator_name varchar(255),   --创建人姓名
    creator_email varchar(255),   --创建人邮箱
    create_time timestamp, --创建时间
    updater_name varchar(255),   --更新人姓名
    updater_email varchar(255),   --更新人邮箱
    update_time timestamp, --更新时间
    PRIMARY KEY("requirement", "version")
);
ALTER TABLE public.sd_requirement_desc OWNER TO atpms;