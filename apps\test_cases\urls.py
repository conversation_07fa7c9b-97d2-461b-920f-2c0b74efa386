from django.urls import path

from .views import (
    TestCasesView, TestCaseDetailView, TestCaseDiscardView,
    PublicTestCasesView, PublicTestCaseDetailView, TestCaseMoveView, TestCaseRecoverView,
    TestCaseSyncView, TestCaseVersionUpdateView,
    TestCaseArchivesView, TestCaseArchiveDetailView,
    TestTypesView, TestCasePatchSyncView, TestCasePatchImportView,
    ProjectTestCaseCopyView, TestCaseDownloadView, TestCaseStepsUpdateView,
    TestCaseByNumberView, RelateRequirementsView, RemoveRequirementsView,
)

urlpatterns = [
    path("", TestCasesView.as_view()),

    path("/<int:pk>", TestCaseDetailView.as_view()),

    path("/<int:pk>/update_steps", TestCaseStepsUpdateView.as_view()),

    path("/<int:pk>/discard", TestCaseDiscardView.as_view()),

    path("/<int:pk>/recover", TestCaseRecoverView.as_view()),

    path("/<int:pk>/move", TestCaseMoveView.as_view()),

    path("/detail_by_number", TestCaseByNumberView.as_view()),

    path("/relate_requirements", RelateRequirementsView.as_view()),

    path("/remove_requirements", RemoveRequirementsView.as_view()),

    path("/sync", TestCaseSyncView.as_view()),

    path("/patch_sync", TestCasePatchSyncView.as_view()),

    path("/version_update", TestCaseVersionUpdateView.as_view()),

    path("/public", PublicTestCasesView.as_view()),

    path("/public/<int:pk>", PublicTestCaseDetailView.as_view()),

    path("/archives", TestCaseArchivesView.as_view()),

    path("/test_types", TestTypesView.as_view()),

    path("/archives/<int:pk>", TestCaseArchiveDetailView.as_view()),

    path("/patch_import", TestCasePatchImportView.as_view()),

    path("/copy_by_project", ProjectTestCaseCopyView.as_view()),

    path("/download", TestCaseDownloadView.as_view()),

]
