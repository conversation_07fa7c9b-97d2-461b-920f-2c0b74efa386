CREATE TABLE public.chip_pin_config (
    id SERIAL PRIMARY KEY,
    pin_id INTEGER NOT NULL,                           -- 引脚号
    chip VARCHAR(50) NOT NULL,                         -- 芯片名
    module VARCHAR(50) NOT NULL,                       -- 当前功能类型配置
    name VARCHAR(100),                                 -- 通道名称
    des VARCHAR(255),                                  -- 引脚注释
    func JSONB DEFAULT '{}'::jsonb,                    -- 当前配置类型（JSON）
    config JSONB DEFAULT '{}'::jsonb                   -- 当前配置（JSON）
);

ALTER TABLE public.chip_pin_config
ADD COLUMN project_code VARCHAR(50),
ADD COLUMN project_name VARCHAR(100),
ADD COLUMN project_gitlab VARCHAR(255),
ADD COLUMN project_branch VARCHAR(100);
