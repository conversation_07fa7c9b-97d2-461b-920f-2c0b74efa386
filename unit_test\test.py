import requests
import pprint
import json

# r = requests.get("http://127.0.0.1:9091/test_cases",
#                  params={
#                      "pagesize": 10000,
#                      "project_number": "ICSCN30",
#                      "execute_mode_list": ["AUTOMATED_EXECUTION", "SEMI_AUTOMATED_EXECUTION"]
#                  }
#                  )
#
# data = r.json().get("data").get("results2")
#
# with open("test.txt", "w") as f:
#     json.dump(data, f)
#
# print(len(data))

# pprint.pprint(data)

# with open("test.txt", "r") as f:
#     data = json.load(f)


# data.reverse()

import requests
import traceback
import pprint
import json

module_map = {
    "显示功能": "DISP",
    "光感功能": "LP",
    "触摸功能": "TOUCH",
    "电源管理": "POWER",
    "温控功能": "TEMP",
    "升级功能": "UPG",
    "网络通讯": "NETCOM",
    "故障模拟": "FLT",
    "电机功能": "EM",
    "噪音检测": "NVH",
    "主机产品功能": "CDC",
    "版本检测": "VER",
    "其他功能": "OTHER",
    "按键功能": "KEY",
    "摄像头功能": "CAM",
    "耐久测试": "STRESS",
}

action_type_map = {
    "系统合格性测试": "SAT",
    "系统合格性": "SAT",
    "系统集成测试": "IST",
    "软件合格性测试": "SQT",
    "软件集成测试": "SIT",
    "软件单元测试": "SUT",
    "硬件测试": "HT",
}

session = requests.session()

res = session.post("http://127.0.0.1:9091/users/login2",
                   json={"username": "<EMAIL>", "password": "hwtc@666"})

r = res.json()

if r.get("err_code") == 0:
    token = r.get("data").get("access_token")

    session.headers.update({"Authorization": f"Bearer {token}"})

    for case in data:
        for i in case.get("test_steps"):
            i["params"] = json.dumps(i["params"])
        try:
            res = session.post("http://127.0.0.1:9091/test_cases", json=case)

            print(res.json())
        except Exception:
            print(traceback.format_exc())
