import datetime
import os

import requests
from jproperties import Properties
from webdav3.client import Client

from utils.nextcloud import NextCloud

NEXTCLOUD_USERNAME = 'jenkins_ftp'
NEXTCLOUD_PASSWORD = 'u26l&mvO'
NEXTCLOUD_UID = '13CB0352-2A54-47C9-BCDE-F9C52A331E85'
# NEXTCLOUD_URL = 'https://ecm.hiwaytech.com/remote.php/dav/files/13CB0352-2A54-47C9-BCDE-F9C52A331E85'
NEXTCLOUD_URL = 'https://ecm.hiwaytech.com'
JENKINS_ROOT = 'jenkins_release'
to_js = True
AUTOTEST_ROOT = 'automated_test'

HW_PLATFORM = 'http://10.1.1.132:8010/project/version/public/saveVersion'
# HW_PLATFORM = 'http://10.1.1.146:8010/project/version/public/saveVersion'


cloud = NextCloud(endpoint=NEXTCLOUD_URL, user=NEXTCLOUD_USERNAME, password=NEXTCLOUD_PASSWORD, json_output=to_js)


def upload_dist_to_cloud(project, software_type, folder, dist, qac):
    """
    上传编译产物和QAC报告到Nextcloud平台
    :param project:
    :param software_type:
    :param folder:
    :param dist:
    :param qac:
    :return:
    """
    status = True
    try:
        if not cloud.check(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}'):
            cloud.mkdir(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}')

        if not cloud.check(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{software_type}'):
            temp = software_type.split('/')
            if len(temp) == 1:
                cloud.mkdir(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{software_type}')
            else:
                cloud.mkdir(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{temp[0]}')
                cloud.mkdir(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{temp[0]}/{temp[1]}')

        if not cloud.check(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{software_type}/{folder}'):
            cloud.mkdir(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{software_type}/{folder}')

        if cloud.check(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{software_type}/{folder}'):
            if os.path.exists(dist):
                name = os.path.basename(dist)
                cloud.upload(NEXTCLOUD_UID, dist, f'{JENKINS_ROOT}/{project}/{software_type}/{folder}/{name}')
                print(
                    f'Version Files Upload File Success: {NEXTCLOUD_URL}/{JENKINS_ROOT}/{project}/{software_type}/{folder}/{name}')

                if qac is not None and os.path.exists(qac):
                    cloud.upload(NEXTCLOUD_UID, qac, f'{JENKINS_ROOT}/{project}/{software_type}/{folder}/qac_reports')
                    print(
                        f'QAC Report Upload File Success: {NEXTCLOUD_URL}/{JENKINS_ROOT}/{project}/{software_type}/{folder}/qac_reports')
        else:
            status = False
    except Exception as e:
        status = False
        print(e.args)

    return status


def create_cloud_share(project, software_type, folder):
    """
    创建cloud平台的分享链接
    :param project:
    :param software_type:
    :param folder:
    :return:
    """
    response = cloud.create_share(f'{JENKINS_ROOT}/{project}/{software_type}/{folder}', 3)
    print(f'shared:{JENKINS_ROOT}/{project}/{software_type}/{folder}, response: {response}')
    if response.is_ok:
        share_url = response.data['url']
    else:
        # 默认是 jenkins_release 的目录
        share_url = 'https://ecm.hiwaytech.com/f/1042881'
    print(f'shared url: {share_url}')
    return share_url


def upload_version_to_hw(project, version, software_type, share):
    """
    将版本信息推送到产品开发平台
    :param project:
    :param version:
    :param software_type:
    :param share:
    :return:
    """
    configs = Properties()
    with open(version, 'rb') as config_file:
        configs.load(config_file)

    if software_type in ('MCU', 'MCU/Master', 'MCU/Slave'):
        desc = f'''
            发布日期：{datetime.datetime.today().strftime('%Y-%m-%d %H:%M:%S')}
            软件版本：{configs.get('MCU_SOFTWARE_VERSION').data}
            默认硬件版本：{configs.get('MCU_HARDWARE_VERSION').data}
            Boot版本：{configs.get('MCU_BOOT_VERSION').data}
            内部软件版本：{configs.get('MCU_SOFTWARE_IN_VERSION').data}
            其它版本：{configs.get('MCU_Other_VERSION').data}
            版本路径：{share}
        '''
        data = {
            'projectCode': f'{project}',
            'type': 'SOFTWARE',
            'property': 'RELEASE',
            'num': f"{configs.get('MCU_Folder_Name').data}",
            'description': desc
        }
    elif software_type == 'HMI':
        desc = f'''
            发布日期：{datetime.datetime.today().strftime('%Y-%m-%d %H:%M:%S')}
            软件版本：{configs.get('APP_VERSION').data}
            版本路径：{share}
        '''
        data = {
            'projectCode': f'{project}',
            'type': 'SOFTWARE',
            'property': 'RELEASE',
            'num': f"{configs.get('APP_VERSION').data}",
            'description': desc
        }
    elif software_type in ('VDS/OS', 'VDS/MCU', 'FCT/OS', 'OS'):
        desc = f'''
            发布日期：{datetime.datetime.today().strftime('%Y-%m-%d %H:%M:%S')}
            OS版本：{configs.get('OS_VERSION').data}
            版本路径：{share}
        '''
        data = {
            'projectCode': f'{project}',
            'type': 'SOFTWARE',
            'property': 'RELEASE',
            'num': f"{configs.get('OS_VERSION').data}",
            'description': desc
        }
    else:
        desc = f'''
            发布日期：{datetime.datetime.today().strftime('%Y-%m-%d %H:%M:%S')}
            软件版本：{configs.get('APP_VERSION').data}
            版本路径：{share}
        '''
        data = {
            'projectCode': f'{project}',
            'type': 'SOFTWARE',
            'property': 'RELEASE',
            'num': f"{configs.get('APP_VERSION').data}",
            'description': desc
        }

    print(HW_PLATFORM, data)
    statue = True
    response = requests.post(HW_PLATFORM, json=data)
    if response.status_code == 200:
        print(f'Version Upload to HW: {response.json()}')
        response_data = response.json()
        if response_data['code'] == 20000:
            print('Version Upload to HW Success')
        else:
            print(f"Version Upload to HW Failure: {response_data['message']}")
            statue = False
    else:
        print('Failed to send POST request:', response.status_code, response.reason)
        statue = False
    return statue


def upload(local_path, remote_path, remote_path_dir=None):
    if remote_path_dir:
        r = cloud.mkdir(NEXTCLOUD_UID, remote_path_dir)
        if not r.is_ok:
            return False, ""
    r = cloud.upload(NEXTCLOUD_UID, local_path, remote_path)
    if not r:
        return False, ""
    r = cloud.create_share(remote_path, 3)
    if r.is_ok:
        return True, r.data['url']
    else:
        return False, ""


def download_directory(remote_path, local_path):
    if not os.path.exists(local_path):
        return False, f"本地路径不存在: {local_path}"

    options = {
        'webdav_hostname': NEXTCLOUD_URL,
        'webdav_login': NEXTCLOUD_USERNAME,
        'webdav_password': NEXTCLOUD_PASSWORD,
        'webdav_root': f'/remote.php/dav/files/{NEXTCLOUD_UID}/',
        'disable_check': True,
        'verify': False,
    }
    client = Client(options)

    if not client.check(remote_path):
        return False, f"远程路径不存在: {remote_path}"

    try:
        client.download_sync(remote_path=remote_path, local_path=local_path)
    except Exception as e:
        return False, f"下载文件失败: {str(e)}"

    return True, ""


def upload_file(local_path, remote_path):
    if not os.path.exists(local_path):
        return False, f"本地路径不存在: {local_path}"

    options = {
        'webdav_hostname': NEXTCLOUD_URL,
        'webdav_login': NEXTCLOUD_USERNAME,
        'webdav_password': NEXTCLOUD_PASSWORD,
        'webdav_root': f'/remote.php/dav/files/{NEXTCLOUD_UID}/',
        'disable_check': True,
        'verify': False,
    }
    client = Client(options)

    if not client.check(remote_path):
        return False, f"远程路径不存在: {remote_path}"
    remote_path = f"{remote_path}/{os.path.basename(local_path)}"

    try:
        client.upload_sync(remote_path=remote_path, local_path=local_path)
        return True, ""
    except Exception as e:
        return False, f"上传文件失败: {str(e)}"


def copy_dir(src, dst):
    options = {
        'webdav_hostname': NEXTCLOUD_URL,
        'webdav_login': NEXTCLOUD_USERNAME,
        'webdav_password': NEXTCLOUD_PASSWORD,
        'webdav_root': f'/remote.php/dav/files/{NEXTCLOUD_UID}/',
        'disable_check': True,
        'verify': False,
        'timeout': 600,
    }
    client = Client(options)

    if not client.check(src):
        return False, f"源路径不存在: {src}"
    if not client.check(dst):
        client.mkdir(dst)

    try:
        client.copy(src, dst)
        return True, ""
    except Exception as e:
        return False, f"复制目录失败: {str(e)}"


if __name__ == '__main__':
    r = cloud.check(NEXTCLOUD_UID, "jenkins_release/")
    print(r)
