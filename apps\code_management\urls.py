# src/code_management/urls.py
from django.urls import path

from apps.code_management.views.project_views import (
    SdkInfoView,
    ConfigSubmitView,
    ProjectInfoView,
    DeleteProjectView,
    ProjectTeamView,
    GetBranchesView,
    ProjectByTeam,
    InitGrpcView
)

from apps.code_management.views.branch_views import (
    ProjectSpaceView,
    ProjectBranchView,
    BranchSubmitView
)

from apps.code_management.views.config_views import (
    ConfigParamsView,
    ConfigInfoView,
    ConfigCommitView,
    ConfigPushView,
    MergeProjectView
)

from apps.code_management.views.chip_views import (
    ChipViews,
    ChipConfigViews,
    ChipModulViews,
    ChipChangeViews,
    ChipTypeViews
)

from apps.code_management.views.memory_views import MeoryInfoView, MemoryChangeView, ADDMemoryView
# from apps.code_management.views.luminance_views import LumminanceViews
from apps.code_management.views.code_views import CodeFileView
from apps.code_management.views.curve_views import LumminanceViews, CurveView
from apps.code_management.views.sdk_views import SDKMapView

urlpatterns = [
    path('/get_branches', GetBranchesView.as_view()),
    path('/init_project', InitGrpcView.as_view()),
    path('/get_team', ProjectTeamView.as_view()),
    path('/projects_by_group', ProjectByTeam.as_view()),
    path('/config_submit', ConfigSubmitView.as_view()),
    path('/configuration_info', ConfigInfoView.as_view()),
    path('/config_params', ConfigParamsView.as_view()),
    path('/config_commit', ConfigCommitView.as_view()),
    path('/config_push', ConfigPushView.as_view()),
    path('/project_info', ProjectInfoView.as_view()),
    path('/branch_options', ProjectBranchView.as_view()),
    path('/space_options', ProjectSpaceView.as_view()),
    path('/sdk_info', SdkInfoView.as_view()),
    path('/branch_submit', BranchSubmitView.as_view()),
    path('/delete_project', DeleteProjectView.as_view()),
    path('/merge_project', MergeProjectView.as_view()),
    path('/chip_info', ChipViews.as_view()),
    path('/chip_config', ChipConfigViews.as_view()),
    path('/chip_modul', ChipModulViews.as_view()),
    path('/chip_change', ChipChangeViews.as_view()),
    path('/chip_type', ChipTypeViews.as_view()),
    path('/liminance_cure', LumminanceViews.as_view()),
    path('/memory_info', MeoryInfoView.as_view()),
    path('/memory_change', MemoryChangeView.as_view()),
    path('/file_content', CodeFileView.as_view()),
    path('/submit_curve', CurveView.as_view()),
    path('/mindmap_data', SDKMapView.as_view()),
    path('/new_memory', ADDMemoryView.as_view())

]
