# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
# import grpc_proto_pb2 as grpc__proto__pb2
from . import grpc_proto_pb2 as grpc__proto__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in grpc_proto_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class HWCPServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetProjectTreeData = channel.unary_unary(
                '/HWCPService/GetProjectTreeData',
                request_serializer=grpc__proto__pb2.GetProjectTreeDataParams.SerializeToString,
                response_deserializer=grpc__proto__pb2.ProjectTreeDataResponse.FromString,
                _registered_method=True)
        self.ConfigChipFunctions = channel.unary_unary(
                '/HWCPService/ConfigChipFunctions',
                request_serializer=grpc__proto__pb2.ConfigChipFunctionsParams.SerializeToString,
                response_deserializer=grpc__proto__pb2.ConfigChipFunctionsResponse.FromString,
                _registered_method=True)
        self.GetSubFunctionItems = channel.unary_unary(
                '/HWCPService/GetSubFunctionItems',
                request_serializer=grpc__proto__pb2.SubFunction.SerializeToString,
                response_deserializer=grpc__proto__pb2.ProjectSubFunctionItemsResponse.FromString,
                _registered_method=True)
        self.UpdateConfigItem = channel.unary_unary(
                '/HWCPService/UpdateConfigItem',
                request_serializer=grpc__proto__pb2.ConfigParams.SerializeToString,
                response_deserializer=grpc__proto__pb2.UpdateConfigItemResponse.FromString,
                _registered_method=True)
        self.GetMCUChipInfo = channel.unary_unary(
                '/HWCPService/GetMCUChipInfo',
                request_serializer=grpc__proto__pb2.GetMCUChipInfoParams.SerializeToString,
                response_deserializer=grpc__proto__pb2.GetMCUChipInfoParamsResponse.FromString,
                _registered_method=True)
        self.GetGPIOInfo = channel.unary_unary(
                '/HWCPService/GetGPIOInfo',
                request_serializer=grpc__proto__pb2.GetGPIOInfoParams.SerializeToString,
                response_deserializer=grpc__proto__pb2.GetGPIOInfoParamsResponse.FromString,
                _registered_method=True)
        self.UpdateGPIOConfigItem = channel.unary_unary(
                '/HWCPService/UpdateGPIOConfigItem',
                request_serializer=grpc__proto__pb2.UpdateGPIOConfigItemParams.SerializeToString,
                response_deserializer=grpc__proto__pb2.UpdateGPIOConfigItemParamsResponse.FromString,
                _registered_method=True)
        self.ExportCode = channel.unary_unary(
                '/HWCPService/ExportCode',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=grpc__proto__pb2.ExportCodeResponse.FromString,
                _registered_method=True)
        self.UpdateMemoryConfigItem = channel.unary_unary(
                '/HWCPService/UpdateMemoryConfigItem',
                request_serializer=grpc__proto__pb2.MemoryConfigParams.SerializeToString,
                response_deserializer=grpc__proto__pb2.UpdateMemoryConfigItemResponse.FromString,
                _registered_method=True)


class HWCPServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetProjectTreeData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ConfigChipFunctions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSubFunctionItems(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateConfigItem(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMCUChipInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetGPIOInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateGPIOConfigItem(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ExportCode(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateMemoryConfigItem(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_HWCPServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetProjectTreeData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProjectTreeData,
                    request_deserializer=grpc__proto__pb2.GetProjectTreeDataParams.FromString,
                    response_serializer=grpc__proto__pb2.ProjectTreeDataResponse.SerializeToString,
            ),
            'ConfigChipFunctions': grpc.unary_unary_rpc_method_handler(
                    servicer.ConfigChipFunctions,
                    request_deserializer=grpc__proto__pb2.ConfigChipFunctionsParams.FromString,
                    response_serializer=grpc__proto__pb2.ConfigChipFunctionsResponse.SerializeToString,
            ),
            'GetSubFunctionItems': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSubFunctionItems,
                    request_deserializer=grpc__proto__pb2.SubFunction.FromString,
                    response_serializer=grpc__proto__pb2.ProjectSubFunctionItemsResponse.SerializeToString,
            ),
            'UpdateConfigItem': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateConfigItem,
                    request_deserializer=grpc__proto__pb2.ConfigParams.FromString,
                    response_serializer=grpc__proto__pb2.UpdateConfigItemResponse.SerializeToString,
            ),
            'GetMCUChipInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMCUChipInfo,
                    request_deserializer=grpc__proto__pb2.GetMCUChipInfoParams.FromString,
                    response_serializer=grpc__proto__pb2.GetMCUChipInfoParamsResponse.SerializeToString,
            ),
            'GetGPIOInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetGPIOInfo,
                    request_deserializer=grpc__proto__pb2.GetGPIOInfoParams.FromString,
                    response_serializer=grpc__proto__pb2.GetGPIOInfoParamsResponse.SerializeToString,
            ),
            'UpdateGPIOConfigItem': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateGPIOConfigItem,
                    request_deserializer=grpc__proto__pb2.UpdateGPIOConfigItemParams.FromString,
                    response_serializer=grpc__proto__pb2.UpdateGPIOConfigItemParamsResponse.SerializeToString,
            ),
            'ExportCode': grpc.unary_unary_rpc_method_handler(
                    servicer.ExportCode,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=grpc__proto__pb2.ExportCodeResponse.SerializeToString,
            ),
            'UpdateMemoryConfigItem': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateMemoryConfigItem,
                    request_deserializer=grpc__proto__pb2.MemoryConfigParams.FromString,
                    response_serializer=grpc__proto__pb2.UpdateMemoryConfigItemResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'HWCPService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('HWCPService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class HWCPService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetProjectTreeData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/HWCPService/GetProjectTreeData',
            grpc__proto__pb2.GetProjectTreeDataParams.SerializeToString,
            grpc__proto__pb2.ProjectTreeDataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ConfigChipFunctions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/HWCPService/ConfigChipFunctions',
            grpc__proto__pb2.ConfigChipFunctionsParams.SerializeToString,
            grpc__proto__pb2.ConfigChipFunctionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSubFunctionItems(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/HWCPService/GetSubFunctionItems',
            grpc__proto__pb2.SubFunction.SerializeToString,
            grpc__proto__pb2.ProjectSubFunctionItemsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateConfigItem(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/HWCPService/UpdateConfigItem',
            grpc__proto__pb2.ConfigParams.SerializeToString,
            grpc__proto__pb2.UpdateConfigItemResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetMCUChipInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/HWCPService/GetMCUChipInfo',
            grpc__proto__pb2.GetMCUChipInfoParams.SerializeToString,
            grpc__proto__pb2.GetMCUChipInfoParamsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetGPIOInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/HWCPService/GetGPIOInfo',
            grpc__proto__pb2.GetGPIOInfoParams.SerializeToString,
            grpc__proto__pb2.GetGPIOInfoParamsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateGPIOConfigItem(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/HWCPService/UpdateGPIOConfigItem',
            grpc__proto__pb2.UpdateGPIOConfigItemParams.SerializeToString,
            grpc__proto__pb2.UpdateGPIOConfigItemParamsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ExportCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/HWCPService/ExportCode',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            grpc__proto__pb2.ExportCodeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateMemoryConfigItem(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/HWCPService/UpdateMemoryConfigItem',
            grpc__proto__pb2.MemoryConfigParams.SerializeToString,
            grpc__proto__pb2.UpdateMemoryConfigItemResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
