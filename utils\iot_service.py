import os
import logging
import traceback
import json

import requests

logger = logging.getLogger("iot")

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
CONF_PATH = os.path.join(BASE_DIR, "config", "config.json")

with open(CONF_PATH, "rb") as f:
    CONF = json.load(f)

IOT_CONF = CONF.get("iot")
BASE_URL = IOT_CONF.get("url")
TIMEOUT = IOT_CONF.get("timeout")


class IOTService:
    def __init__(self):
        pass

    @staticmethod
    def ground_lock_control(number, operator="open"):
        api = "iot/parking/parking_lock_control"

        url = BASE_URL + api

        data = {
            "device": number,
            "operator": operator
        }

        try:
            logger.info("地锁控制 url: %s data: %s", url, data)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("地锁控制 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"地锁控制出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 200:
                return False, response
        except Exception:
            logger.error("地锁控制出错！\n%s", traceback.format_exc())
            return False, {"msg": f"地锁控制出错。{traceback.format_exc()}"}

        return True, response

    @staticmethod
    def ground_lock_list():
        api = "iot/parking/parking_locks"

        url = BASE_URL + api

        try:
            logger.info("获取地锁列表 url: %s", url)
            response = requests.get(url, timeout=TIMEOUT)
            logger.info("获取地锁列表 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"获取地锁列表出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 200:
                return False, response
        except Exception:
            logger.error("获取地锁列表出错！\n%s", traceback.format_exc())
            return False, {"msg": f"获取地锁列表出错。{traceback.format_exc()}"}

        return True, response

    @staticmethod
    def ground_lock_info(number):
        api = "iot/parking/parking_lock"

        url = BASE_URL + api

        data = {
            "number": number
        }

        try:
            logger.info("获取地锁状态 url: %s data: %s", url, data)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("获取地锁状态 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"获取地锁状态出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 200:
                return False, response
        except Exception:
            logger.error("获取地锁状态出错！\n%s", traceback.format_exc())
            return False, {"msg": f"获取地锁状态出错。{traceback.format_exc()}"}

        return True, response

    @staticmethod
    def charging_pile_control(number, operator="open"):
        api = "iot/parking/charging_pile_control"

        url = BASE_URL + api

        data = {
            "device": number,
            "operator": operator
        }

        try:
            logger.info("充电桩控制 url: %s data: %s", url, data)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("充电桩控制 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"充电桩控制出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 200:
                return False, response
        except Exception:
            logger.error("充电桩控制出错！\n%s", traceback.format_exc())
            return False, {"msg": f"充电桩控制出错。{traceback.format_exc()}"}

        return True, response

    @staticmethod
    def charging_pile_list():
        api = "iot/parking/charging_piles"

        url = BASE_URL + api

        try:
            logger.info("获取充电桩列表 url: %s", url)
            response = requests.get(url, timeout=TIMEOUT)
            logger.info("获取充电桩列表 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"获取充电桩列表出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 200:
                return False, response
        except Exception:
            logger.error("获取充电桩列表出错！\n%s", traceback.format_exc())
            return False, {"msg": f"获取充电桩列表出错。{traceback.format_exc()}"}

        return True, response

    @staticmethod
    def charging_pile_info(number):
        api = "iot/parking/charging_pile"

        url = BASE_URL + api

        data = {
            "number": number
        }

        try:
            logger.info("获取充电桩状态 url: %s data: %s", url, data)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("获取充电桩状态 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"获取充电桩状态出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 200:
                return False, response
        except Exception:
            logger.error("获取充电桩状态出错！\n%s", traceback.format_exc())
            return False, {"msg": f"获取充电桩状态出错。{traceback.format_exc()}"}

        return True, response

    @staticmethod
    def car_washer_control(number, operator="open"):
        api = "iot/parking/car_wash_control"

        url = BASE_URL + api

        data = {
            "device": number,
            "operator": operator
        }

        try:
            logger.info("洗车机控制 url: %s data: %s", url, data)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("洗车机控制 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"洗车机控制出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 200:
                return False, response
        except Exception:
            logger.error("洗车机控制出错！\n%s", traceback.format_exc())
            return False, {"msg": f"洗车机控制出错。{traceback.format_exc()}"}

        return True, response

    @staticmethod
    def car_washer_list():
        api = "iot/parking/car_washes"

        url = BASE_URL + api

        try:
            logger.info("获取洗车机列表 url: %s", url)
            response = requests.get(url, timeout=TIMEOUT)
            logger.info("获取洗车机列表 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"获取洗车机列表出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 200:
                return False, response
        except Exception:
            logger.error("获取洗车机列表出错！\n%s", traceback.format_exc())
            return False, {"msg": f"获取洗车机列表出错。{traceback.format_exc()}"}

        return True, response

    @staticmethod
    def car_washer_info(number):
        api = "iot/parking/car_wash"

        url = BASE_URL + api

        data = {
            "number": number
        }

        try:
            logger.info("获取洗车机状态 url: %s data: %s", url, data)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("获取洗车机状态 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"获取洗车机状态出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 200:
                return False, response
        except Exception:
            logger.error("获取洗车机状态出错！\n%s", traceback.format_exc())
            return False, {"msg": f"获取洗车机状态出错。{traceback.format_exc()}"}

        return True, response

    @staticmethod
    def iot_device_repair(category, device, status):
        """
        :param category: 1：charging_pile(充电桩)、2：parking_lock(车位锁)、3：car_wash(洗车机)
        :param device: 设备编号
        :param status: 0 空闲中 1 使用中 2 故障中  3 维修中
        :return:
        """
        api = "iot/parking/device_event_sync"

        url = BASE_URL + api

        data = {
            "category": category,
            "device": device,
            "action": 1,
            "status": status
        }

        try:
            logger.info("设备报修 url: %s data: %s", url, data)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("设备报修 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"设备报修出错。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 200:
                return False, response
        except Exception:
            logger.error("设备报修出错！\n%s", traceback.format_exc())
            return False, {"msg": f"设备报修出错。{traceback.format_exc()}"}

        return True, response


if __name__ == '__main__':
    s = IOTService()
    print(s.charging_pile_control("HW-SCP-012"))
