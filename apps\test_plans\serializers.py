from rest_framework import serializers


class TestPlanListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    project_number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    creator_name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    plan_type_list = serializers.ListField(child=serializers.CharField(), min_length=0, required=False)
    software_version = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class SubTestPlanSerializer(serializers.Serializer):
    name = serializers.Char<PERSON><PERSON>(max_length=255)
    desc = serializers.Char<PERSON>ield(required=False, allow_blank=True, allow_null=True)
    test_cases = serializers.List<PERSON>ield(child=serializers.IntegerField(), min_length=0, default=[])
    machine_number = serializers.CharField(max_length=255)
    exec_status = serializers.BooleanField()
    abnormal_stop = serializers.BooleanField()
    tester_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    tester_email = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    def validate_test_cases(self, value):
        if len(value) == 0:
            raise serializers.ValidationError("子计划测试用例列表不能为空！")

        return value


class TestPlanSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    durability = serializers.BooleanField()
    product_version = serializers.CharField()
    software_version = serializers.CharField()
    plan_type = serializers.CharField(max_length=255)
    project_name = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255)
    project_id = serializers.CharField(max_length=255)
    sub_plans = serializers.ListField(child=SubTestPlanSerializer(), min_length=1)


class TestPlanUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    desc = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    durability = serializers.BooleanField(required=False)
    plan_type = serializers.CharField(max_length=255)
    test_cases = serializers.ListField(child=serializers.IntegerField(), min_length=0)
    software_version = serializers.CharField()
    product_version = serializers.CharField()
    sub_plans = serializers.ListField(child=SubTestPlanSerializer(), min_length=1)


class TestSubPlanStatusUpdateSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    sub_plan_id = serializers.IntegerField()
    status = serializers.BooleanField()
