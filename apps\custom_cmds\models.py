from django.db import models


class CustomCMDModel(models.Model):
    cmd_name = models.Char<PERSON>ield(max_length=255)
    cmd_code = models.CharField(max_length=255, unique=True)
    desc = models.TextField(null=True)
    params = models.JSONField(null=True)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        managed = False
        app_label = 'custom_cmds'
        db_table = 'custom_cmds'

    def to_dict(self):
        return {
            'cmd_name': self.cmd_name,
            'cmd_code': self.cmd_code,
            'desc': self.desc,
            'params': self.params,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S"),
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S"),
        }
