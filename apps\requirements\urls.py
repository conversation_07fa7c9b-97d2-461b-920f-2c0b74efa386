from django.urls import path

from .views import (
    RequirementsView, RequirementDetailView, RequirementRelateTestCasesView, RequirementRelatedTestCasesView,
    RemoteRequirementsByTestCaseView, RemoteRequirementsByTestCaseView2, RequirementRemoveTestCasesView,
)

urlpatterns = [
    path("", RequirementsView.as_view()),

    path("/<int:pk>", RequirementDetailView.as_view()),

    path("/relate_test_cases", RequirementRelateTestCasesView.as_view()),

    path("/remove_test_cases", RequirementRemoveTestCasesView.as_view()),

    path("/test_cases", RequirementRelatedTestCasesView.as_view()),

    path("/get_remote_reqs_by_test_cases", RemoteRequirementsByTestCaseView.as_view()),

    path("/get_remote_reqs_by_test_cases2", RemoteRequirementsByTestCaseView2.as_view()),
]
