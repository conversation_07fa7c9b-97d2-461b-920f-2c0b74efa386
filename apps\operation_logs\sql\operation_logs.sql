CREATE TABLE IF NOT EXISTS public.operation_logs(
    id serial primary key, --id
    user_email varchar(255),
    user_name varchar(255),
    "action" varchar(255),
    method varchar(10),
    path varchar(255),
    "timestamp" timestamp,
    ip_address varchar(255),
    status_code int,
    response_time float,
    is_success boolean,
    "exception" text,
    user_agent varchar(255),
    request_data text,
    response_data text
);
ALTER TABLE public.operation_logs OWNER TO atpms;