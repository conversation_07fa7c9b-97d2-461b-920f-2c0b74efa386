import logging
import traceback
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db import transaction
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.cache import cache
from django.core.serializers import serialize
import json
import os
import re
import shutil
import time
from pathlib import Path

logger = logging.getLogger("code_management")
from apps.code_management.grpc.host_machine_grpc import host_machine_client
from apps.code_management.grpc.git_grpc import git_client
from apps.code_management.models.project_models import ProjectInfoSerializer, CodeProjectInfo, CodeBranchInfo, BranchInfoSerializer
from apps.code_management.models.config_models import (
    NodeTree,
    NodeTreeSerializer,
    UserDefinedConfig,
    UserDefinedOption,
    Level1Group,
    Level2MainParam,
    Level3SubParam,
    MainParamEnum,
    MainParamUnit8,
    SubParamEnumRelation,
    ParamOriginLocation,
    BranchDefinedValue,
    MainSpecialEnum
)
from apps.code_management.models.luminance_models import LuminancecCure
from apps.code_management.models.memory_models import (
    ChipDataflash,
    MemoryInfo,
    MemoryTableInfo,
    ParamInfoTable,
    MemoryConfigInfo,
    BranchMemoryDefind,
    BranchMemoryTableDefind
)
from users.models import UserFSInfo, User
from utils.fs_service import FSService
from utils.fs_app import fs_app


# local_path = "/home/<USER>/hwcp"
local_path = "F:\project\HWCP"
config_path = "/home/<USER>/hwcp/gRPC_Server/HwcpTest"
config_url = "http://*********/python-team/HwcpTest.git"
config_branch = "main"
sdk_git_path = "http://*********/"



def get_dataflash(node_name, node_level, project_code, project_name,  project_gitlab, project_branch):


    dataflash = {}
    # 查询芯片
    try:

        code_branch = CodeBranchInfo.objects.get(
            project_code=project_code,
            project_name=project_name,
            project_gitlab=project_gitlab,
            project_branch=project_branch
        )
        chip = code_branch.chip  # 直接取对象的 chip 字段（非列表）
    except CodeBranchInfo.DoesNotExist:

        chip = None
    except CodeBranchInfo.MultipleObjectsReturned:

        chip = None

    if chip:
        datadlash = ChipDataflash.objects.using("hwcp").filter(
            var_name=node_name,
            node_level=node_level,
            chip=chip
        )
        logger.info(f"datadlash: {datadlash}")
        # 初始化结果结构

        if not datadlash.exists():
            logger.warning("未查询到符合条件的记录")
            return dataflash

        # 提取分组名称
        group_name = datadlash.first().group
        dataflash["group"] = group_name

        # 构建list字典，key为param_key，value为字段映射
        dataflash["list"] = {}
        for item in datadlash:
            # 字段映射
            dataflash["list"][item.param_key] = {
                "name": item.name,
                "desc": item.desc,
                "type": item.type,
                "default": item.default
            }

        logger.info(f"格式化后的结果: {dataflash}")
        return dataflash
    else:
        logger.info("获取芯片失败")

    return dataflash




class MeoryInfoView(APIView):


    def reorganization_memory(self, memory_info, space_info):

        # 初始化结果结构
        memory = []
        config_memory = {}
        try:
            # 获取默认值
            memory_info_result = BranchMemoryDefind.objects.using("hwcp").filter(
                project_code=space_info['project_code'],
                project_name=space_info['project_name'],
                project_gitlab=space_info['project_gitlab'],
                project_branch=space_info['project_branch'],
                var_name=space_info['node_name'],
                node_level=space_info['node_level']
            ).values_list('memory_info', flat=True)
            logger.info(f"memory_info_result：{memory_info_result}")

            for item in memory_info:

                # 构建分组唯一标识，用 (group_key, group_name) 确保分组正确
                group_key_name = (item.group_key, item.group_name)
                # 如果分组未初始化，则创建分组基础结构
                if group_key_name not in config_memory:
                    config_memory[group_key_name] = {
                        "group": item.group_name,  # 分组标题用 group_name
                        "list": {}
                    }

                # 构造字段数据，映射到前端需要的字段结构
                field_data = {
                    "name": item.param_name,
                    "unit": "",  # 表中无直接对应字段，若有可从其他字段取
                    # desc 处理：若数据库存的是字符串，可转数组；若本身是数组格式字符串需 eval（注意安全）
                    "desc": item.desc.split('\n') if item.desc else [],
                    "type": item.type,
                    "default": item.default_value,
                    "min": item.min,
                    "max": item.max
                }

                # 读分支存储的值
                if memory_info_result.exists():
                    for data in memory_info_result:
                        logger.info(f"data:{data}")
                        for key, value in data.items():
                            if key == item.param_key:
                                field_data['default'] = value
                            else:
                                continue
                else:
                    logger.info(f"没有找到memory_info的值")

                # 给分组的 list 添加字段，field_key 用 param_key
                config_memory[group_key_name]["list"][item.param_key] = field_data


                # 将分组缓存的数据转成前端需要的数组格式，放到 config.variables 里
            memory = list(config_memory.values())

        except Exception as e:
            logger.info("重组文本数据失败：", e)
            return None

        return memory



    def reorganization_table(self, table_info, table_tile):

        table = {}
        tableColumns = []

        try:
            for item in table_tile:
                data = {}
                data['label'] = item['param_name']
                data['prop'] = item['param_key']
                data['type'] = 'text'
                if item['param_key'] == "data_length":
                    data['type'] = 'text_disable'

                tableColumns.append(data)

            logger.info(f"tableColumns: {tableColumns}")

            table['tableColumns'] = tableColumns
            table['tableData'] = table_info

        except Exception as e:
            logger.info("重组表格数据失败：", e)
            return None

        return table





    def memory_info(self, space_info):
        """
            获取文本信息
        """
        memory_info = MemoryInfo.objects.using("hwcp").filter(
            var_name=space_info['node_name'],
            node_level=space_info['node_level']
        )
        logger.info(f"memory_info: {memory_info}")

        if not memory_info.exists():
            logger.info("未查询到符合条件的记录")
            raise ValueError("未查询到符合条件的记录")

        memory = self.reorganization_memory(memory_info, space_info)
        logger.info(f"memory: {memory}")

        return memory



    def table_info(self, space_info):
        """
            获取表格数据
        """

        table_info_set = MemoryTableInfo.objects.using("hwcp").filter(
            var_name=space_info['node_name'],
            node_level=space_info['node_level']
        ).order_by("index_row").values(
            "index_row",
            "address_index",
            "address_info",
            "data_length",
            "default_value",
            "read_from_flash",
            "desc",
            "index_row"
        )
        if not table_info_set:
            return None

        table_info = list(table_info_set)
        logger.info(f"table_info: {table_info}")

        memory_table = BranchMemoryTableDefind.objects.using("hwcp").filter(
            project_code=space_info['project_code'],
            project_name=space_info['project_name'],
            project_gitlab=space_info['project_gitlab'],
            project_branch=space_info['project_branch'],
            var_name=space_info['node_name'],
            node_level=space_info['node_level'],
        ).values('memory_table', 'row_index')
        logger.info(f"memory_table: {memory_table}")

        # 读取分支的数据
        if memory_table.exists():
            for item in table_info:
                for table in memory_table:
                    if str(item['index_row']) == str(table['row_index']):
                        logger.info(f"table['row_index']:{table['row_index']}, item['index_row']:{item['index_row']}")
                        for key, value in table['memory_table'].items():
                            logger.info(f"key:{key}, value:{value}")
                            if key in item.keys():
                                item[key] = value
                            else:
                                continue

        logger.info(f"table_info: {table_info}")

        table_tile_set = ParamInfoTable.objects.using("hwcp").filter(
            var_name=space_info['node_name'],
            node_level=space_info['node_level']
        ).order_by("index_col").values(
            "param_key",
            "param_name"
        )
        table_tile = list(table_tile_set)
        logger.info(f"table_tile: {table_tile}")

        table = self.reorganization_table(table_info, table_tile)

        return table






    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("receive info: %s", params)

            space_info = {}
            space_info['node_level'] = params.get('node_level')
            space_info['node_name'] = params.get('nodeName')
            space_info['project_code'] = params.get('project_code')
            space_info['project_name'] = params.get('project_name')
            space_info['project_gitlab'] = params.get('project_gitlab')
            space_info['project_branch'] = params.get('project_branch')
            space_info['workspacePath'] = params.get('workspacePath')
            space_info['branchStatus'] = params.get('branchStatus')
            space_info['branchStatus'] = params.get('branchStatus')


            logger.info(f"space_info:{space_info}")

            memory = self.memory_info(space_info)

            table = self.table_info(space_info)
            logger.info(f"memory:{memory}, table:{table}")

            if memory and table:
                return Response({
                    "status": 1,
                    "message": "配置获取成功",
                    "memory_info": memory,
                    "table_info": table
                })

            else:
                return Response(
                    {
                        "config_status": 0,
                        "message": "配置获取失败"}
                )

        except Exception as e:
            logger.info(f"配置获取失败: {str(e)}")
            return Response({"status": 0, "message": str(e)})





class MemoryChangeView(APIView):

    def defind_memory_info(self, info):
        try:
            # 查询历史数据
            memory_defind_info = BranchMemoryDefind.objects.using("hwcp").get(
                project_code=info["project_code"],
                project_name=info["project_name"],
                project_gitlab=info["project_gitlab"],
                project_branch=info["project_branch"],
                node_level=info["node_level"],
                var_name=info["node_name"]
            )
            # 处理JSONField（字典类型）
            if not memory_defind_info.memory_info:
                memory_defind_info.memory_info = {}  # 确保初始为字典

            # 更新或添加键值对
            if info["config_label"] in memory_defind_info.memory_info:
                del memory_defind_info.memory_info[info["config_label"]]
            memory_defind_info.memory_info[info["config_label"]] = info["config_value"]
            memory_defind_info.save(using="hwcp")

        except BranchMemoryDefind.DoesNotExist:

            branch_memory = BranchMemoryDefind.objects.using("hwcp").create(
                project_code=info["project_code"],
                project_name=info["project_name"],
                project_gitlab=info["project_gitlab"],
                project_branch=info["project_branch"],
                node_level=info["node_level"],
                var_name=info["node_name"]
            )


            branch_memory.memory_info = {
                info["config_label"]: info["config_value"]
            }

            branch_memory.save(using="hwcp")

    def defind_table(self, info):
        try:
            table = BranchMemoryTableDefind.objects.using("hwcp").get(
                project_code=info["project_code"],
                project_name=info["project_name"],
                project_gitlab=info["project_gitlab"],
                project_branch=info["project_branch"],
                node_level=info["node_level"],
                var_name=info["node_name"],
                row_index=info['row']
            )
            if not table.memory_table:
                table.memory_table = {}  # 确保初始为字典

            # 更新或添加键值对
            if info["config_label"] in table.memory_table:
                del table.memory_table[info["config_label"]]
            table.memory_table[info["config_label"]] = info["config_value"]
            table.save(using="hwcp")

        except BranchMemoryTableDefind.DoesNotExist:
            table = BranchMemoryTableDefind.objects.using("hwcp").create(
                project_code=info["project_code"],
                project_name=info["project_name"],
                project_gitlab=info["project_gitlab"],
                project_branch=info["project_branch"],
                node_level=info["node_level"],
                var_name=info["node_name"],
                row_index=info['row']
            )

            table.memory_table = {
                info["config_label"]: info["config_value"]
            }

            table.save(using="hwcp")


    def updata_memory_info(self, belong, info):


            if belong == "memory_info":
               self.defind_memory_info(info)


            elif belong == "memory_table":
              self.defind_table(info)






    def create_mermory_info(self, belong, info):

        data = {
            # 公共字段：直接映射info中的对应值
            "project_code": info["project_code"],
            "project_name": info["project_name"],
            "project_gitlab": info["project_gitlab"],
            "project_branch": info["project_branch"],
            "node_level": info["node_level"],  # 对应info中的node_level
            "var_name": info["var_name"]  # 模型中单独的var_name字段，需外部提供
        }

        if belong == "memory_info":
            data["memory_info"] = {
                info["config_label"]: info["config_value"]
            }
        elif belong == "memory_table":
            data["memory_table"] = {
                info["config_label"]: info["config_value"]
            }

        new_record = BranchMemoryDefind(**data)
        # 保存到数据库
        new_record.save()



    def send_memory_updata_grpc(self, belong, info):

        try:
            # 查node_path
            parent_obj = NodeTree.objects.using("hwcp").filter(
                name=info["node_name"],
                level=info["node_level"]

                ).first()
            logger.info(f"parent_obj:{parent_obj}")
            if parent_obj:
                parent_id = parent_obj.parent_id
                sibling_nodes = NodeTree.objects.using("hwcp").filter(
                    parent_id=parent_id
                ).only('node_path')
                if sibling_nodes.exists():
                    config_para_path = sibling_nodes.first().node_path
                logger.info(f"config_para_path:{config_para_path}")

            # 查xml_path
            chip_queryset = CodeBranchInfo.objects.filter(
                branch_space=info['workspace_path']
            ).only('chip')
            if chip_queryset.exists():  # 检查是否有匹配记录
                # 取第一个实例的chip字段值
                chip = chip_queryset.first().chip
                logger.info(f"获取到chip值: {chip}")
            else:
                logger.info("未查询到对应的chip记录")
                return False
            logger.info(f"chip:{chip}")

            xml_path_result = MemoryConfigInfo.objects.using("hwcp").filter(
                chip=chip,
                var_name=info['node_name'],
                node_level=info['node_level']
            ).only('xml_path')
            if xml_path_result.exists():
                xml_path = xml_path_result.first().xml_path
                logger.info(f"获取到xml_path值: {xml_path}")
            else:
                logger.info("未查询到对应的xml_path记录")
                return False
            logger.info(f"xml_path:{xml_path}")

            sdk_path = os.path.dirname(info['workspace_path']) + "/" + "hiwaysdk_2.0" + '/' + "Application"
            xml_path = info['workspace_path'] + '/' + "Application" + "/" + xml_path
            logger.info(f"sdk_path:{sdk_path}, xml_path:{xml_path}")

            data = {
                # 已明确赋值的字段
                'sdk_path': sdk_path,
                'project_path': info['workspace_path'],
                'branch_status': info['branch_status'],

                # 需要根据实际场景填充的字段（代码中仅使用未显式赋值）
                'config_para_path': config_para_path,  # 修改参数所在路径
                'config_module_name': info["node_name"],  # 修改参数模块名（如Config_Memory）
                'config_para_type': "arrays_struct",  # 修改参数类型（如arrays_struct）
                'config_para_name': "gMemoryConfigInfo",  # 修改参数名（如gMemoryConfigInfo）
                'config_para_value': info["config_value"],  # 修改参数的值
                'module_name': "Memory",  # 模块名（如Memory）
                'xml_path': xml_path  # xml文件路径
            }
            logger.info(f'belong:{belong}')
            if belong == "memory_info":
                # 将数转成十进制
                if info["config_label"] == "size":
                    temp = data["config_para_value"]
                    temp1 = int(temp, 16)
                    data['config_para_value'] = str(temp1)

                data['row_index'] = None  # 对应行索引
                data['column_index'] = None  # 对应列索引
                data['struct_member_type'] = ''  # 参数类型

            elif belong == "memory_table":

                data['row_index'] = int(info['row']) - 1  # 对应行索引
                data['column_index'] = int(info['col']) - 1  # 对应列索引

                data['config_para_value'] = str(info["config_value"])
                logger.info(f'info["config_label"]:{info["config_label"]}')

                param_type = ParamInfoTable.objects.using("hwcp").filter(
                    param_key=info["config_label"]
                ).only("param_type")

                logger.info(f"param_type:{param_type}")

                if param_type.exists():
                    param_type = param_type.first().param_type
                    logger.info(f"获取到param_type值: {xml_path}")
                    data['struct_member_type'] = param_type  # 参数类型

                else:
                    logger.info(f"查询不到当前修改key的类型")
                    return False

            logger.info(f"data: {data}")

            response = host_machine_client.modify_memory_parmas(data)
            logger.info(f"response: {response}")

            if response.config_status == 1:
                return True

            return False

        except Exception as e:
            # 捕获异常并触发回滚
            logger.error(f"gRPC 调用失败, 触发回滚: {str(e)}")
            return False




    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("receive info: %s", params)


            config_value = params.get('config_value')
            workspace_path = params.get('workspace_path')
            config_label = params.get("config_label")
            project_code = params.get('project_code')
            project_name = params.get('project_name')
            project_gitlab = params.get('project_gitlab')
            project_branch = params.get('project_branch')
            node_name = params.get('nodeName')
            node_level = params.get('node_level')
            branch_status = params.get('branch_status')

            info = {
                "project_code": project_code,
                "project_name": project_name,
                "project_gitlab": project_gitlab,
                "project_branch": project_branch,
                "node_name": node_name,
                "node_level": node_level,
                "config_label": config_label,
                "config_value": config_value,
                "workspace_path": workspace_path,
                "branch_status": branch_status
            }



            # 插入数据
            belong_query = ParamOriginLocation.objects.using("hwcp").filter(
                param_key=info["config_label"]  # 修正为 param_key（根据您的模型定义）
            ).values_list('belong', flat=True).first()

            if belong_query:
                belong = belong_query

                logger.info(f" 修改的字段属于 {belong} 模块")

                if belong == "memory_table":
                    info['row'] = params.get('row')  # 对应行索引
                    info['col'] = params.get('col')  # 对应列索引

                # 参数打印
                logger.info(f"info:{info}")

                # 使用数据库事务包裹操作
                with transaction.atomic(using="hwcp"):

                   # 插入数据到数据库
                    self.updata_memory_info(belong, info)

                    # 调用grpc修改
                    result = self.send_memory_updata_grpc(belong, info)

                    if not result:
                        raise transaction.TransactionManagementError("grpc配置失败，触发回滚")
                logger.info("配置成功")
                return Response({"config_status": 1, "message": "配置成功"})

            else:
                logger.warning(f"未找到参数 {info['config_label']} 的归属信息")
                raise ValueError("未找到参数归属信息")

        except Exception as e:
                logger.info(f"配置获取失败: {str(e)}")
                return Response({"status": 0, "message": str(e)})



class ADDMemoryView(APIView):

    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("receive info: %s", params)


            config_value = params.get('config_value')
            workspace_path = params.get('workspace_path')
            config_label = params.get("config_label")
            project_code = params.get('project_code')
            project_name = params.get('project_name')
            project_gitlab = params.get('project_gitlab')
            project_branch = params.get('project_branch')
            node_name = params.get('nodeName')
            node_level = params.get('node_level')
            branch_status = params.get('branch_status')

            try:

                code_branch = CodeBranchInfo.objects.get(
                    project_code=project_code,
                    project_name=project_name,
                    project_gitlab=project_gitlab,
                    project_branch=project_branch
                )
                chip = code_branch.chip  # 直接取对象的 chip 字段（非列表）
            except CodeBranchInfo.DoesNotExist:
                chip = None
            except CodeBranchInfo.MultipleObjectsReturned:
                chip = None

            if chip:
                datadlash = ChipDataflash.objects.using("hwcp").filter(
                    var_name=node_name,
                    node_level=node_level,
                    chip=chip
                )
                logger.info(f"datadlash: {datadlash}")
                min_size = ""
                for item in datadlash:

                    if item.name == "最小可编程单元":
                        min_size = item.default

                if min_size:
                    logger.info(f"min_size: {min_size}")
                    return Response({"status": 1, "message": '获取成功', "min_size": min_size})
                else:
                    return Response({"status": 0, "message": '最小单元地址获取失败'})




        except Exception as e:
                logger.info(f"新增配置数据获取失败: {str(e)}")
                return Response({"status": 0, "message": str(e)})





