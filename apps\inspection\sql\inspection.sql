CREATE TABLE IF NOT EXISTS public.inspection_items(
    id serial primary key,

    parent_id INT DEFAULT 0,
    path VARCHAR(255) NOT NULL,
    "level" INT DEFAULT 1,

    name VA<PERSON>HAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL UNIQUE,
    "desc" TEXT DEFAULT '',
    type VA<PERSON><PERSON><PERSON>(255) DEFAULT 'item',
    category VARCHAR(255) DEFAULT '',
    tags TEXT DEFAULT '',

    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.inspection_items OWNER TO atpms;