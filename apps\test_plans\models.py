import datetime
import json

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone, sql_insert_many
from django.db import transaction

from test_cases.models import TestCaseModel
from .signals import plan_create
from functions.models import FunctionModel


class TestPlanModel:
    def __init__(self):
        self.table_name = "public.test_plans"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            params = {
                "name": kwargs.get("name"),
                "desc": kwargs.get("desc"),
                "durability": kwargs.get("durability"),
                "plan_type": kwargs.get("plan_type"),
                "sub_plans": kwargs.get("sub_plans"),
                "software_version": kwargs.get("software_version"),
                "product_version": kwargs.get("product_version"),
                "creator_name": kwargs.get("creator_name"),
                "creator_employee_number": kwargs.get("creator_employee_number"),
                "creator_email": kwargs.get("creator_email"),
                "project_name": kwargs.get("project_name"),
                "project_number": kwargs.get("project_number"),
                "project_id": kwargs.get("project_id"),
                "create_time": now,
                "update_time": now,
            }

            for (index, sp) in enumerate(kwargs["sub_plans"]):
                sp["id"] = index
                sp["test_cases"] = [TestCaseModel().retrieve(i) for i in sp["test_cases"]]

            params["sub_plans"] = json.dumps(params["sub_plans"])

            sql = """
                INSERT INTO {table_name} ("name", "desc", durability, plan_type, sub_plans, creator_name, creator_employee_number, 
                creator_email, project_name, project_number, project_id, software_version, product_version, create_time, update_time) 
                VALUES (%(name)s, %(desc)s, %(durability)s, %(plan_type)s, %(sub_plans)s,
                    %(creator_name)s, %(creator_employee_number)s, %(creator_email)s,
                    %(project_name)s, %(project_number)s, %(project_id)s, %(software_version)s, %(product_version)s, %(create_time)s, 
                    %(update_time)s)
                ;
            """.format(table_name=self.table_name)

            sql_execute(sql, params)

            plan_create.send(
                self.__class__,
                project_number=kwargs.get("project_number"),
                version_name=kwargs.get("software_version")
            )

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        name_re = kwargs.get("name_re")
        if name_re is not None and name_re != '':
            sql_where_list.append("name ilike %(name_re)s")
            params["name_re"] = f"%{name_re}%"

        creator_name_re = kwargs.get("creator_name_re")
        if creator_name_re is not None and creator_name_re != '':
            sql_where_list.append("creator_name ilike %(creator_name_re)s")
            params["creator_name_re"] = f"%{creator_name_re}%"

        creator_employee_number = kwargs.get("creator_employee_number")
        if creator_employee_number is not None and creator_employee_number != '':
            sql_where_list.append("creator_employee_number = %(creator_employee_number)s")
            params["creator_employee_number"] = creator_employee_number

        software_version = kwargs.get("software_version")
        if software_version is not None and software_version != '':
            sql_where_list.append("software_version = %(software_version)s")
            params["software_version"] = software_version

        status = kwargs.get("status")
        if status is not None and status != '':
            sql_where_list.append("status = %(status)s")
            params["status"] = status

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        plan_type_list = kwargs.get("plan_type_list", [])
        if len(plan_type_list) > 0:
            sql_where_list.append("plan_type in %(plan_type_list)s")
            params["plan_type_list"] = tuple(plan_type_list)

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY update_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, "name", "desc", durability, plan_type, project_name, project_number, project_id, creator_name,
                creator_employee_number, creator_email, create_time, software_version, product_version, update_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        for i in content["results"]:
            i["product_version"] = json.loads(i["product_version"])

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, "name", "desc", durability, plan_type, sub_plans, project_name, project_number, project_id, creator_name,
                creator_employee_number, creator_email, software_version, product_version, create_time, update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["product_version"] = json.loads(result["product_version"])
            result["sub_plans"] = json.loads(result["sub_plans"])
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def retrieve2(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, "name", "desc", durability, plan_type, sub_plans, project_name, project_number, project_id, creator_name,
                creator_employee_number, creator_email, software_version, product_version, create_time, update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        data = FunctionModel().list().get("results")
        module_map = {}
        for i in data:
            module_map[i["number"]] = i["name"]
            if i.get("children"):
                for j in i["children"]:
                    module_map["{}-{}".format(i["number"], j["number"])] = j["name"]

                    if j.get("children"):
                        for k in j["children"]:
                            module_map["{}-{}-{}".format(i["number"], j["number"], k["number"])] = k["name"]
        status_map = {
            "PENDING": "待评审",
            "REVIEWING": "评审中",
            "APPROVED": "评审通过",
            "REJECTED": "评审不通过",
            "DEPRECATED": "废弃",
        }

        if result:
            result["product_version"] = json.loads(result["product_version"])
            result["sub_plans"] = json.loads(result["sub_plans"])
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

            for sp in result["sub_plans"]:
                for tc in sp.get("test_cases", []):
                    tc["module_name"] = module_map.get(tc["module"])
                    tc["module_2level_name"] = None
                    tc["module_3level_name"] = None
                    if tc["module_2level"]:
                        tc["module_2level_name"] = module_map.get("{}-{}".format(tc["module"], tc["module_2level"]))
                        if tc["module_3level"]:
                            tc["module_3level_name"] = module_map.get(
                                "{}-{}-{}".format(tc["module"], tc["module_2level"], tc["module_3level"]))
                    tc["module_str"] = "/".join(
                        [i for i in [tc["module_name"], tc["module_2level_name"], tc["module_3level_name"]] if i])
                    tc["version_name"] = f"V{tc['version']}.0"
                    tc["status_name"] = status_map.get(tc["status"])

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
    """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()
            params = {
                "id": kwargs.get("pk"),
                "update_time": now,
            }

            sql_set_list = []

            name = kwargs.get("name")
            if name is not None:
                sql_set_list.append("name = %(name)s")
                params["name"] = name

            desc = kwargs.get("desc")
            if desc is not None:
                sql_set_list.append("\"desc\" = %(desc)s")
                params["desc"] = desc

            durability = kwargs.get("durability")
            if durability is not None:
                sql_set_list.append("durability = %(durability)s")
                params["durability"] = durability

            plan_type = kwargs.get("plan_type")
            if plan_type is not None:
                sql_set_list.append("plan_type = %(plan_type)s")
                params["plan_type"] = plan_type

            software_version = kwargs.get("software_version")
            if software_version is not None:
                sql_set_list.append("software_version = %(software_version)s")
                params["software_version"] = software_version

            product_version = kwargs.get("product_version")
            if product_version is not None:
                sql_set_list.append("product_version = %(product_version)s")
                params["product_version"] = product_version

            sub_plans = kwargs.get("sub_plans")
            if sub_plans is not None:
                for (index, sp) in enumerate(sub_plans):
                    sp["id"] = index
                    sp["test_cases"] = [TestCaseModel().retrieve(i) for i in sp["test_cases"]]
                sql_set_list.append("sub_plans = %(sub_plans)s")
                params["sub_plans"] = json.dumps(sub_plans)

            if not sql_set_list:
                return

            sql_set = ", ".join(sql_set_list)

            sql = """
                UPDATE {table_name}     
                    SET 
                    {sql_set},
                    update_time = %(update_time)s
                    WHERE id = %(id)s;
            """.format(
                table_name=self.table_name,
                sql_set=sql_set,
            )
            sql_execute(sql, params)

    def update_sub_plan_status(self, plan_id, sub_plan_id, status):
        plan = self.retrieve(plan_id)

        sub_plans = plan.get("sub_plans")
        sub_plans[sub_plan_id]["exec_status"] = status

        sql = """
            UPDATE {table_name}     
                SET 
                sub_plans = %(sub_plans)s,
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
        )
        now = datetime.datetime.now()
        params = {
            "id": plan_id,
            "sub_plans": json.dumps(sub_plans),
            "update_time": now,
        }

        sql_execute(sql, params)
