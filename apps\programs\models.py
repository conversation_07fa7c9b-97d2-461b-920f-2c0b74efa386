import datetime
import json

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone, sql_insert_many
from django.db import transaction

from test_cases.models import TestCaseModel


class ProgramModel:
    def __init__(self):
        self.table_name = "public.programs"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            params = {
                "name": kwargs.get("name"),
                "number": kwargs.get("number"),
                "desc": kwargs.get("desc"),
                "type": kwargs.get("type"),
                "icon": kwargs.get("icon_path"),
                "extra_args": json.dumps(kwargs.get("extra_args")),
                "create_time": now,
                "update_time": now,
            }

            sql = """
                INSERT INTO {table_name} ("name", "number", "desc", "type", icon, extra_args, create_time, update_time) 
                VALUES (
                    %(name)s, %(number)s, %(desc)s, %(type)s, %(icon)s, %(extra_args)s, %(create_time)s, 
                    %(update_time)s
                    )
                ;
            """.format(table_name=self.table_name)

            sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        name_re = kwargs.get("name_re")
        if name_re is not None and name_re != '':
            sql_where_list.append("name ~* %(name_re)s")
            params["name_re"] = name_re

        type = kwargs.get("type")
        if type is not None and type != '':
            sql_where_list.append("type = %(type)s")
            params["type"] = type

        number_re = kwargs.get("number_re")
        if number_re is not None and number_re != '':
            sql_where_list.append("number ~* %(number_re)s")
            params["number_re"] = number_re

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY update_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, "name", "number", "type", icon, extra_args, "desc", create_time, update_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        for result in content["results"]:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, "name", "number", "desc", "type", icon, extra_args, create_time, update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
    """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()
            params = {
                "id": kwargs.get("pk"),
                "update_time": now,
            }

            sql_set_list = []

            name = kwargs.get("name")
            if name is not None:
                sql_set_list.append("name = %(name)s")
                params["name"] = name

            number = kwargs.get("number")
            if number is not None:
                sql_set_list.append("number = %(number)s")
                params["number"] = number

            type = kwargs.get("type")
            if type is not None:
                sql_set_list.append("type = %(type)s")
                params["type"] = type

            icon = kwargs.get("icon_path")
            if icon is not None:
                sql_set_list.append("icon = %(icon)s")
                params["icon"] = icon

            extra_args = kwargs.get("extra_args")
            if extra_args is not None:
                sql_set_list.append("extra_args = %(extra_args)s")
                params["extra_args"] = json.dumps(extra_args)

            desc = kwargs.get("desc")
            if desc is not None:
                sql_set_list.append("\"desc\" = %(desc)s")
                params["desc"] = desc

            if not sql_set_list:
                return

            sql_set = ", ".join(sql_set_list)

            sql = """
                UPDATE {table_name}     
                    SET 
                    {sql_set},
                    update_time = %(update_time)s
                    WHERE id = %(id)s;
            """.format(
                table_name=self.table_name,
                sql_set=sql_set,
            )
            sql_execute(sql, params)


class ProgramVersionModel:
    def __init__(self):
        self.table_name = "public.program_versions"

    def create(self, **kwargs):
        now = datetime.datetime.now()

        params = {
            "program_id": kwargs.get("program_id"),
            "version": kwargs.get("version"),
            "path": kwargs.get("path"),
            "linux_path": kwargs.get("linux_path"),
            "desc": kwargs.get("desc"),
            "create_time": now,
            "update_time": now,
        }

        sql = """
            INSERT INTO {table_name} ("program_id", "version", path, linux_path, "desc", create_time, update_time) 
            VALUES (%(program_id)s, %(version)s, %(path)s, %(linux_path)s, %(desc)s, %(create_time)s, %(update_time)s)
            ;
        """.format(table_name=self.table_name)

        sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        program_name_re = kwargs.get("program_name_re")
        if program_name_re is not None and program_name_re != '':
            sql_where_list.append("p.name ~* %(program_name_re)s")
            params["program_name_re"] = program_name_re

        version_re = kwargs.get("version_re")
        if version_re is not None and version_re != '':
            sql_where_list.append("pv.version ~* %(version_re)s")
            params["version_re"] = version_re

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY pv.create_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} as pv left join public.programs as p
             on pv.program_id = p.id
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT pv.id, p.name as program_name, pv."version", pv.path, pv.linux_path, pv."desc", pv.is_disabled, 
            pv.create_time, pv.update_time
             FROM {table_name} as pv left join public.programs as p
             on pv.program_id = p.id
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        for i in content["results"]:
            i["create_time"] = i["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            i["update_time"] = i["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT pv.id, pv.program_id, p.name as program_name, pv."version", pv.path, pv.linux_path, pv."desc", 
            pv.is_disabled, pv.create_time, pv.update_time
        FROM {table_name} as pv left join public.programs as p
             on pv.program_id = p.id
        WHERE pv.id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
    """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        now = datetime.datetime.now()
        params = {
            "id": kwargs.get("pk"),
            "update_time": now,
        }

        sql_set_list = []

        program_id = kwargs.get("program_id")
        if program_id is not None:
            sql_set_list.append("program_id = %(program_id)s")
            params["program_id"] = program_id

        version = kwargs.get("version")
        if version is not None:
            sql_set_list.append("\"version\" = %(version)s")
            params["version"] = version

        path = kwargs.get("path")
        if path is not None:
            sql_set_list.append("path = %(path)s")
            params["path"] = path

        linux_path = kwargs.get("linux_path")
        if linux_path is not None:
            sql_set_list.append("linux_path = %(linux_path)s")
            params["linux_path"] = linux_path

        desc = kwargs.get("desc")
        if desc is not None:
            sql_set_list.append("\"desc\" = %(desc)s")
            params["desc"] = desc

        is_disabled = kwargs.get("is_disabled")
        if is_disabled is not None:
            sql_set_list.append("is_disabled = %(is_disabled)s")
            params["is_disabled"] = is_disabled

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )
        sql_execute(sql, params)

    def disable(self, pk):
        params = {
            "id": pk,
            "is_disabled": True,
            "update_time": datetime.datetime.now(),
        }
        sql = """
            UPDATE {table_name}     
                SET 
                is_disabled = %(is_disabled)s,
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
        )
        sql_execute(sql, params)

    def last_version(self, program_number):
        params = {
            "program_number": program_number,
        }
        sql = """
            SELECT pv.id, pv.program_id, p.name as program_name, p.number as program_number, pv."version", pv.path, 
                pv.linux_path, pv."desc", pv.is_disabled, pv.create_time, pv.update_time
            FROM {table_name} as pv left join public.programs as p
                 on pv.program_id = p.id
            WHERE p.number = %(program_number)s and not(pv.is_disabled) 
            ORDER BY pv.id desc
            LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result
