import datetime
import logging
from users.models import User

from django.db import transaction

from utils.sql_helper import (
    sql_execute, sql_fetchone_dict, sql_fetchall_dict, sql_fetchone, sql_insert_many, sql_execute_return
)

logger = logging.getLogger("machines")


class DeviceTypeModel:
    def __init__(self):
        self.table_name = "public.device_types"

    def create(self, **kwargs):
        now = datetime.datetime.now()
        params = {
            "name": kwargs.get("name"),
            "number": kwargs.get("number"),
            "desc": kwargs.get("desc"),
            "create_time": now,
            "update_time": now,
        }
        sql = """
            INSERT INTO {table_name} ("name", "number", "desc", create_time, update_time) 
            VALUES (%(name)s, %(number)s, %(desc)s, %(create_time)s, %(update_time)s);
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {
        }

        sql_where_list = []

        name = kwargs.get("name")
        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        number = kwargs.get("number")
        if number is not None and number != '':
            sql_where_list.append("number = %(number)s")
            params["number"] = number

        name_re = kwargs.get("name_re")
        if name_re is not None and name_re != '':
            sql_where_list.append("name ilike %(name_re)s")
            params["name_re"] = f"%{name_re}%"

        number_re = kwargs.get("number_re")
        if number_re is not None and number_re != '':
            sql_where_list.append("number ilike %(number_re)s")
            params["number_re"] = f"%{number_re}%"

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name}
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, "name", "number", "desc"
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", "number", "desc", create_time, update_time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        number = kwargs.get("number")
        if number is not None:
            sql_set_list.append("number = %(number)s")
            params["number"] = number

        desc = kwargs.get("desc")
        if desc is not None:
            sql_set_list.append("\"desc\" = %(desc)s")
            params["desc"] = desc

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result


class DeviceModel:
    def __init__(self):
        self.table_name = "public.devices"

    def create(self, **kwargs):
        with transaction.atomic():
            params = {
                "name": kwargs.get("name"),
                "type": kwargs.get("type"),
                "number": kwargs.get("number"),
                "model": kwargs.get("model"),
                "desc": kwargs.get("desc"),
                "status": kwargs.get("status"),
                "maintainer": kwargs.get("maintainer"),
                "create_time": datetime.datetime.now(),
                "update_time": datetime.datetime.now(),
            }
            sql = """
                INSERT INTO {table_name} ("name", "type", "number", model, "desc", maintainer,
                status, create_time, update_time) 
                VALUES (%(name)s, %(type)s, %(number)s, %(model)s, %(desc)s, %(maintainer)s,
                 %(status)s, %(create_time)s, %(update_time)s)
                 RETURNING id
                 ;
            """.format(table_name=self.table_name)
            device_id = sql_fetchone(sql, params=params)[0]
            sql = """
                UPDATE {table_name}     
                    SET 
                    d_number=%(d_number)s
                    WHERE id = %(id)s;
            """.format(
                table_name=self.table_name
            )
            params = {
                "d_number": "HW-D-{}".format(str(device_id).zfill(4)),
                "id": device_id
            }
            sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):

        params = {
        }

        sql_where_list = []

        name_re = kwargs.get("name_re")
        if name_re is not None and name_re != '':
            sql_where_list.append("d.name ~* %(name_re)s")
            params["name_re"] = name_re

        d_number_re = kwargs.get("d_number_re")
        if d_number_re is not None and d_number_re != '':
            sql_where_list.append("d.d_number ~* %(d_number_re)s")
            params["d_number_re"] = d_number_re

        name = kwargs.get("name")
        if name is not None and name != '':
            sql_where_list.append("d.name = %(name)s")
            params["name"] = name

        maintainer = kwargs.get("maintainer")
        if maintainer is not None and maintainer != '':
            sql_where_list.append("d.maintainer = %(maintainer)s")
            params["maintainer"] = maintainer

        type = kwargs.get("type")
        if type is not None and type != '':
            sql_where_list.append("d.type = %(type)s")
            params["type"] = type

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name} as d left join public.user as u
             on d.maintainer = u.employee_number
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name} as d left join public.user as u
             on d.maintainer = u.employee_number
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} as d left join public.user as u
             on d.maintainer = u.employee_number
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT d.id, d."name", d."type", d."number", d.d_number, d.model, d."desc", u.username, 
                d.maintainer, d.status, mdm.machine_id
             FROM {table_name} as d left join public.user as u
             on d.maintainer = u.employee_number left join public.machines_devices_map as mdm
             on d.id = mdm.device_id
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT d.id, d."name", d."type", d."number", d.d_number, d.model, d."desc", 
                d.maintainer, d.status, d.create_time, d.update_time, u.email, u.username
                FROM {table_name} as d left join public.user as u on 
                    d.maintainer = u.employee_number
                WHERE d.id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        type = kwargs.get("type")
        if type is not None:
            sql_set_list.append("type = %(type)s")
            params["type"] = type

        number = kwargs.get("number")
        if number is not None:
            sql_set_list.append("number = %(number)s")
            params["number"] = number

        model = kwargs.get("model")
        if model is not None:
            sql_set_list.append("model = %(model)s")
            params["model"] = model

        desc = kwargs.get("desc")
        if desc is not None:
            sql_set_list.append("\"desc\" = %(desc)s")
            params["desc"] = desc

        maintainer = kwargs.get("maintainer")
        if maintainer is not None:
            sql_set_list.append("maintainer = %(maintainer)s")
            params["maintainer"] = maintainer

        status = kwargs.get("status")
        if status is not None:
            sql_set_list.append("status = %(status)s")
            params["status"] = status

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result

    def bulk_create(self, data):
        col_names = ["name", "type", "number", "model", "desc", "maintainer", "status"]
        argslist = []

        now = datetime.datetime.now()

        for i in data:
            temp_list = []
            for j in col_names:
                temp_list.append(i.get(j))
            temp_list.append(now)
            temp_list.append(now)
            argslist.append(temp_list)

        sql = """
            INSERT INTO {table_name} ("name", "type", "number", model, "desc", maintainer,
            status, create_time, update_time) 
            VALUES %s 
            ON CONFLICT ("name") DO NOTHING
            ;
        """.format(table_name=self.table_name)

        sql_insert_many(sql, argslist=argslist)


class FunctionModel:
    def __init__(self):
        self.table_name = "public.functions"

    def create(self, **kwargs):
        params = {
            "name": kwargs.get("name"),
            "number": kwargs.get("number"),
            "desc": kwargs.get("desc"),
            "create_time": datetime.datetime.now(),
            "update_time": datetime.datetime.now(),
        }

        sql = """
            INSERT INTO {table_name} ("name",  "number", "desc", create_time, update_time) 
            VALUES (%(name)s, %(number)s, %(desc)s, %(create_time)s, %(update_time)s);
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):

        params = {
        }

        sql_where_list = []

        name = kwargs.get("name")
        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        name_re = kwargs.get("name_re")
        if name_re is not None and name_re != '':
            sql_where_list.append("name ilike %(name_re)s")
            params["name_re"] = f"%{name_re}%"

        number_re = kwargs.get("number_re")
        if number_re is not None and number_re != '':
            sql_where_list.append("number ilike %(number_re)s")
            params["number_re"] = f"%{number_re}%"

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name}
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} 
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, "name", "number", "desc", create_time, update_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", "number", "desc", create_time, update_time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        number = kwargs.get("number")
        if number is not None:
            sql_set_list.append("number = %(number)s")
            params["number"] = number

        desc = kwargs.get("desc")
        if desc is not None:
            sql_set_list.append("\"desc\" = %(desc)s")
            params["desc"] = desc

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result

    def bulk_create(self, data):
        col_names = ["name", "number", "desc"]
        argslist = []

        now = datetime.datetime.now()

        for i in data:
            temp_list = []
            for j in col_names:
                temp_list.append(i.get(j))
            temp_list.append(now)
            temp_list.append(now)
            argslist.append(temp_list)

        sql = """
            INSERT INTO {table_name} ("name", "number", "desc", create_time, update_time) 
            VALUES %s 
            ON CONFLICT ("name") DO NOTHING
            ;
        """.format(table_name=self.table_name)

        sql_insert_many(sql, argslist=argslist)


class MachineModel:
    def __init__(self):
        self.table_name = "public.machines"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()
            params = {
                "name": kwargs.get("name"),
                "type": kwargs.get("type"),
                "number": kwargs.get("number"),
                "position": kwargs.get("position"),
                "desc": kwargs.get("desc"),
                "status": kwargs.get("status"),
                "maintainer": kwargs.get("maintainer"),
                "is_share": kwargs.get("is_share"),
                "create_time": now,
                "update_time": now,
            }
            sql = """
                INSERT INTO {table_name} ("name", "type", "number", "position", "desc", maintainer,
                status, is_share, create_time, update_time) 
                VALUES (%(name)s, %(type)s, %(number)s, %(position)s, %(desc)s, %(maintainer)s,
                 %(status)s, %(is_share)s, %(create_time)s, %(update_time)s)
                 RETURNING id
                 ;
            """.format(table_name=self.table_name)
            machine_id = sql_fetchone(sql, params=params)[0]

            sql = """
                UPDATE {table_name}     
                    SET 
                    m_number=%(m_number)s
                    WHERE id = %(id)s;
            """.format(
                table_name=self.table_name
            )
            params = {
                "m_number": "HW-T-{}".format(str(machine_id).zfill(4)),
                "id": machine_id
            }
            sql_execute(sql, params)

            sql = """
                INSERT INTO public.machines_devices_map (machine_id, device_id, create_time, update_time)
                VALUES (%(machine_id)s, %(device_id)s, %(create_time)s, %(update_time)s);
            """
            devices = kwargs.get("devices", [])
            for d in devices:
                params = {
                    "machine_id": machine_id,
                    "device_id": d,
                    "create_time": now,
                    "update_time": now
                }
                sql_execute(sql, params=params)

            sql = """
                INSERT INTO public.machines_functions_map (machine_id, function_id, create_time, update_time)
                VALUES (%(machine_id)s, %(function_id)s, %(create_time)s, %(update_time)s);
            """
            functions = kwargs.get("functions", [])
            for f in functions:
                params = {
                    "machine_id": machine_id,
                    "function_id": f,
                    "create_time": now,
                    "update_time": now
                }
                sql_execute(sql, params=params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {
        }

        sql_where_list = []

        name = kwargs.get("name")
        if name is not None and name != '':
            sql_where_list.append("m.name = %(name)s")
            params["name"] = name

        name_re = kwargs.get("name_re")
        if name_re is not None and name_re != '':
            sql_where_list.append("m.name ilike %(name_re)s")
            params["name_re"] = f"%{name_re}%"

        number_re = kwargs.get("number_re")
        if number_re is not None and number_re != '':
            sql_where_list.append("m.m_number ilike %(number_re)s")
            params["number_re"] = f"%{number_re}%"

        machine_name_number_re = kwargs.get("machine_name_number_re")
        if machine_name_number_re is not None and machine_name_number_re != '':
            sql_where_list.append(
                "(m.name ilike %(machine_name_number_re)s or m.m_number ilike %(machine_name_number_re)s)")
            params["machine_name_number_re"] = f"%{machine_name_number_re}%"

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        maintainer = kwargs.get("maintainer")

        params = {
        }

        sql_where_list = []

        if maintainer is not None and maintainer != '':
            sql_where_list.append("maintainer = %(maintainer)s")
            params["maintainer"] = maintainer

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        sql = """
            SELECT count(*)
                FROM {table_name} as m left join public.user as u 
             on m.maintainer = u.employee_number
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        maintainer = kwargs.get("maintainer")

        params = {
        }

        sql_where_list = []

        if maintainer is not None and maintainer != '':
            sql_where_list.append("maintainer = %(maintainer)s")
            params["maintainer"] = maintainer

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        sql = """
            SELECT id
                FROM {table_name} as m left join public.user as u 
             on m.maintainer = u.employee_number
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} as m left join public.user as u 
             on m.maintainer = u.employee_number
             {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT m.id, m."name", m.m_number, m."number", m."position", m."desc", u.username, m.maintainer, 
                m.status, m.is_share
             FROM {table_name} as m left join public.user as u 
             on m.maintainer = u.employee_number
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT m.id, m."name", m."type", m.m_number, m."number", m."position", m."desc", u.username, u.email,
            m.maintainer, 
                m.status, m.is_share, m.create_time, m.update_time
                FROM {table_name} as m left join public.user as u 
             on m.maintainer = u.employee_number
                WHERE m.id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)
        result = sql_fetchone_dict(sql, params)

        if result:
            sql = """
                SELECT d.id, d."name", d."type", d.d_number, d."number", d.model, d."desc", d.maintainer, d.status, 
                    d.create_time, d.update_time
                    FROM public.machines_devices_map as m LEFT JOIN public.devices as d
                    ON m.device_id = d.id
                    WHERE machine_id = %(id)s;
            """
            result["devices"] = sql_fetchall_dict(sql, params)

            sql = """
                SELECT f.id, f."name", f."number", f."desc", f.create_time, f.update_time
                    FROM public.machines_functions_map as m LEFT JOIN public.functions as f
                    ON m.function_id = f.id
                    WHERE machine_id = %(id)s;
            """
            result["functions"] = sql_fetchall_dict(sql, params)

        return result

    def retrieve_by_m_number(self, m_number):
        params = {
            "m_number": m_number,
        }
        sql = """
            SELECT m.id, m."name", m."type", m.m_number, m."number", m."position", m."desc", u.username, u.email,
            m.maintainer, 
                m.status, m.is_share, m.create_time, m.update_time
                FROM {table_name} as m left join public.user as u 
             on m.maintainer = u.employee_number
                WHERE m.m_number = %(m_number)s
                LIMIT 1;
        """.format(table_name=self.table_name)
        result = sql_fetchone_dict(sql, params)

        if result:
            params = {
                "id": result.get("id"),
            }
            sql = """
                SELECT d.id, d."name", d."type", d.d_number, d."number", d.model, d."desc", d.maintainer, d.status, 
                    d.create_time, d.update_time
                    FROM public.machines_devices_map as m LEFT JOIN public.devices as d
                    ON m.device_id = d.id
                    WHERE machine_id = %(id)s;
            """
            result["devices"] = sql_fetchall_dict(sql, params)

            sql = """
                SELECT f.id, f."name", f."number", f."desc", f.create_time, f.update_time
                    FROM public.machines_functions_map as m LEFT JOIN public.functions as f
                    ON m.function_id = f.id
                    WHERE machine_id = %(id)s;
            """
            result["functions"] = sql_fetchall_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            params = {
                "id": kwargs.get("pk"),
                "update_time": now,
            }

            sql_set_list = []

            name = kwargs.get("name")
            if name is not None:
                sql_set_list.append("name = %(name)s")
                params["name"] = name

            type = kwargs.get("type")
            if type is not None:
                sql_set_list.append("type = %(type)s")
                params["type"] = type

            m_number = kwargs.get("m_number")
            if m_number is not None:
                sql_set_list.append("m_number = %(m_number)s")
                params["m_number"] = m_number

            number = kwargs.get("number")
            if number is not None:
                sql_set_list.append("number = %(number)s")
                params["number"] = number

            position = kwargs.get("position")
            if position is not None:
                sql_set_list.append("\"position\" = %(position)s")
                params["position"] = position

            desc = kwargs.get("desc")
            if desc is not None:
                sql_set_list.append("\"desc\" = %(desc)s")
                params["desc"] = desc

            maintainer = kwargs.get("maintainer")
            if maintainer is not None:
                sql_set_list.append("maintainer = %(maintainer)s")
                params["maintainer"] = maintainer

            status = kwargs.get("status")
            if status is not None:
                sql_set_list.append("status = %(status)s")
                params["status"] = status

            is_share = kwargs.get("is_share")
            if is_share is not None:
                sql_set_list.append("is_share = %(is_share)s")
                params["is_share"] = is_share

            if sql_set_list:
                sql_set = ", ".join(sql_set_list)
                sql = """
                    UPDATE {table_name}     
                        SET 
                        {sql_set},
                        update_time = %(update_time)s
                        WHERE id = %(id)s;
                """.format(
                    table_name=self.table_name,
                    sql_set=sql_set,
                )
                sql_execute(sql, params)

            devices = kwargs.get("devices", [])
            if not devices:
                sql = """
                    DELETE FROM public.machines_devices_map 
                    WHERE machine_id = %(machine_id)s; 
                """
                params = {
                    "machine_id": kwargs.get("pk")
                }
                sql_execute(sql, params)
            else:
                sql = """
                    DELETE FROM public.machines_devices_map 
                    WHERE machine_id = %(machine_id)s and 
                        device_id not in %(devices)s; 
                """
                params = {
                    "machine_id": kwargs.get("pk"),
                    "devices": tuple(devices)
                }
                sql_execute(sql, params)
                sql = """
                    INSERT INTO public.machines_devices_map (machine_id, device_id, create_time, update_time)
                    VALUES (%(machine_id)s, %(device_id)s, %(create_time)s, %(update_time)s)
                    ON CONFLICT (machine_id, device_id) DO NOTHING;
                """
                for d in devices:
                    params = {
                        "machine_id": kwargs.get("pk"),
                        "device_id": d,
                        "create_time": now,
                        "update_time": now
                    }
                    sql_execute(sql, params)

            functions = kwargs.get("functions", [])
            if not functions:
                sql = """
                    DELETE FROM public.machines_functions_map 
                    WHERE machine_id = %(machine_id)s; 
                """
                params = {
                    "machine_id": kwargs.get("pk")
                }
                sql_execute(sql, params)
            else:
                sql = """
                    DELETE FROM public.machines_functions_map 
                    WHERE machine_id = %(machine_id)s and 
                        function_id not in %(functions)s; 
                """
                params = {
                    "machine_id": kwargs.get("pk"),
                    "functions": tuple(functions)
                }
                sql_execute(sql, params)
                sql = """
                    INSERT INTO public.machines_functions_map (machine_id, function_id, create_time, update_time)
                    VALUES (%(machine_id)s, %(function_id)s, %(create_time)s, %(update_time)s)
                    ON CONFLICT (machine_id, function_id) DO NOTHING;
                """
                for f in functions:
                    params = {
                        "machine_id": kwargs.get("pk"),
                        "function_id": f,
                        "create_time": now,
                        "update_time": now
                    }
                    sql_execute(sql, params)

    def bulk_create(self, data):
        col_names = ["name", "type", "number", "position", "desc", "maintainer", "status", "is_share"]
        argslist = []

        now = datetime.datetime.now()

        for i in data:
            temp_list = []
            for j in col_names:
                temp_list.append(i.get(j))
            temp_list.append(now)
            temp_list.append(now)
            argslist.append(temp_list)

        sql = """
            INSERT INTO {table_name} ("name", "type", "number", "position", "desc", maintainer,
            status, is_share, create_time, update_time) 
            VALUES %s 
            ON CONFLICT ("name") DO NOTHING
            ;
        """.format(table_name=self.table_name)

        sql_insert_many(sql, argslist=argslist)


class MachineUsageRecordModel:
    def __init__(self):
        self.table_name = "public.machine_usage_records"

    def create(self, **kwargs):
        params = {
            "name": kwargs.get("name"),
            "type": kwargs.get("type"),
            "number": kwargs.get("number"),
            "position": kwargs.get("position"),
            "desc": kwargs.get("desc"),
            "status": kwargs.get("status"),
            "maintainer": kwargs.get("maintainer"),
            "is_share": kwargs.get("is_share"),
            "create_time": datetime.datetime.now(),
            "update_time": datetime.datetime.now(),
        }

        sql = """
            INSERT INTO {table_name} ("name", "type", "number", "position", "desc", maintainer,
            status, is_share, create_time, update_time) 
            VALUES (%(name)s, %(type)s, %(number)s, %(position)s, %(desc)s, %(maintainer)s,
             %(status)s, %(is_share)s, %(create_time)s, %(update_time)s);
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):
        machine_name = kwargs.get("machine_name")

        params = {
        }

        sql_where_list = []

        if machine_name is not None and machine_name != '':
            sql_where_list.append("m.name = %(machine_name)s")
            params["machine_name"] = machine_name

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        maintainer = kwargs.get("maintainer")

        params = {
        }

        sql_where_list = []

        if maintainer is not None and maintainer != '':
            sql_where_list.append("maintainer = %(maintainer)s")
            params["maintainer"] = maintainer

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        maintainer = kwargs.get("maintainer")

        params = {
        }

        sql_where_list = []

        if maintainer is not None and maintainer != '':
            sql_where_list.append("maintainer = %(maintainer)s")
            params["maintainer"] = maintainer

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        sql = """
            SELECT id
                FROM {table_name}
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} as r LEFT JOIN public.machines as m
            ON r.machine_id = m.id LEFT JOIN public.user as u 
            ON r.employee_number = u.employee_number
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT r.id, m.name as machine_name, u.username as "user", r.employee_number, r.start_time, r.end_time, 
                r.project, r.content, r.create_time, r.update_time, r.executive_personnel
             FROM {table_name} as r LEFT JOIN public.machines as m
             ON r.machine_id = m.id LEFT JOIN public.user as u 
             ON r.employee_number = u.employee_number
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", "type", "number", "position", "desc", maintainer, status, is_share, create_time, update_time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        type = kwargs.get("type")
        if type is not None:
            sql_set_list.append("type = %(type)s")
            params["type"] = type

        number = kwargs.get("number")
        if number is not None:
            sql_set_list.append("number = %(number)s")
            params["number"] = number

        position = kwargs.get("position")
        if position is not None:
            sql_set_list.append("\"position\" = %(position)s")
            params["position"] = position

        desc = kwargs.get("desc")
        if desc is not None:
            sql_set_list.append("\"desc\" = %(desc)s")
            params["desc"] = desc

        maintainer = kwargs.get("maintainer")
        if maintainer is not None:
            sql_set_list.append("maintainer = %(maintainer)s")
            params["maintainer"] = maintainer

        status = kwargs.get("status")
        if status is not None:
            sql_set_list.append("status = %(status)s")
            params["status"] = status

        is_share = kwargs.get("is_share")
        if is_share is not None:
            sql_set_list.append("is_share = %(is_share)s")
            params["is_share"] = is_share

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result

    def bulk_create(self, data):
        col_names = ["name", "type", "number", "position", "desc", "maintainer", "status", "is_share"]
        argslist = []

        now = datetime.datetime.now()

        for i in data:
            temp_list = []
            for j in col_names:
                temp_list.append(i.get(j))
            temp_list.append(now)
            temp_list.append(now)
            argslist.append(temp_list)

        sql = """
            INSERT INTO {table_name} ("name", "type", "number", "position", "desc", maintainer,
            status, is_share, create_time, update_time) 
            VALUES %s 
            ON CONFLICT ("name") DO NOTHING
            ;
        """.format(table_name=self.table_name)

        sql_insert_many(sql, argslist=argslist)


class MachineReservationModel:
    def __init__(self):
        self.table_name = "public.machine_reservations"

    def create(self, **kwargs):
        params = {
            "machine_id": kwargs.get("machine_id"),
            "employee_number": kwargs.get("employee_number"),
            "start_time": kwargs.get("start_time"),
            "end_time": kwargs.get("end_time"),
            "project": kwargs.get("project"),
            "content": kwargs.get("content"),
            "executive_personnel": kwargs.get("executive_personnel"),
            "status": kwargs.get("status"),
            "is_share": kwargs.get("is_share"),
            "t_mode": kwargs.get("t_mode"),
            "comment": kwargs.get("comment"),
            "create_time": datetime.datetime.now(),
            "update_time": datetime.datetime.now(),
        }

        sql = """
        INSERT INTO {table_name} (machine_id, employee_number, start_time, end_time, project, content, executive_personnel,
        status, is_share, t_mode, comment, create_time, update_time) 
        VALUES (%(machine_id)s, %(employee_number)s, %(start_time)s, %(end_time)s, %(project)s, %(content)s, %(executive_personnel)s,
        %(status)s, %(is_share)s, %(t_mode)s, %(comment)s, %(create_time)s, %(update_time)s)
        RETURNING id;
        """.format(table_name=self.table_name)

        result = sql_execute_return(sql, params)
        return result

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {
        }

        sql_where_list = []

        machine_name = kwargs.get("machine_name")
        if machine_name is not None and machine_name != '':
            sql_where_list.append("m.name = %(machine_name)s")
            params["machine_name"] = machine_name

        machine_name_re = kwargs.get("machine_name_re")
        if machine_name_re is not None and machine_name_re != '':
            sql_where_list.append("m.name ilike %(machine_name_re)s")
            params["machine_name_re"] = f"%{machine_name_re}%"

        machine_id = kwargs.get("machine_id")
        if machine_id is not None and machine_id != '':
            sql_where_list.append("mr.machine_id = %(machine_id)s")
            params["machine_id"] = machine_id

        username_re = kwargs.get("username_re")
        if username_re is not None and username_re != '':
            sql_where_list.append("u.username ilike %(username_re)s")
            params["username_re"] = f"%{username_re}%"

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("mr.project = %(project_number)s")
            params["project_number"] = project_number

        machine_ids = kwargs.get("machine_ids")
        if machine_ids is not None and machine_ids != '':
            sql_where_list.append("mr.machine_id in %(machine_ids)s")
            params["machine_ids"] = tuple(machine_ids)

        start_time = kwargs.get("start_time")
        end_time = kwargs.get("end_time")
        if start_time is not None and end_time is not None:
            sql_where_list.append("mr.end_time > %(start_time)s")
            sql_where_list.append("mr.start_time < %(end_time)s")
            params["start_time"] = start_time
            params["end_time"] = end_time

        exclude_id = kwargs.get("exclude_id")
        if exclude_id is not None and exclude_id != '':
            sql_where_list.append("mr.id != %(exclude_id)s")
            params["exclude_id"] = exclude_id

        status_list = kwargs.get("status_list")
        if status_list:
            sql_where_list.append("mr.status in %(status_list)s")
            params["status_list"] = tuple(status_list)

        employee_number = kwargs.get("employee_number")
        if employee_number is not None and employee_number != '':
            sql_where_list.append("mr.employee_number = %(employee_number)s")
            params["employee_number"] = employee_number

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        maintainer = kwargs.get("maintainer")

        params = {
        }

        sql_where_list = []

        if maintainer is not None and maintainer != '':
            sql_where_list.append("maintainer = %(maintainer)s")
            params["maintainer"] = maintainer

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT mr.id
                FROM {table_name} as mr LEFT JOIN public.machines as m 
                ON mr.machine_id = m.id LEFT JOIN public.user as u 
                ON mr.employee_number = u.employee_number
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page")
        pagesize = kwargs.get("pagesize")

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY update_time desc "

        if page and pagesize:
            sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)
        else:
            sql_limit = ""

        sql = """
            SELECT count(*)
                FROM {table_name} as mr LEFT JOIN public.machines as m 
                ON mr.machine_id = m.id LEFT JOIN public.user as u 
                ON mr.employee_number = u.employee_number
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT mr.id, m.name as machine_name, mr.machine_id, u.username as "user", mr.employee_number, mr.start_time, 
                mr.end_time, mr.project, mr.executive_personnel,
                mr.content, mr.status, mr.is_share, mr.t_mode,
                mr.create_time, mr.update_time
             FROM {table_name} as mr LEFT JOIN public.machines as m 
                ON mr.machine_id = m.id LEFT JOIN public.user as u 
                ON mr.employee_number = u.employee_number
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        # 查询 executive_personnel 对应的姓名
        for item in content["results"]:
            if item["executive_personnel"]:
                try:
                    user = User.objects.get(email=item["executive_personnel"])
                    item["executive_personnel_name"] = user.username
                except User.DoesNotExist:
                    item["executive_personnel_name"] = "Unknown"
            else:
                item["executive_personnel_name"] = None

        for i in content["results"]:
            i["start_time"] = i["start_time"].strftime("%Y-%m-%d %H:%M:%S")
            i["end_time"] = i["end_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, machine_id, employee_number, start_time, end_time, status, project, "content", executive_personnel,
                is_share, t_mode, create_time, update_time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        start_time = kwargs.get("start_time")
        if start_time is not None:
            sql_set_list.append("start_time = %(start_time)s")
            params["start_time"] = start_time

        end_time = kwargs.get("end_time")
        if end_time is not None:
            sql_set_list.append("end_time = %(end_time)s")
            params["end_time"] = end_time

        project = kwargs.get("project")
        if project is not None:
            sql_set_list.append("project = %(project)s")
            params["project"] = project

        executive_personnel = kwargs.get("executive_personnel")
        if executive_personnel is not None:
            sql_set_list.append("executive_personnel = %(executive_personnel)s")
            params["executive_personnel"] = executive_personnel

        content = kwargs.get("content")
        if content is not None:
            sql_set_list.append("\"content\" = %(content)s")
            params["content"] = content

        comment = kwargs.get("comment")
        if comment is not None:
            sql_set_list.append("\"comment\" = %(comment)s")
            params["comment"] = comment

        is_share = kwargs.get("is_share")
        if is_share is not None:
            sql_set_list.append("is_share = %(is_share)s")
            params["is_share"] = is_share

        t_mode = kwargs.get("t_mode")
        if t_mode is not None:
            sql_set_list.append("t_mode = %(t_mode)s")
            params["t_mode"] = t_mode

        status = kwargs.get("status")
        if status is not None:
            sql_set_list.append("status = %(status)s")
            params["status"] = status

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result

    def cancel(self, pk):
        params = {
            "id": pk,
            "update_time": datetime.datetime.now()
        }

        sql = """
            UPDATE {table_name}     
                SET 
                status = 2,
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name
        )

        result = sql_execute(sql, params)

        return result

    def approve(self, pk):
        params = {
            "id": pk,
            "update_time": datetime.datetime.now()
        }

        sql = """
            UPDATE {table_name}     
                SET 
                status = 1,
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name
        )

        result = sql_execute(sql, params)

        return result

    def bulk_create(self, data):
        col_names = ["name", "type", "number", "position", "desc", "maintainer", "status", "is_share"]
        argslist = []

        now = datetime.datetime.now()

        for i in data:
            temp_list = []
            for j in col_names:
                temp_list.append(i.get(j))
            temp_list.append(now)
            temp_list.append(now)
            argslist.append(temp_list)

        sql = """
            INSERT INTO {table_name} ("name", "type", "number", "position", "desc", maintainer,
            status, is_share, create_time, update_time) 
            VALUES %s 
            ON CONFLICT ("name") DO NOTHING
            ;
        """.format(table_name=self.table_name)

        sql_insert_many(sql, argslist=argslist)


def get_machine_reservation_timeline(**kwargs):
    page = kwargs.get("page", 1)
    pagesize = kwargs.get("pagesize", 10)
    start_time = kwargs.get("start_time", datetime.date.today())
    end_time = kwargs.get("end_time", datetime.date.today() + datetime.timedelta(days=1))
    machine_name_number_re = kwargs.get("machine_name_number_re")
    machine_name_re = kwargs.get("machine_name_re")
    machine_number_re = kwargs.get("machine_number_re")

    results = dict()

    results["machines"] = MachineModel().list(page=page, pagesize=pagesize,
                                              machine_name_number_re=machine_name_number_re,
                                              name_re=machine_name_re,
                                              number_re=machine_number_re,
                                              )

    machine_ids = [i.get("id") for i in results["machines"]["results"]]
    if machine_ids:
        results["machine_reservations"] = MachineReservationModel().list(
            machine_ids=machine_ids, start_time=start_time, end_time=end_time, status_list=[1, 3, 5]
        )
    else:
        results["machine_reservations"] = {"count": 0, "results": []}

    return results