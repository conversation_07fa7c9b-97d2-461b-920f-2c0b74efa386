#!/usr/bin/env python
#
# Hi There!
#
# You may be wondering what this giant blob of binary data here is, you might
# even be worried that we're up to something nefarious (good for you for being
# paranoid!). This is a base85 encoding of a zip file, this zip file contains
# an entire copy of pip (version 25.1.1).
#
# Pip is a thing that installs packages, pip itself is a package that someone
# might want to install, especially if they're looking to run this get-pip.py
# script. <PERSON><PERSON> has a lot of code to deal with the security of installing
# packages, various edge cases on various platforms, and other such sort of
# "tribal knowledge" that has been encoded in its code base. Because of this
# we basically include an entire copy of pip inside this blob. We do this
# because the alternatives are attempt to implement a "minipip" that probably
# doesn't do things correctly and has weird edge cases, or compress pip itself
# down into a single file.
#
# If you're wondering how this is created, it is generated using
# `scripts/generate.py` in https://github.com/pypa/get-pip.

import sys

this_python = sys.version_info[:2]
min_version = (3, 9)
if this_python < min_version:
    message_parts = [
        "This script does not work on Python {}.{}.".format(*this_python),
        "The minimum supported Python version is {}.{}.".format(*min_version),
        "Please use https://bootstrap.pypa.io/pip/{}.{}/get-pip.py instead.".format(*this_python),
    ]
    print("ERROR: " + " ".join(message_parts))
    sys.exit(1)


import os.path
import pkgutil
import shutil
import tempfile
import argparse
import importlib
from base64 import b85decode


def include_setuptools(args):
    """
    Install setuptools only if absent, not excluded and when using Python <3.12.
    """
    cli = not args.no_setuptools
    env = not os.environ.get("PIP_NO_SETUPTOOLS")
    absent = not importlib.util.find_spec("setuptools")
    python_lt_3_12 = this_python < (3, 12)
    return cli and env and absent and python_lt_3_12


def include_wheel(args):
    """
    Install wheel only if absent, not excluded and when using Python <3.12.
    """
    cli = not args.no_wheel
    env = not os.environ.get("PIP_NO_WHEEL")
    absent = not importlib.util.find_spec("wheel")
    python_lt_3_12 = this_python < (3, 12)
    return cli and env and absent and python_lt_3_12


def determine_pip_install_arguments():
    pre_parser = argparse.ArgumentParser()
    pre_parser.add_argument("--no-setuptools", action="store_true")
    pre_parser.add_argument("--no-wheel", action="store_true")
    pre, args = pre_parser.parse_known_args()

    args.append("pip")

    if include_setuptools(pre):
        args.append("setuptools")

    if include_wheel(pre):
        args.append("wheel")

    return ["install", "--upgrade", "--force-reinstall"] + args


def monkeypatch_for_cert(tmpdir):
    """Patches `pip install` to provide default certificate with the lowest priority.

    This ensures that the bundled certificates are used unless the user specifies a
    custom cert via any of pip's option passing mechanisms (config, env-var, CLI).

    A monkeypatch is the easiest way to achieve this, without messing too much with
    the rest of pip's internals.
    """
    from pip._internal.commands.install import InstallCommand

    # We want to be using the internal certificates.
    cert_path = os.path.join(tmpdir, "cacert.pem")
    with open(cert_path, "wb") as cert:
        cert.write(pkgutil.get_data("pip._vendor.certifi", "cacert.pem"))

    install_parse_args = InstallCommand.parse_args

    def cert_parse_args(self, args):
        if not self.parser.get_default_values().cert:
            # There are no user provided cert -- force use of bundled cert
            self.parser.defaults["cert"] = cert_path  # calculated above
        return install_parse_args(self, args)

    InstallCommand.parse_args = cert_parse_args


def bootstrap(tmpdir):
    monkeypatch_for_cert(tmpdir)

    # Execute the included pip and use it to install the latest pip and
    # any user-requested packages from PyPI.
    from pip._internal.cli.main import main as pip_entry_point
    args = determine_pip_install_arguments()
    sys.exit(pip_entry_point(args))


def main():
    tmpdir = None
    try:
        # Create a temporary working directory
        tmpdir = tempfile.mkdtemp()

        # Unpack the zipfile into the temporary directory
        pip_zip = os.path.join(tmpdir, "pip.zip")
        with open(pip_zip, "wb") as fp:
            fp.write(b85decode(DATA.replace(b"\n", b"")))

        # Add the zipfile to sys.path so that we can import it
        sys.path.insert(0, pip_zip)

        # Run the bootstrap
        bootstrap(tmpdir=tmpdir)
    finally:
        # Clean up our temporary working directory
        if tmpdir:
            shutil.rmtree(tmpdir, ignore_errors=True)


DATA = b"""
P)h>@6aWAK2mnTQqFMrZI-~di003nH000jF003}la4%n9X>MtBUtcb8c|B0UO2j}6z0X&KUUXrdvZA;
a6ubz6s0VM$QfAw<4YV^ulDhQoop$MlK*;0e<?$L01LzdVw?IP-tnf*qTlkJj!Mom=viw7qw3H>hKz9
FVcXpQ<V`^+*aO7_tw^Cd$4zs{Pl#j>6{|X*AaQ6!2wJ?w>%d+2&1X4Rc!^r6h-hMtH_<n)`omXfA!z
c)+2_nTCfpGRv1uvmTkcug)ShEPeC#tJ!y1a)P)ln~75Jc!yqZE1Gl6K?CR$<8F6kVP)a}pU*@~4OAy
<MFxvzbFl3|p@5?5Ii7qF0_`NT{r7m1lM_B44a9>d5{IF3D`nKTt~p1QY-O00;m^cA{Eu_pjHy0RRA2
0{{RI0001RX>c!JUu|J&ZeL$6aCu!)OK;mS48HqU5b43r;JP^vOMxACEp{6QLy+m1h%E`C9MAjpBNe-
8r;{H19{ebpf{zJ27j)n8%0=-6Z#elILRo@w9oRWWbO{z8ujDS!QAC@3T%nJCf;1rX6ghzu#Z}<GSE4
4EG}J&ngovyJ$%DCh>R@K&*?Hgj1WFD91+adaM4G`4Xs@*hA^t@nbDYdL)-aOjsW~3}QVVby(8=@7U$
Fzj5Y{w!2hUUH`?e9j7WDA;>-1aos>7j{2$~BfyL8p@__Y98dsP#Bs7^<X<wp+-f{6%mc1~N!0T>lWF
=e_gr;(4^?am?Cp93+7b-!?~nb}-$cPSR1zckA*zNp!)$;YjlZrfn&RWNM}=QA7*cb8A{(9@{5!vBfq
rEMoeu5FvJZngI@N#4#(2v$WnMGCAVD?b9t8W^qDfcFBe5ZZF%dPAPaq#<aBs;+HiVj+9PK#6heH_-Q
-kVzlI0rncJH8Q{ZFBFwrpI^^9n>>ikclG~yPvCg`JUGb_W2#PdCXxx}7!|T*xc9qdnTILbO-nAJaF2
~0snMF<S>DU<%E01X4*yW9@|}F2;vY~;0|XQR000O8Ms}iFB^=tD)dBzjss#W56#xJLaA|NaUte%(a4
m9mZf<3AUtcb8d3{vDPTN2bz56Q$bEqvD7eOzLnyL~CDi@L_;ZRYu+Sp^V)ZR6_YZ?pj@13#Z1R`1=l
J)M)n>TOXIt;_f2D8Q^;6`S?Y{9RUgUr+|m;!25C-6tno(2iIDhjlyJ)nM4*651XX%H+qrBEdT{cBla
4$^`0^qPP-6zv*|ge-jzUzxn2=uGMl9#)iA)y8^Cdr~rxdixH}OOJhxFbsp>7(O2Tg09*VTBnRAqE#)
uTB%a`7P2*FzrkVV`K)SOhdyilnqJR#!6l}Q6a+(^)-m{nsTFZ3tf`=GYik||DD|c)gW1pJ_vy8mPk!
87%_j>OLv)_N=Qs$09E*XCaNb7Sbvyz%2H(~=0(GyA#Q^BB=o_mcOvCiSC>?bfF%-ta6OhP5HUX=GiK
PR!(uKJlo!!9~IAAmCk)?77i`J23la2CGx64oXMzMaXkQ<~~8EU?%I}z$$rRNujEIu~M()ri%^Gi%ri
C!gNA@cLO=l6KV$(xV^&hZYbU&TCtOIChO4g;gfcAY_>ak~kgLxGa?L$cMXVJ{&`q`lnqv$j}Cr3vW0
iSMRu8%^e>;b`+HM=<$xdKPpu@6SuMN-LR><I%Q*6KB$|FO|;GzL#iRN>$cFaIP$0f`iClb~O`=>COC
NvJms>bV(-KMn=QG5PXY-h<W~5DV>9vs;@fOIZ_lmTi^Fg`mulO!YZVO^KOIvSWtj)HD-~+vPht4%90
Btz&yv-M$^(udyl?*`G;BgAr}tWa5RRHyc3Sz7-4H^#tC)@T$~!*>z3|0?b+NOYH~Nw+WUjLL%ySwnL
u=yu<vc3u_eSABqMv1<dK3~PnBl0=rs9{uo26@Eh_|L3jt&5T?qD<5Mcu0h17g|$V?PNqMXo*93w<Z=
Ay5kq}l5ej^BRm!k{x=O24Ati8?V8#^|byUl)+2Cp02IUfPD5`wLJ@0|XQR000O8Ms}iF&BwqszyJUM
9svLV3;+NCaA|NaaCt6td2nT9C62L9#V`y-_kP9QSf2V13=C{<Kw>tnX)PsolsJX_Jx&=d9p7_`6i5S
Mvz$qHBvD4Gc2vqMK2J#u@ySRoi8HJ74pBUZpQaDYr)B{xbde<biidBj6SwLQ4C~0fIn*4z#kiE0Sc{
#il<@j|pBMZL#}ADaAESsKi)hSbaxtCyXu4z%H~r`8#VV{D!!(UMBd94M9GxnKfLFZz7T$d6N~+ca-?
5#f2RFEdlxM*G?W6ErQaLd-ZtL;~P)h>@6aWAK2mnTQqFPfaVBa?*********>P003}la4%nJZggdGZ
eeUMUtei%X>?y-E^v8uQL#?LKn&gQE3C4#Qn@ThKqVNNDp=U63SAQ?v2?jR*$!3y9w((B25z~hr>E!V
=a%yTIu%MC&`>ff>`8PBZ$&Am5S?phNulCDC@HdWepHHb)qlj?Id=n;NN3!c*LnlPC<-TpI>d;Lp*Ax
@NYlcAX86|n4s~x3dA%{4b5C^-eJBN!K+x-$+`^E}a>&gXQM{XH`M*P*a}Am<P+AA>WcRbiVczN>%Eu
!-!D~*qf!k-xtUCwxR;$%}PqvdlVHD&~%WR1u#|G-0Bu50PnTiT%Z?T3jfmjc5UXrmz##CF1#g5dw1-
zI=Xd1etfCe>Kbz2Zb=v9mCs;VRH$CIz~P?f2K%DS#dWJIa%?;aogHQ@69cMXq8c`Di1-^-kNu8}WM7
<J_ILoNkv+!s(w0|XQR000O8Ms}iF&;COF5DfqTtSSHi8UO$QaA|NaUukZ1WpZv|Y%gMUX>4R)Wo~vZ
aCy}lU31&G@m;?H$2^2=N#4g(M-#QZB+i|uY2rzoYi~Rn9z{YHYl`Fnq-CAQ|Gm2lfCNEG$#wd2daz7
zcd_5QSjh9dmt9fj?4r6aL|yH9)v`|VoNa5t3R#zFThtZHJ5f}(Oy-4b*#<G-(eu2;qS!aJXjxg`-ol
c3tfl#{N!wjnY|Nu9THgPE?4erz&>lqEEViQFv#c&lp5exlX@K`4=Yr?2i>y^J?Xltg+iQ_#e3NE(*R
a~ZM~)4^&sP`8s~<03{p0f84<Wm3xkxu957}D~C}i&&+$=3a_L0Nmb=TlKBo?wy6}(y~qD|4n$vv;~T
Eq=(m!iz^Jr@#{r1$zsJq{@5MUrfbk|zmEB`zmroso7e+C>GN0V8o%7O?~gDj;U?>xNf}UYx@*^m>F_
-X;xL5cD6ahfeVRL1sI?=W~SiyiN18O>H_k-=<yJCVX%dMaEi~qt&%VDP!#(y^1bAynO%h;xv4(T$=^
Kf9(pv&+jC=O(W`z%U%mvT9(PMd>Xao`zFZ?(K`)1zlII7wid^b;cIxC+!a+W!ST0auUak`d}fFPIDg
O*ac}{6?6<g5t8Zv=u|PG!%asYI7{q={Ykrbc=^mUXSS%J`3kC+syT(tKOtvCq8~P4d^jGDuH{`=Nu(
sCzd_!=!uvV-J-gcs5ehSzD194LJ)rxHxFD1uS+$k$lP-Jo?__laZXtX&<SF}muOI~h6it%CpcXh=TD
1HzkMgWiszs4^l@X)}UHz{m7yduwp<-tvT&x3u4jTMp!#&kACMV4D>+*oF!Xj(aj0@p_<31C`%8oG}Q
^Q?l#=$f3bt13(x2^iNWIYC|}Cgi~%cnj*o@Mdida)Q#}J7rhLb_bb(Kk%$;RmOpJpvBr9XIUo%ICFW
7kUDxhdd`A~UhfL<V5yR^#z`rg46YoiEM3VKTpZD@2m#~@&~O{d*F@Bc)!493-?HvR#<ht+^7snAeLG
17vEWb+?zta;IgxF}C-_ZOx0*m)NK~*N4EBGfBstU_RGD4cKn_D3S?@XciIPxDu7U@|DRK_7{)Q`{(U
@JLy3*F7nWYj`c>oor6{?8dvz&spJjOkw_Hi(kuQ&`Aw(r+6-pZV3b<XvB_BHbov@w~4izJDgqt^!=I
pk47DoC)B>7bU%A)t0pz>T~@K~vph23+y*Np@hiqmmlmW3<~;Ljv1$E!zShmNy2z^oy0q{ordW$NRxA
sM@Yd@6)10ZzBRe5RO5upi;1Y3!8!)uuSl@1=rdft<@LbeWMlhffOXcwql=_#cu!!vC8T#>JscF$NmG
`?oyeysArHMWS%37XSsx>Kn%q>(R~%)n2KyyviN_&zWavNP<ju7=n$3W$PNWWTt@}NCTJ0{m1*hH)?{
pfw>l8-s`^l2B<>50?j0&2B<si>I(PNFMI}>QgLm3H9(K^XxEz7cfXdj0LqI^tf+#ujd*>e{4NmFpX5
<JV8+pRocJs$tQVNdIn#i0_DU`AYrDovUCjQiCergV_-mrJ=SL8mDrB%-MX@vs8|GHxYNvuqRI@38yr
6MRHjilyrww}xsk-jpo#zoo3TD%$+=J@5O%eSwSi+4YPM#_4FA(ki(^VPNJOa|zSLZYe)snqH+{T_;9
2#arM3ZZZG`JcL|e8U_qg$yBsAQ+*eqH!G+?RzSwpq_c?zh#Q~#XwHD)0Iswsa2)?g0^2fV5YR41S4A
$_sE3c*tGK8F9F&g>mj~R%MSfBuwr(~P)xZz_Z6C{z`+cL;Vp(H-@-7<mdMZ8t4_A{9`cRKm~Cx`e*|
tN!QollfYV}a0H=dTD6ut|Z3Nk%h~Ru_M!g{T;A|#75nHDdy=xzQt6lS)S9l0#*;$uOgTf!c4$>c!bk
q;9`#Y?z4pa|>sgDi`zKb`oHOAF^Wk%^#>tOK!&dDn%i;(12Ue|7;zj%X-;`wFUjG5auF(u;h*7z|;y
BInyFvt`DKc<e@pZ+KrGF4OXyf}e+#@>Jr!C531b)(G~DKkQ5q+Ep3%XorWivoJ=v_v~6x<iG3Nm)0`
^#q;@00C0f;;kP{VC;<gX#3Px;%PZ6In}K+&+!|LtK7xfYi0dE-B9mgb2Yood<B>Vb*h5W2UH?|qOf2
_OH-P2dcK+6nO@r4Pf%ykAeK%PI1I6&wvm!16s95=p)sT<49qy-M`$3cJY--+21!k~p@7O~yzZIGVc7
EaS&WjH?&*0x#Tj{(Nt-7<3hFk8xIx)nGk&oLPOUf>p+UUGQU(RY^7NJc5HiE=YFrD(8w7SM!13UGx)
;zHEWPJAdH@?ej!0pdU7|ne4g@<(UA>Xcxw?Gy{q>Jnm9Kuj{_*|0cke%ay!i0(^7>*}BZz^-)8Gvze
~e0`oki+0PewpSf*ZB(0EzCbfyV~cm4a%hfrw>O7OiPWByR$%LN#>yG$A)m{p$hDQdZaGshh+fr@lZO
FWIoGDlX(X;S9K|&?WP}^-cnwqTWm6?D6DwPuh<jsHGe^&Va}tvIJ*{%YOKGgY&+dmykU!9x_gg>-87
`IwK2y4C;{H*o5uzqz0VvZ1l$aR48Xf15+bL@jLRE7)-dAzSACPx6wG#p*>^krf&RxyJ>8U&t6mf8;y
&7X20>E|7^#zJ9}g&9ZTS_0@_tGilvry(2N>c;fdNEUWNTRdp)oR*-4#P*>_k2<?tqAkes(AboB0kWN
A*czCN9B$l-?+vQHimbF9U!r9}#lekaKwip@~}zrk{w2AeVWU7_fW4WRPfw7@%KSpQtA)x`VLI+?=jf
xRg-%>z%2fPFWQ+fV4vAVdPTQfCfV9{j+n9`spC^)=d*nP==@{5e!h{s4I|E86nNIC@@6TBHsbj-k+d
xA5Yj6>yS9zr+@@%Is0tn5ZR^I#N*e6!krv#&e)}WP6_N(yEYqI!bW7pJ1}#S+OlLwbM+oS5}Poskp<
LuOzY<YIJVKhv9Xs<>3CKNAxgDdQZW8kq6%ai90@dNjEer6euQ}6*#ZibeM@yt1%r<=NLt95PE(;?Ze
n7oHou`#$by=Qlx<)eB8-goP_4e<b<q!cqYc1R&47%8fl(Zt4EvM)3i~U&t+dm`?JKg?hr*g1Q-+5kO
FZ>P&k#ssf6QvNDU)STGwO-(zriiGc=JE%*Du?%YXm#V#!|BG)@w_BW0gHy#1Ry8nw0xjg^NiuJ=8c$
A|Joml)u*qe~5|%#X9?DDe+KYB-RjGEsv@b-&TS{?qU;j(e=PGX+)8J66u1=f>S*=)LUTSxqVEhQguP
Ut6FF-6Q=-%8_l9#+^p?yoT{Jy?q$BKT7YRv(-Rt{6<MIUZW`*{u!2aC-rw=t}U`Xht?|}daLtH<F$c
d*fHP#(?^zO9QQwZM=EaJaoKoyb;OBPe{nO`q9x+^ji+_vw6|Uq6}LF0iuzEVLR59s$&otr3nj2OXGt
|*nxS-n_AFA@V3E1RnNo)Igfc{U<Ds)uM6H_x`iy-qhs&hVco)hVZ}8eo;01coK9|r%!4YuVl~5}kD!
_D<)%yldy*PtB(jYx#oKh#<hoq`Z(p!Cj<`CJ}c?bB10w>CxuE@UDM04yk_O)!)5I&w!jE0;Dc)jB$W
0*Xiv-VKK5w^lX<EFmH>$_bi9mEzq30gq?(}ikV+^SPOv_IIfL7%Lz2#{Ij1-Z+YqT$-ddLEq*)jcs5
_>1ipe<^X#--5nMKVUD0%j4G<FF*Y-PC@ANW^n68_xD!2$IH(W*4OrDNm>-K|5F2k1k0hQOEdq*6LKn
!Gy<>6qj_|yp~97_J)~|`rH^<_G;}rzrtIlM^Q2Oq7)C~s$Nub4jRkK?IL7&2f^0|L%t4osLr+gyYXK
(--v|M^c=VPPW*}(lW{_tmBKV=-4<wx^Q4?AN!<y|D!|IHQv<_~(r~m;5(3@}8>uKLk9cV&OXfzA_+v
R%Nv^YCNgdP!A1Xwsu9tf^&r*j*#u{&YB5{~0m5R!MumvIRAN^tGG;1R+_1m}fBV{PksYsf+4|8P7{i
9mpejj<l{xWg<o@YsqDl473V@@Lr8pb>(?9{9BmX82Td`y_*w<vc-z7?`!t=E;@?<n=EhOMou*0ehA@
<oNoKVE14(!)JW};Ji6Xn5?<b$>Vbr05URZW^n?{eWOLSdy<*k7W0!Vf9qWiKgD>&2IR@3%MuMRX3_a
s(liwz3)3_K)oTxjM>vejY_;{qKM1j}S~UYx3Ywdh{tn%|qpyD7)$bek`qy_Cg8oAIdnnpb-Ox2mM5l
d9m((F(G`N_*>;4yiQ1d%R#1rs8+9Yr+Kn5{V;g6NRMA|?0iA|)1<gnIW?wgYK<@tpWweW+*e*sWS0|
XQR000O8Ms}iFQF-=P3Jd@MfG7X}761SMaA|NaUukZ1WpZv|Y%gPBV`ybAaCzNYZI9c=5&nL^VynOiR
mi;5aNJgain`7vcHx&GcHj?(1F;fU(%KZsaF-`5MgM!BnO%}gk-S`*rVT0>vbZm^^FGfkZ#J7flbgK~
uVgNF>Y#Evo6RmR^r0*&EB4atGreveKbRtKerLsQr}JI&SeT1#RUAYqE$t9~_**=>341EBycvI*pBCa
Py|D}NQX71~DXlJYnO%4?x{Q-sm8V4&mvZw_(pj;UJJV2jCwB{>bE~SdQut1Cc~Je4TU8kly^CUPb=H
#H$h^q)MrQg)#pFU}m@pWO)u)YOX4`!)@>}dl-|v;mzSf!Its^@RMXE9rr@B%bo77d-5crBmzOS-7c4
a#M*sOnv-*+rM5+lu8=4xy)iym8bC=+nhsuj<UouYz?Xd1K0N1dl;nhIoTwLu<`*n;G{xF9c4l~^_I6
7Q6K$zxH%Do&F5#l@TNUVQW7b@KPOZ(b*_pT4@n`dLwFY>3I_;Exxn+KPnS5zKCElrGtOu8pl00$(?C
@$fJ7V|gL)fe(MDY+dC-Vo<I#CCpwvdh}QnTMQ8i^0w*}leg0jgEQ%@IIRz*iBeF7Fv&-CGOKr04bL*
Fl$C^9%qp5KW(zS}&gKiDa#L3-k!GVcubeZz${p_mD0ZhuCRX_dMv7*<em0-Sd-W;RJ7sJHMO<t$X^h
~IY$bULDb^58Zs4Lk|4@`wajR2h;IkCHskfNM$hN3NDy<Z3Epi)k!K9YN|H17blP|?PY^{pIl2$B!^H
kiN>vpl?VQ+1dBr+=7E<y@mi#7kc$UVcX%({eu=W)Al-cEsi1t-Z1+mR9AxG*s}POJ@NjoPVhBOM)gT
UPz;bbi1uyex6^?2QoI8qYi_WXTc4j8pa@FS_Zthv?rZ@?&fw&Cn01M_O^HjTLLvGW(&>xk|-R+kMAQ
t2z37Cf+M?lsO0#VyAKi4^l$rf}$TowZRiksVi?uINU-UVbLQ0+%`J{avz9=O3^;D*!SPP1VbB1&l(K
s-JY`cEqKH<q8b5=$jo{1wvs!_@BulQr`>5AS%I5%QIoiZ@t!~*aOIQ;z5ind-e*fzBn5s#;$u(JkpE
}9O>f;Qj$4X$p$(g!%*AK7)8R~Kir(4ReOfn^Rhp5(k)~cZL2+t1n<6|5SI=6)qclR>zQ{LOovN_t2v
>#<@&mk*Bs}OGzkDeoOSE<l2*DFJMX8)8=`4G=R%FCfm=ykLocFD3ou!<;wp&$!6~T!<Ad{*R1&+#W;
VfRBV4A!=9YY8ggVa>U<%v|jR<ydRbL?20`Gs=|m#~;xbMQv79K``apw~Ln_9Un@#{q%8wWV2JUhW`K
y^gW?vOJaY64m2U8&fND`Nv0pelqm1>)ClxXM>y%1}SA?s~38(+mBPa?by9?Qc@g(97iYH!sTs>$j`G
(xTs^bwIK)Z6Gk6&CaZv+QJcCVy&zE|7g|cpFfx(*^2q|Gw2nT?(;jsOeq#$OGcaIF%tg?D=pOsph)f
~#GhH>0#q;8*ZfRT3F2)<}AU`6$P;@D^hYr$Nkjey!OJZy!zK|}0h^L@}|E<EJ+bViKk>GNaG0#y~k4
%BM$0XE$OHK>NCZCfS%en9rWk4;%qO7<7NQ_aEa><Q-wdRtA`0T9&JvuC)_VwFNq9%V`JJGI}k8T!T%
<D(X$KAu@<&*B=$@1o=lh#*tCN(HGh&<F?H7qoX4p}A+dOCR7l~1lhz{Ein!j=RH(Umc<P08`B2Eu}_
j*by;W{Hh3JND@I6QY;yIuMx391iD$NlGrKaGiw6M)WlgiFZ{T{6+M0K7K^hs%yb#%-J8?LCP-$VP0c
fLO17DX!s-(40#)^_ANl9PY4p*JkL>zA07;)T?Hiuxhyo0ky()%q4T=1FAinK?UPEcs;a1(^x~O|dN`
)bw1JZZIuBi>X^a@8bc+D72yLr0q0GB<9)zCAFY#Ul;zMx_Wd`adAgT<-Bnu*=sZUgM77jriNxsVO!$
80#ken~z;TiW+jR(4KHO+C0!k9PW?|BD(eYHfebFFjec3XoT@)1D$4Bf|n7OMu>^S|I;fmj@=?e@R<5
@R|25i)xfE|&R85Qt)cz=+Ye1mS5dF~S$DNKk{B^92`pJWv@&1Cx1x+wpm%xvVHYQm(GcAT1G@y|$?K
?I21(k!L5eh+?daO%yXYwrwlOx$tmu=(NlroTk34Y0?{7d3{)8i25(bQwL9r+)BVH$`2h`E~EZLzrO0
clrmtQ>mO<ri>r7Si}=!qSBRC<;zzV`<YkRd(UGo3>JBcG&KeNbKq~C{BK{(dW157B;FgwHUhHiwo#7
ZobY&e#4T?qmB@L52hO)Nn-KkP~g+UK>g?ddmW34c(Qp9Q+Xy5mB&KIer<KXr!9s36ha@_09-ock6AZ
DXJcB%0QZYm~c|M8xxUjck`*Dq5+IeyEY@4c7|=Nl95L5CZBo)5dNs81VBxTA)4{P!U=`R%0L4i^yi2
udr=OWxT#cC1$R)51itUkxv>qHsZHBj*axnZGb|zxf6z?^(KqGua&w=B&Ki>eL^gk8|mjyNQarv**2)
?pkg1E^SDXQi0BE)thkO=1d8ET+GLb->s&A*x&$nZxyF%UGJj7EPZA#c4+J<cEe0A9=P<3{}8VzrnB+
QX`x)ddytl+Qb7SFaxVx`#8w(D0_7LB?gP}g#VDb`lB4rk&&9(}DF1N9{N5@jA)J!i*ogX3G9Rk<Gx0
Z0l3dAxhSv3~m$KUIFO}WgkBQDzV_8xIJT%z2343g#{HN+ua-ba3XaC>Rd>dCkrQG=-C3FJaBOxS<iE
v)g)kBA~O?RrNLwPC+Zfm1nqV4z(=+EaJcDpKpB?9Pnj^#oWcRuGT^`=ss7q$Hy#rK%=ZF5mcz>og5=
4l$?jUhTtV(rEDs(+{5cLloFUiB`y<Ew9<U%g7+Kfk(qnLK;??D<vSfV-?%10kG}1HUmz-R*F@B(UZH
S@s!YU7y|gUgd2D?MeD`$`}C?W9ha_tcxP^h3QN^3=vlS=8z}4?U?Q!`itD%d{89Y7Z`Ne!gsOFdbc#
`-gzXxrKC4QQOirbMXu+mIrPH0;8L8w7U*@FqjY1}yF~XXOMxU{r<EY4Yx9I&1&k++iG$(FNEhmpjrh
V0bzwED?e^iHX2VoETKKM-*Gg14rYq7>Rym0OR87*ZjQTiI<W`<@L7EQARP?~iCyL9ssMCdpjX0eVN6
Sp({?k3+`%0FBY{T$+kX5+jkb)33WaKdyj_(Nd`i)su9G&#LN^aD)Mld7V@PIT4{VxJYuIN->*;$oLj
e8?>&cM1-yL{YSUvG<=`Tz%O4f7Xnhw>|9e~mXRQ*TTXm%N)>q(Q?yxsOF}_njyEU)US_3N+ww@{Xx@
nq%!3of$}XuQPuWjRaZBg>rBs7Yuu(-a~e02la02LW=O;;j5PraGZ?=d*cp}G!_b(`d?_S-P%>vn~9s
SZbfq=|I>G4$c!y9(pt=IcbIE|B+z9rxN&_r(>O}g&k!R87I}%CQ&lnxU&wmKLpvbFx83<!5cTT=Nwg
Za0-?HB)BQkS;j8{*h<pOq^4$5XnDrbv@S;xnk=wBdRN~*JPf2_h!v7Z%e=Rd}CNgd6-fsWZSQM$vL7
`7d+mJ(jE~+$1fuXYkIK&n1s9!}EqMg_IwpezMXQaOP(jQ{Au2S3&n*Cfnb|(}IY2E>~em!LMtf;e;O
-sk0W2yGFu8V%cRPj{n|DG=2q@^~1Uz!!SsybGhUV7N6Ha`&mXJD(|0yx%KAO7ymS8ta560lRd`k~hF
O#g;CAK7)++uag?>hcd?eEH-t1q1Tfj&3W@X|#Sqe?mYvGd<^|!6CQwxsS2vGkfDKM1LSLG|&UO;O*V
OG{^IXamz8;2PSrC68|#&Q2{$j`jaTJu6KfOGu$}{$f|Z;$FIAm+<}R*ON4&V+-fV4tD}G|Kw!c(=+k
L(dQEp{I=N(Eg7KyEu`CfY>n<$&bZp^5nEzJlyfyk4P)h>@6aWAK2mnTQqFQkG8MM?8006Z%0015U00
3}la4%nJZggdGZeeUMV{dL|X=inEVRUJ4ZZ2?n&0B46+sG0Au3xdo;1CHXGfi6*JppR#J3Be!96PX++
!cnvqs5iQh9U_rWk)sK-`;0tcgbCnvVB26)CgjU+?}2Gw;cyT@MDp$(wl7+*J+W9O`OL!awGFvC|PgI
(de?+NKwmbljcQM-0Wtf1ChrYITGSfiMuMTYnh8Q7fS{tR%s?xh()(?wxv~{=(mWKDwb(n%S7Cz^;*O
l$btAQcUW|WFMzPQPIJ2=tzRl2v1Gi)=0ixkCJenw<Gdy;gi7<9Op3drN<<>nvPuEwM%=As1=QElpk`
^ri3g0FDC4veOFDX06`N5I1fx;9DT}H$Tgtdnva-*zVi{-Bek+vyq;_gV07Shj>0tBtFyBqZQM#<B;s
gwguQNFnr>VmCGkN!6SK{k=NhrnHRD9T$fUV(_X&FXoj!k$K$}daF%anyY2H8S*k~^-dqMG)fzkxV@E
Vfy4R@6Vp(;`k}G968Z&e_&!)*KO+Ws+8E@467eD&yKN|K;TD==_(<{mY>`Hx6%ZWPOS!;O*WWn^Z0B
a+#}bB_m)o#pms2G`fiIG@b8RL}KnqEbP7(FT~{<kk)aYjDAk@1RIPH#sPf~rb5-#WHkEm{Orxi+vtb
G%j2`dcgI*45O!&v1|nTqLWRtf)NknAV03);DWdh4(aGDh^NZsn+ITA~1sMQ#u$EZ~?Rp_TKp#KjKqZ
Qmub7H-6&f2?G92k8bEZ~{`{U*D#XQ)88o&cRABnEZQZCa~y5tpaPtSiiJe>zOS#cd_de!Al)p8?#Nf
XIyE>Auj&jXd#Qoq0YlvedN&KBZ0zfG$mXWRF{g0y)c^IN@v<@NsLePkH*=H&F)E{i@LUhq=bSLG~sL
4P8{g()Z~;rXZIi;I(^<LLb2==cKEGS#7`V`{z;-S#tKZ$INa=OZ0G`gC}Daul7OA09>LXQ%&c0J<Q2
-~@0q%2*Y#yiv<>6p^QiB4`AJc6R>ZcqZP(yK5;R0d^_{+vFD!*EJl@w#L&!<VCfKv-IDRybM8rD@^1
{M9m9^JEs3;ta0MtZ}fY{JcI{&7k`$<JCb@R_zPlNC2!&?0ozz_RI#m=QjDd;*rH<JJ0UMduo%Lef5H
0hK+`0wWSPZFsC+~^c@Ru^CDfW%sXDG|Mb38|9N?wKsRQfkkFx~{w#c)cC@Z-F<FW#l4Sp!zP$6(sx%
TfPO?{Fo1OT~#+#@iiLq7S$D|}hZEIL6LfR#0c6<*pPBFW7~V5>ry0!HKJ4YTiOxf4kt6>kRj>KG7`f
`S<w14kblQQ!zjMxh8i5Ub$5tk!X<#ID$iJA@eQfnZ7MFSTKX_k`2KOJE1t#L3@V2$SYh6{pzIchO^y
f=QQQp(?Yq9)td=cs()fgH}eKr-(Snj+uEBV6Mo6O<O)1Mrw4;R=o!-yJ~POR0tQo4*yl8`ItUUMaTP
1#0qLfuSgUoCWPcYEL3_hCO>F&X))qo?h-l}=X$3hWQL?X>6iYQ3?PHPFL#fD=#e46ln^R8$Z_zWU@~
nMFnWFG9n#}=RR<0ILL-yQyT>rGe4(Qqz8JRbA<zi@zB%7@PqQJ}O*>1;cg@6Am(`2zI`V#4*XQW8OP
JaE0KHpgFj!xI8yEEx*i5?QB#Yfx7HTzkUlq5AJPOt(IJ~dmeaM{R<q{S^2?5z51EMc}qGnP_hV)^bD
xs0WA_Uf2!TYq(58t1_D<dJP;XjoKY`#dz9vM~uZ?nsE48$|V=c>A>B!^v{t@g6i6p0XkwLrFc0)K}M
TFZ?uv8M$tvT+8ltJr{IU@kotaBd2dqNA@1oJ1d7*JO{dM^jcn?iMO`3+%`mb0K53BW`Lz&`}#<fDUk
y;ThN>iR)O?4`76>LhwqL$UKaYH6`r5k(s4+6d?!CR#U7lGUCt-h!;p)Gvt&JMT1$B&9XmQP>NOLlD!
mtXuY`FmK=^6$dgg?4rbawB+ST@({9GRePg0SC*sdMU%uA|d#jk<w+s$SkS0bINN(+M2STkWixx-#2p
Q=6{X-BE$K$#Wk6|LhYX7i_%P}G^a@P*JlSyx<|D^p!ybmqw!CCv!U(g~vZ;@|#`$GI6mobI1J4#SwD
e5X-ekN*kv4qDr;6$S!To!pJL^{D%vtHog0*1oRRCKcM#e#I<-lNRaZ}3YCX0`cFGWD~<X4gXp5v}1K
9{iCpK<)L|q4y78dFZA{kQd9OWy6!SQ^;Mk)<j}qOB%2Xm>pGUs%yQJLk}1<n}m}B+RU#-mvz96A>)Z
_XgOy_j$--t<G^rEiNG~wE!L(d1U#yXNG8Z$8tNK<&?a|=%nOcw+VyfsWp}$yZ`M-P@N;QVrS-0DD;T
<63Mrwi>aN~99s~AwCUy1(t5v$B!S`67s2>x;h&(}SBz3jxxDg$xH8+S@EqfxD)nPZDT>W9url?%5ix
WhOFYImxp%H>D3lYLMs^yz)7r1)G8a>EEzn`H*mZE}4U<#oII}zl35IJ(9lv$o}AD{u|@z_0L)WKTwf
MAnctDQZ;R|ZDLL<=C-Ci<Ykqu{MXe6{|T8z|k#6}m`3CvK>Gpt_E`M*2rT=~h^HI(eo{TY9drt0zem
6$5g7A=;vb*$U)PIKn8!22qs?hSfIH1&go+3-uq68*j>N*8vus6$p#VHBl2ZK|JPKGu9rN=uB1qvbL`
|-Y<&{!haI#a@2#`E4w_NFdOzo$d!u^qTjvc{X^ggTkR3{RLgWm0v~pB@7Qn};SVJ(p6H(;V=YV5DO$
(3GHSenn_mpNjppr&;7&zpn@W!vy_d6?9gh3y`cl@hsV$|4Wz$g#!zE&kfp62dVT(1m5C*0XwvJU~j<
zk-iH{iln8k6D^cIf>)<NuJj~*j$2Y)w2U-`UVu#tfUwn6Of*u>T62sqm~JN5+PsNOc;r+mW-_mS2=U
*Epo)%;J?5JIG*Op^N;GoP99K5ftc2iiZN#{Ja#5yfWfFz+k5DZqQ}6@6{(q}Y9P&`nysRoumzC=D<P
d#j&cWzbLX26=Uc>_GRrO?<dYllkjKDBhz?1Te&F?!i+qx!&G{K@U{VReW&j$4$KB)Mis6g^E|Tu2@}
y28r=KhfPVI@EL~BkGhTDVcPVQ$n0tV;RGSr*dqz`C0=V|-&U36WdC3Tq^ljA1#;f7pEW;eyi_~*w$f
N>uR!oPp&QL%ny-p+@Jp5Igsc$XDpZRrt(+af9y2mXHY%`|u$Es?aVTGO?fuBucBb%|os)+8B-ocqly
u3b=)z*0w|O(&@JUKWeVO($D*E9hz9?mGWgFZD6H?<<f32fKe8hO=A7GNN^Gh}+0wViK82$V{`&$Kvx
iPw8$tsbc_T>ts6~}M`MfA;3L%e5dA5c~@;D^2D`a=IN-dE`@R8LgJ7Dt-S%nPUEGOU4nlssJx6hVC-
yq8a0C2?J3YNVxGAZdRO#)LsDAfv~Lgm;1>r&-3TQdFP$;+53kTh)};gcIGbc}N<T{G+U(Fjc*4iEq>
KmSVrM_YC|{v>WzSI*&wpd%1IDfuvMe!!+0z7UfoVT#R$n;6ia(ObI3xk5u3)2b}(~5OU31>aMgVi|P
Y;2(>33e5bOvA<?T|Zt0%1On}?i^OXj|!ZCPL>*!66lJ%Pd|C_di%iYf114&Bb3yZHhoe8fs;l8?RZu
u5hf0(^S?l>1$@N<d0&9_;$81%7PH~XEw%g<@axAa8*o}RNvwygerLk^rq2wZ089u!7E5L?uI0F$l|A
(lb;PYoJP`$O}=R@*+kO)#S6ylyC?Ia%r3Wtyq5R&KS8GGpxht+CDDbXQk+IcW=vE7Atl+%yaeg0K4d
(V%<sy1EvRZ|0qq!%@R`K8-xtXTAJZy%gi7&-bmG*uXlq$Mk!y^h1}0X}*=C-rG(#%*fGM<5pM$fm?I
xXDLhF958Lxwv<!UAxL_Aye4a7&0$<3swU%3&NS&-GvlSpp@s*#HM;VzTsvLLS=Y1Q%IdmMavZ#Iyb>
{Y3hxeYkMQ3Z5Iqj2qRWmw{G&VVy4o&nO)>6rkM!AQve3$==Hw?m?Prq!@WHHgAPeodN#BPybzMS5iE
7IR20Q8dR5KscN})%c7RGbA-|*_OVHT%{+uF4@DqZ5~yS=2KR?#(^CGgd<O7Ub{izU9-vbki^pqduL?
k?U<$^Wg3ti3G?oux;Gj;}a0Rkqc2x0ZREF6axk_|}@HnznZgjG6<^rkf4-kc$}F-I&l`^il?F6sPO_
X+#V8)3RWC=jKU>B32J8AmOub$8Qk1pOe=%wNBhrEf&VSbK+nU`!P@PRvt<Ef1sDBxo|0^Sl4x_X0Kk
|Acfjq(~#Axa#zN$%Cvli+<Yt5t3QALmp?rx2_BmK8qSG9mn!o4Dliihs<{fg?@;$3mg^Y448dDDSLf
~z^JlKPG>``maL3d2r?UO$v+1wje?6P{@HziKdD7;GFG#*3mvX0hZta*<zdSWA%o}}b9NB=&>w2|dhJ
$7{A*b$+IoRCnv=qDXr3=MFYHB^UY5%gnobA8I;LT4Z_Gk7)WxRaHLpW(aR~#*de8V5M-kR^@O|eA|t
DOwx5iO)`OPX?R<^m{r-#`)ct-Ky~wK^~`hIKHQLqd%mfrc(+&1VN?E(Yv#ih)gIm7<_#s3%<Ai?osQ
JD!>PZg9}dx4QOBrxDv#@^+Dbwc}~ZqxV(L9Fn2h-dDwdlw(5;BK0gV5(<Y^pqRkxcX~*O6K$7q#2{e
b@CTv6Kp0MvbwkygaAS}IjV;rn#PX{@&lYxcZYZtgm8M7AoBtfnU3aNONNq%G+5+O&Uw@rxEyX{?xaC
^Uc#!_pvT662tHNqe)z%S1cPPx4#y<&@wnitlAG~7jO~iEVsvV4|IjczY1*A4v7>R4Bq3*0!2^^>e!n
R05dz7xks}&R)1{(8>FaRJEt<nTBoA%PcF}u#XDzeN;pRaeFf;kjFL2$R+`otC<3N-t4wNjZuo*h|x+
j~q^nZ-Muj?g-q=?cNwmppEC4fgJk<ER?(>88kw4az92G}{_5I5WRdR)XX~%z+4OdnOJ1p2X$5<I~fb
cvsvu6YRRz0FG>HT}gnf)i!6=wjzNQ^hedKX@H|z@12?zae$|E_Lb~=hY#B`wNm@zwFC9LezkDNQA$}
I5Qps_517i(G;KJc>=LAtzc%`?_URbx%-_+lYInGSb-aJ6&^>@wbu!J)5;s6P?qClp-D_jA06mN6{>c
Q9-s(E(GlwU6=PvRfxm?!+&I3HB7aqrN^|&`EGRp_0sOPK);L(ePclFa{-`+bQ7e3E!Uv;)R7w<^RpW
3|e0~6otsVL12#Gxwqsf2^Z5i}fOx`;ey=SpDjOhl)ybP}h*>$ApZKQcKqbHsKe^`GF-<Y0;t&(vx(`
^NyArH|h$+SbFo(`V@1u(<MVuY^=zlUw&s%huebV5NX2@7M`)Q<z+DMHda(&hHQXjtRZJ_hx^5?!DQ>
wS$jlioZ8<)U7+ctY2Lnyk2}o(C%M;b^8Lgr!(uS!9Wec!&X+MNPlP$zH^)ANU*=$yH|ev#%g$t?ML-
MyA?x3&3H35>i?O8!`D@CuaAg$xHpPI`<E9F<NNM-&NtP=BpCe{P)h>@6aWAK2mnTQqFPWRT?=p^001
y=000{R003}la4%nJZggdGZeeUMWq4y{aCB*JZgVbhdEGtja^uF4|Me7O>|HHslUUnHTq)hj6?vEI-S
U3uB(0N_tfN3+NFo9O1OrG*vsjh7XUNsP#=XP6NuK2TV`eb;AVqClb(K@uD-xLL>FMd|?-_ZXH~ARKy
o|F{iAu>xl*=Nk7R#(EMIMB=!9w~+N0-Z3iB%R=i4<=?TwIEG?=MAJtVNlLc$H^GDdt7C5<$8ymvOof
_c8t!H+h`<(>N_<kp{^PG(XC6Sp=o@1;K>><i}iwC61hlnbhEd8H{8=J`0jW%;Q8V1GiktBUQ~bg3u-
!!Lv$@gaXWyNTgXQBALrH5*amL`A44T9UW<G!z`W0i##ZlESzVh{8%RO%sff5Fi2$mZLxs4nXh?JE}g
c8ZKu}ih)|DV+GUm{%FrlW25~wP3!rd5TaTbmE`yRAm+Kq}r(0hB@oX}E{%Z34hu81EABh)nSdPT&5=
oa8Bk?9yCH{*Kl96~%3y7aSr10U0ArkHHfCO3LGo@evzmR{f;zF)uT3Wnbr*}aTN9XmceGu+sp<t9&=
O6iLw__29OCM;fvc#;ec+Ou(<^xXvmhT+;$l_e8D(S(2K&^t}w#p48slp(aJDTEhT8hGFSpIpFw8uvR
eyH~{P`I9^{qz-lKdN6hfCHhVDwPjgLe|TB&W~{xDu?OFc6YN8{FN+&C@2Hd^SeOF7qF&9JgblrJNwa
ieiEm*rt2H{ysJ6jiT}d`RpK79D@I@4<vv2@mW4PmT<$Mq`G%eirqeW7$?0@>baeFO=!v)pgZVs5BHR
m4jv_f1(^yS!<t&&@Lzu`w9SfMsP>jD7GhlzFaUsj9NZ}VOU>Pn4h4=4Q!T9IvF9u`&J^cKiJlMZOln
J<f-(@Kfkm2WroW~!hxcgx;5nwzZpfD0~{1vY52uHmh(F@IQ$ClIR>F4~5p%Ie&fI;d9gg3%tyn$n4M
FdPknA(V54f!4Z5RaV*MGs(cWapK}1y%xidkTXL)bPk9N>2;MaU?R>ZSy3%Kk?#4nibL;iZA-w+d=2v
Pw9pywDo%WQ^TSErz}nfh@h|XBrXTQ583^6mIUeT$z@T=A+C0q1WJjsIG+?nRt&7F(9;KjdbyNh25XL
tCnUD=NzCf5FX8}3Rb?DnAm&32<22=-D8hphS4WX>ifm_Cpm|mR-=Nq5H39M*l~v(06-kqz^akmwL?C
8W8ZJdPM_!J@5%!QNX%v86mTNNv!-IBNRWJ$!ZY7lht)hG}2>=eg5U?E(bd|=i$6?*(5{MtlMw>85MU
W^^iD{TrktAqA6vZr^je;2g98h8<s;y_xEkJgcVmuc2OORS3D5Sv<v?B66={rIVW~c|#Si)EU02u<r2
lN0QUMJEe4EzVOCP!GTXn=SO0(5*{aSu{Gps!U9n0*|h!j$7#6(_)7H@;aZ!?b$xv|;!e?NZp9aXFpV
yr^U{AK7o8*B@_zL65~#t-!84!Uwq3yAO&KaPh96!p1R=Z8v9Rqw=nB&qG_37Ii~S93vfYU(>$5uAAt
^a3HnYI*oC;V4JNDY>w;sVDk>rDAo5(j06*YttHG8F-cVg{1O1ipk-B)2-h8+-NliNeA@w}S))Z09Pz
7yja8yH3`&$#xP-#9oxI^luZsf+#kCphGp_-zZgfKI;M)gBtr1&F#J1}ja8;=ijYVod#`d3S<}_^QBa
6Y9_^bKC#i&N6*~V<H6T?Z(*P3@&wwGjBG~V0whZgp8vx(&>XktINz>u0hA--_lRItp0p>KEF-22xJH
J?sN=uW4;(b(H#v1!hE`>i*0cPv<-1;D^Y#9qL3$If;m?KEgC=)U0wUaP4c_oRi#XT=tlwbfeFTDR5T
dRuC;>z4XkZ%a#l-PV5ZZEaUVx3=G*b+fHE{I-73Bq_-7sn*cDg<-#ef<GDC!pCjfxRwE~k$mhxOi+)
V;AV|H-099fu)>r~6ydEU>U(F;tYZmo)7-n-Fq|!zZb@{#u`sq*&#sTfmGiV_DtP6!AbAdKKm{d)o|q
q~Fpj5Lnyj0W=ZQFDtw$<~RGsF55o^IhCh{&wt*55#Hdnss;TpTtu_v1Y*4XYeFxx*aXO4PXgzfcoe|
VS^hs@Xi^It$c;q|v?R2uF7_SgT|(P8+DOwJf7zsThN_MiX#x4-`7+sDlio*p#Arm_9^%=zzLQ11%%+
(Ap}iTxAnPKqbw;`#S-yXWnA;R0%G-<DQ$#t@*FTMBI_2ECGDH|W032d!%pWS81ISJbZN3aAYJ0n0#y
M8dCU;p$bzpH_omXPyTW>F{}4ke@Qoa1AaCO%lO;;v_q><nY9UT+n?u_uh~Vys==mo(?*khGhjV6uFK
Hp0I!UoWgijL7v<$|MGj8qL(40abMBX3EiHq%c_GTkAxUi1-npnXBX((SSfgawmvJeKgqEC4m~R!e^G
<3ms}5Z9>f@xy<f^y<m;Tf`(?ID&_owE<fDz%8l1n?%?MoI63E_sY(jqO#_#%YXfLc7yRn5ocDs*Fcd
y6QWB1_mk%<-8k)P(mcdXXN>Knsv?mY*7)ZRo055<P#djk!AxNUKtF$B@2>TVE^9AFbAMtTLf_!O^fQ
xoIM{!eLFG*XP?%B9G);P^Dg#F=#3Vo4(m&#uLdr8syfYrhF|*qlLapnBF_2PBoMr1kyn6fjl?tOX1a
%(6SMi-=Zn?zhNN+p$C#??Os(HaQc&`Rcb~4iXz-MzRcG&LH)38OHNCB(C*62fv}SD~M>nECV}>@i>O
ny?!x<#YlXhvh*K_dv_2F`3=QemyPFMlls8x8=m#{{{esEdcD;_^<7fi<-{Pw1;U*h2siNlR0;3x<nr
{z>E)@1wvBi8!}sIY?_R$5+?bk*7b&15^^(}|Teu!Ee4Ky_R{)yvN?|B+8Qif2M>4}C0#N*j0eecoj)
<zbxp@gs-(}^?tV$!A&CLzz7#T!E7WOd*o{`Q0smNq4NI1bru-s9Fah{UHqp&zS?Y0Wm7(R(AZYK+pV
><6Zj-qM>{SFE6MKED7Alk_cXYI{&DPZ6P;CuF*=U52Gc~Ar^4ga)B*lb2D-EP|&31v8b!f0=U?lVbD
K<pr+29pjjo(ojk#SlZk7g@4h5LJ*=_^TaRa`2+~lJngqHB515VCISVfb4<Lksq9pt_fVtWEfP6mmZ0
gF6<6+py)U?rgjVI2H8g<iEkyrHQ7E;Zj=wDP1p_*%{}r3Hr*+19TrC}rtKX$3uvr8e73DECYWI1nqp
c34LatTlSdHaF=hpXO?N4wGj)>Ez_6S$y^D)1ML9*%3)9@xN5!S|Tim{H3G~E0#FH~15eVRr)W0@;wc
L`C*H)t!A;#CAXRBG9(oA+v>tRG@a*m4!T5%*iPU~PkC>Ro@17r`PZ%{1M)$#bN>tT2CKyMMkvJ@MJx
25&o)IFi&7G%Xj0#6iff~Q_dBdGSRQ9}kd_yDsfhy-jnW)u)i3P7ki2sGhjtzEm?Nnt9Km1mgHXZe3H
5({I@b50}-s{*xdp|wLJUrtgQ{1Vy7a(xFJrqkc23Fy>ltp}IU18Rsw5g1OQ%Ex6khHX-BIFM9<RjjC
W88m^34g4k^K?FR7X6OUM!X7|X0h>V(kA&RIFfP&G1+1YSqF9kVq&5<mqU3B*|1nZd06SGgO?QN|Lf*
w$rO2*)5f=v!^;A*rgL{)#m&9R-OGgG$y(`_(-pTSlD{jGiOyT7oNAK0;<rxCi&3HzS(uve#x>5^A+w
tDx5&<XS<C(GnubUgva;({!9(fNYiCpFM8jy`a1pO>`5P@wv_FyEc7vzf@gY8Wnr6#qj_NYM9hte#%9
-`HNhM@QKsrlMc4jS0cZmgkcF*nC_YY5y{I2#sqH3FRmCii8aQT~gGT?^0Qao_M}Mh$E_19kifJHL^r
2i3iXk5oxYXYFbNcHFq%FQrUSUZRHHCn<G%+;5UyWbDxpIv-M)4VINi2D=ZO)0ROtAi2mLg496z3m<l
8QB-+3bOF}%YIuFde9=CiwLzVw<iL||N7XPbqlsgSY`Z<iWV5=KrYD|mETHXE9tEAI(uBVBuv=tg?eh
6fI7ypSuWQev+($8#s%346*-{C|b17Z(uT?%!v^7=-5FiMws#k=lgZoHpvZ3~NV0KYPyN@b>2$WmcVW
c>CFxd>@nu-NQ*0kY1mUC#djggxYFtET=dLkE#6Y!1*lM$)4)Ea`04kzpm_iY4p`!o17-I6IVx8@kM)
XX%W!pQpb%6{G@l4+3<G;k6AYQ7-2@P>Z3ULk0Fg}+;#g?>f9AIX;O6z{n;n>QBKErlQ&EVHT6-5z(6
skX3lQ=?8<SM*yg4<XP^J%L7|b=br454r(wZEc;ib@oVweJe~NbZ1m(?;^7CqgB_VrNq7kVCiQFX#6J
{XWJjgzNlt8B*lhKH(odD#=Tv~N?8D4reiTKMP_zH=jz5|`4QZzXpA<+m~E#|Di_lB;}l%gu!!?ohSz
2m3^Wb12n2eZI;jvYahjsxsT|`8>u0IJ=6a{q6we!-`-^