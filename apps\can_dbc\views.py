import json
import traceback
import logging
import os
import uuid
import urllib.parse

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.files.storage import FileSystemStorage
from django.conf import settings

from .serializers import (
    DBCSerializer, DBCListSerializer, DBCUpdateSerializer, Signal2MessageSerializer,
)
from .models import DBCModel
from .dbc_parser import DBCParser

logger = logging.getLogger("machine")

dbc_fs = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, "dbc"), base_url="/media/dbc")


class DBCsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = DBCListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DBCModel()

            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = DBCSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            file = serializer.validated_data.get("file")

            prefix = str(uuid.uuid4())
            dbc_path = dbc_fs.save(f"{prefix}_{file.name}", file)

            parser = DBCParser()
            parser.parse(dbc_fs.path(dbc_path))

            DBCModel().create(
                user=request.user,
                project_id=serializer.validated_data.get("project_id"),
                project_name=serializer.validated_data.get("project_name"),
                project_number=serializer.validated_data.get("project_number"),
                file_name=file.name,
                file_path=dbc_path,
                file_url=dbc_fs.url(dbc_path),
                content=json.dumps(parser.content),
                version=parser.version,
                description=serializer.validated_data.get("description"),
                tags=serializer.validated_data.get("tags", []),
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DBCDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = DBCModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = DBCUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = DBCModel()
            model.update(pk=pk, **serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            model = DBCModel()

            dbc = model.retrieve(pk=pk)

            dbc_fs.delete(dbc.get("file_path", ""))

            model.delete(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class DBCDownloadView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = DBCModel()
            dbc = model.retrieve(pk=pk)

            if not dbc:
                return Response({"err_code": 1, "msg": "DBC文件不存在。"}, status.HTTP_404_NOT_FOUND)

            file_path = dbc.get("file_path", "")
            if not file_path or not dbc_fs.exists(file_path):
                return Response({"err_code": 2, "msg": "DBC文件已被删除。"}, status.HTTP_404_NOT_FOUND)

            response = Response(dbc_fs.open(file_path).read(), content_type="application/octet-stream")
            file_name = dbc.get("file_name", "dbc_file")
            file_name = urllib.parse.quote(file_name)
            response["Content-Disposition"] = f'attachment; filename="{dbc.get("file_name", "dbc_file")}"'
            return response
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 3, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class Signal2MessageView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = Signal2MessageSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            dbc_id = serializer.validated_data.get("dbc_id")
            dbc = DBCModel().retrieve(pk=dbc_id)
            if not dbc:
                return Response({"err_code": 1, "msg": "DBC文件不存在。"}, status.HTTP_400_BAD_REQUEST)
            file_path = dbc.get("file_path", "")
            if not file_path or not dbc_fs.exists(file_path):
                return Response({"err_code": 2, "msg": "DBC文件已被删除。"}, status.HTTP_400_BAD_REQUEST)
            parser = DBCParser()
            parser.parse(dbc_fs.path(file_path))

            message = parser.signal2message(serializer.validated_data.get("message"))

            return Response({"err_code": 0, "data": message, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class Message2SignalView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = Signal2MessageSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            dbc_id = serializer.validated_data.get("dbc_id")
            dbc = DBCModel().retrieve(pk=dbc_id)
            if not dbc:
                return Response({"err_code": 1, "msg": "DBC文件不存在。"}, status.HTTP_400_BAD_REQUEST)
            file_path = dbc.get("file_path", "")
            if not file_path or not dbc_fs.exists(file_path):
                return Response({"err_code": 2, "msg": "DBC文件已被删除。"}, status.HTTP_400_BAD_REQUEST)
            parser = DBCParser()
            parser.parse(dbc_fs.path(file_path))

            message = parser.message2signal(serializer.validated_data.get("message"))

            return Response({"err_code": 0, "data": message, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
