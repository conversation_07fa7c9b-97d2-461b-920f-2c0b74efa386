from rest_framework import serializers


class TagListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number_re = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)


class TagSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    number = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class TagUpdateSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)
