--机台表
CREATE TABLE IF NOT EXISTS public.machines(
    id serial primary key, --id
    "name" varchar(255), --机台名称
    "type" varchar(255), --机台类型
    "number" varchar(255),  --机台资产编号,
    m_number varchar(255),  --机台编号,
    "position" varchar(255),  --机台位置
    "desc" varchar(255),
    maintainer varchar(255),  --机台维护人员工号
    status int, --机台状态 0：空闲中 1 使用中 2：报修中 2：维修中 3：保养中
    is_share bool default false ,  --是否可以被多个项目共享
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.machines OWNER TO atpms;

--设备类型表
CREATE TABLE IF NOT EXISTS public.device_types(
    id serial primary key, --id
    "name" varchar(255), --设备类型名称
    "number" varchar(255),  --设备类型编号
    "desc" varchar(255),  --描述
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.device_types OWNER TO atpms;

--设备表
CREATE TABLE IF NOT EXISTS public.devices(
    id serial primary key, --id
    "name" varchar(255), --设备名称
    "type" varchar(255), --设备类型
    "number" varchar(255),  --设备资产编号
    d_number varchar(255),  --设备编号
    model varchar(255),  --设备型号
    "desc" varchar(255),  --设备描述
    maintainer varchar(255),  --设备维护人员工号
    status int default 0, --设备状态 0：空闲中 1 使用中 2：报修中 3：维修中 4：保养中 5: 报废
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.devices OWNER TO atpms;

--功能表
CREATE TABLE IF NOT EXISTS public.functions(
    id serial primary key, --id
    "name" varchar(255), --功能名称
    "number" varchar(255),  --功能编号
    "desc" varchar(255),  --功能描述
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.functions OWNER TO atpms;


--机台和设备的映射表
CREATE TABLE IF NOT EXISTS public.machines_devices_map(
    id serial primary key, --id
    machine_id int,
    device_id int unique ,
    create_time timestamp, --创建时间
    update_time timestamp, --更新时间
    UNIQUE (machine_id, device_id)
);
ALTER TABLE public.machines_devices_map OWNER TO atpms;

--机台和功能的映射表
CREATE TABLE IF NOT EXISTS public.machines_functions_map(
    id serial primary key, --id
    machine_id int,
    function_id int,
    create_time timestamp, --创建时间
    update_time timestamp, --更新时间
    UNIQUE (machine_id, function_id)
);
ALTER TABLE public.machines_functions_map OWNER TO atpms;


--机台预约表
CREATE TABLE IF NOT EXISTS public.machine_reservations(
    id serial primary key, --id
    machine_id int, --机台id
    employee_number varchar(255), --工号
    start_time timestamp, --预约的开始时间
    end_time timestamp, --预约的结束时间
    status int, --预约状态，0 审核中 1 已批准 2 已取消 3 已签到 4 超期未签到释放 5 已结束 6 超期未批准释放
    project varchar(255),  --申请项目
    "content" varchar(255),  --测试内容
    is_share bool default false,  --是否与其它项目共用
    t_mode varchar(255),  --使用模式
    comment varchar(255), --备注
    executive_personnel varchar(255),  --机台执行人员
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.machine_reservations OWNER TO atpms;


--机台使用记录表
CREATE TABLE IF NOT EXISTS public.machine_usage_records(
    id serial primary key, --id
    machine_id int, --机台id
    employee_number varchar(255), --工号
    start_time timestamp, --开始使用时间
    end_time timestamp, --结束使用时间
    project varchar(255),  --项目
    "content" varchar(255),  --测试内容
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.machine_usage_records OWNER TO atpms;