import datetime
import json
import os
import uuid

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone
from django.db import transaction, models
from django.core.files.storage import FileSystemStorage
from django.conf import settings

from test_cases.models import TestCaseModel, TestCaseRequirementMap
from functions.models import FunctionModel
from test_products.models import TestProductModel
from .signals import plan_create

at_file_fs = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, "at_files"), base_url="/media/at_files")


class AtFile(models.Model):
    pathname = models.CharField(max_length=255, default='')
    relative_pathname = models.CharField(max_length=255, default='')
    remote_pathname = models.CharField(max_length=255, default='')
    title = models.CharField(max_length=255, default='')
    extension = models.CharField(max_length=30, default='')
    size = models.IntegerField(default=0, db_column='size')
    object_type = models.CharField(max_length=30, default='', db_column='object_type')
    object_id = models.IntegerField(default=0, db_column='object_id')
    gid = models.CharField(max_length=48, default='')
    added_by = models.CharField(max_length=30, default='', db_column='added_by')
    added_date = models.DateTimeField(null=True, blank=True, db_column='added_date')
    downloads = models.IntegerField(default=0)
    extra = models.CharField(max_length=255, default='')
    deleted = models.BooleanField(default=False)

    class Meta:
        db_table = 'at_file'
        managed = False
        app_label = 'test_plans_v2'


class TestPlanModel:
    def __init__(self):
        self.table_name = "public.test_plans_v2"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            params = {
                "name": kwargs.get("name"),
                "desc": kwargs.get("desc"),
                "plan_use": kwargs.get("plan_use"),
                "plan_type": kwargs.get("plan_type"),
                "test_type": kwargs.get("test_type"),
                "test_type_name": kwargs.get("test_type_name"),
                "p_start_time": kwargs.get("p_start_time"),
                "p_end_time": kwargs.get("p_end_time"),
                "pic_name": kwargs.get("pic_name"),
                "pic_email": kwargs.get("pic_email"),
                "sample_information": kwargs.get("sample_information"),
                "status": "DEBUGGING",
                "test_cases": kwargs.get("test_cases"),
                "test_case_extra_args": kwargs.get("test_case_extra_args"),
                "test_product_id": kwargs.get("test_product_id"),
                "m_version": kwargs.get("m_version"),
                "sub_versions": kwargs.get("sub_versions"),
                "product_version": kwargs.get("product_version"),
                "creator_name": kwargs.get("creator_name"),
                "creator_email": kwargs.get("creator_email"),
                "project_name": kwargs.get("project_name"),
                "project_number": kwargs.get("project_number"),
                "project_id": kwargs.get("project_id"),
                "abnormal_stop": kwargs.get("abnormal_stop"),
                "finish_notice": kwargs.get("finish_notice"),
                "create_time": now,
                "update_time": now,
            }
            test_cases = [TestCaseModel().retrieve(i) for i in params["test_cases"]]
            params["test_cases"] = json.dumps(test_cases)

            sql = """
                INSERT INTO {table_name} ("name", "desc", test_type, test_type_name, plan_use, plan_type, p_start_time, 
                p_end_time, pic_name, pic_email, sample_information, status,
                test_cases, test_case_extra_args, test_product_id, abnormal_stop, finish_notice, creator_name, creator_email, project_name,
                project_number, project_id, m_version, sub_versions, product_version, create_time, update_time) 
                VALUES (%(name)s, %(desc)s, %(test_type)s, %(test_type_name)s, %(plan_use)s, %(plan_type)s, %(p_start_time)s, 
                    %(p_end_time)s, %(pic_name)s, %(pic_email)s, %(sample_information)s, %(status)s,
                    %(test_cases)s, %(test_case_extra_args)s,  %(test_product_id)s, %(abnormal_stop)s, %(finish_notice)s,
                    %(creator_name)s, %(creator_email)s, %(project_name)s, %(project_number)s, %(project_id)s, 
                    %(m_version)s, %(sub_versions)s, %(product_version)s, %(create_time)s, %(update_time)s)
                RETURNING id;
                ;
                """.format(table_name=self.table_name)

            r = sql_fetchone_dict(sql, params)
            tp_id = r.get("id")

            plan_create.send(
                self.__class__,
                project_number=kwargs.get("project_number"),
                version_name=kwargs.get("m_version")
            )

            sub_versions = json.loads(kwargs.get("sub_versions"))
            for i in sub_versions:
                plan_create.send(
                    self.__class__,
                    project_number=kwargs.get("project_number"),
                    version_name=i.get("name")
                )

            return tp_id

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        name_re = kwargs.get("name_re")
        if name_re is not None and name_re != '':
            sql_where_list.append("name ilike %(name_re)s")
            params["name_re"] = f"%{name_re}%"

        creator_name_re = kwargs.get("creator_name_re")
        if creator_name_re is not None and creator_name_re != '':
            sql_where_list.append("creator_name ilike %(creator_name_re)s")
            params["creator_name_re"] = f"%{creator_name_re}%"

        name = kwargs.get("name")
        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        status = kwargs.get("status")
        if status is not None and status != '':
            sql_where_list.append("status = %(status)s")
            params["status"] = status

        status_list = kwargs.get("status_list")
        if status_list is not None and status_list != '':
            sql_where_list.append("status in %(status_list)s")
            params["status_list"] = tuple(status_list)

        creator_email = kwargs.get("creator_email")
        if creator_email is not None and creator_email != '':
            sql_where_list.append("creator_email = %(creator_email)s")
            params["creator_email"] = creator_email

        m_version = kwargs.get("m_version")
        if m_version is not None and m_version != '':
            sql_where_list.append("m_version = %(m_version)s")
            params["m_version"] = m_version

        # 处理 result_two 参数
        result_two = kwargs.get("result_two")
        if result_two is not None:
            if result_two == "null":  # 处理前端传递的字符串 "null"
                sql_where_list.append("result_two IS NULL")
            else:
                sql_where_list.append("result_two = %(result_two)s")
                params["result_two"] = result_two

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        action_type = kwargs.get("action_type")
        if action_type is not None and action_type != '':
            sql_where_list.append("test_type = %(test_type)s")
            params["test_type"] = action_type

        id = kwargs.get("id")
        if id is not None:
            sql_where_list.append("id = %(id)s")
            params["id"] = id

        plan_type_list = kwargs.get("plan_type_list", [])
        if len(plan_type_list) > 0:
            sql_where_list.append("plan_type in %(plan_type_list)s")
            params["plan_type_list"] = tuple(plan_type_list)

        test_type_list = kwargs.get("test_type_list", [])
        if len(test_type_list) > 0:
            sql_where_list.append("test_type in %(test_type_list)s")
            params["test_type_list"] = tuple(test_type_list)

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY update_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} 
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, "name", "desc", abnormal_stop, finish_notice, test_type, test_type_name, plan_use, plan_type, project_name, 
            project_number, project_id, creator_name, creator_email, create_time, m_version, product_version, status,
            result, result_two, update_time, p_start_time, p_end_time
            FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        status_map = {
            "DEBUGGING": "调试中",
            "REVIEWING": "评审中",
            "RUNNING": "执行中",
            "COMPLETED": "已完成",
        }

        for i in content["results"]:
            i["product_version"] = json.loads(i["product_version"])
            i["status_name"] = status_map.get(i["status"])
            # i["test_cases"] = json.loads(i["test_cases"])

            # total = len(i["test_cases"])
            # exec = 0

            # sql = """
            #     SELECT id, test_plan_id, test_case_id, test_case_version, result_two
            #     FROM public.test_case_result
            #     WHERE test_plan_id = %(test_plan_id)s
            # """
            # result2 = sql_fetchall_dict(sql, {"test_plan_id": i["id"]})
            # result2 = {(j.get("test_case_id"), j.get("test_case_version")): j for j in result2}

            # for j in i["test_cases"]:
            #     r = result2.get((j.get("id"), j.get("version")), {}).get("result_two")
            #     if r is not None:
            #         exec += 1

            # i["progress"] = f"{exec}/{total}"

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, "name", "desc", test_type, test_type_name, plan_use, plan_type, p_start_time, p_end_time, pic_name,
                pic_email, sample_information, status, test_cases, test_case_extra_args, test_product_id, project_name, project_number,
                project_id, creator_name, creator_email, m_version, sub_versions, product_version, abnormal_stop, finish_notice, create_time, 
                update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["product_version"] = json.loads(result["product_version"])
            result["sub_versions"] = json.loads(result["sub_versions"]) if result["sub_versions"] else []
            result["test_cases"] = json.loads(result["test_cases"])
            if result["test_case_extra_args"]:
                result["test_case_extra_args"] = json.loads(result["test_case_extra_args"])
            result["create_time2"] = result["create_time"]
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        sql = """
            SELECT id, test_plan_id, test_case_id, test_case_version, exec_id, result, result_two,"value", remark, generation_mode,
                creator_name, creator_email, create_time, update_time
            FROM public.test_case_result
            WHERE test_plan_id = %(test_plan_id)s
        """

        result2 = sql_fetchall_dict(sql, {"test_plan_id": pk})
        result2 = {(i.get("test_case_id"), i.get("test_case_version")): i for i in result2}

        if result:
            for i in result["test_cases"]:
                i["result"] = result2.get((i.get("id"), i.get("version")), {})

            total = len(result["test_cases"])
            exec = 0

            for j in result["test_cases"]:
                r = result2.get((j.get("id"), j.get("version")), {}).get("result_two")
                if r is not None:
                    exec += 1

            result["progress"] = f"{exec}/{total}"

        if result:
            if result["test_product_id"]:
                result["test_product"] = TestProductModel().retrieve(result["test_product_id"])
            else:
                result["test_product"] = None

        if result:
            test_case_ids = [result["test_cases"][i].get("id") for i in range(len(result["test_cases"]))]

            reqs = TestCaseRequirementMap.objects.filter(
                test_case_id__in=test_case_ids,
            ).values(
                "test_case_id", "test_case_version",
                "requirement_id", "requirement_version", "requirement_number"
            )
            req_d = {}
            for req in reqs:
                if req_d.get((req["test_case_id"], req["test_case_version"])) is None:
                    req_d[(req["test_case_id"], req["test_case_version"])] = []
                req_d[(req["test_case_id"], req["test_case_version"])].append({
                    "requirement_id": req["requirement_id"],
                    "requirement_version": req["requirement_version"],
                    "requirement_number": req["requirement_number"]
                })
            for tc in result["test_cases"]:
                tc["requirements"] = req_d.get((tc.get("id"), tc.get("version")), [])

        return result

    def retrieve2(self, pk, machine_number):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", "desc", test_type, test_type_name, plan_use, plan_type, p_start_time, p_end_time, pic_name,
                pic_email, sample_information, status, test_cases, test_case_extra_args, test_product_id, project_name, project_number,
                project_id, creator_name, creator_email, m_version, product_version, abnormal_stop, finish_notice, create_time, 
                update_time
            FROM {table_name}
            WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)
        data = FunctionModel().list().get("results")
        module_map = {}
        for i in data:
            module_map[i["number"]] = i["name"]
            if i.get("children"):
                for j in i["children"]:
                    module_map["{}-{}".format(i["number"], j["number"])] = j["name"]
                    if j.get("children"):
                        for k in j["children"]:
                            module_map["{}-{}-{}".format(i["number"], j["number"], k["number"])] = k["name"]
        t_status_map = {
            "PENDING": "待评审",
            "REVIEWING": "评审中",
            "APPROVED": "评审通过",
            "REJECTED": "评审不通过",
            "DEPRECATED": "废弃",
        }

        p_status_map = {
            "DEBUGGING": "调试中",
            "RUNNING": "执行中",
            "COMPLETED": "已完成",
        }

        plan_use_map = {
            "FULL_FUNCTIONALITY_TEST": "全功能测试",
            "VERSION_REGRESSION_TEST": "版本回归测试",
            "SPECIFIC_VALIDATION_TEST": "专项验证测试",
            "PROBLEM_VALIDATION_TEST": "问题验证测试",
            "DURABILITY_TEST": "耐久测试",
        }

        if result:
            result["product_version"] = json.loads(result["product_version"])
            result["test_cases"] = json.loads(result["test_cases"])
            if result["test_case_extra_args"]:
                result["test_case_extra_args"] = json.loads(result["test_case_extra_args"])
            result["plan_use_name"] = plan_use_map.get(result["plan_use"])
            result["status_name"] = p_status_map.get(result["status"])
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

            for tc in result.get("test_cases", []):
                tc["module_name"] = module_map.get(tc["module"])
                tc["module_2level_name"] = None
                tc["module_3level_name"] = None
                if tc["module_2level"]:
                    tc["module_2level_name"] = module_map.get("{}-{}".format(tc["module"], tc["module_2level"]))
                    if tc["module_3level"]:
                        tc["module_3level_name"] = module_map.get(
                            "{}-{}-{}".format(tc["module"], tc["module_2level"], tc["module_3level"]))
                tc["module_str"] = "/".join(
                    [i for i in [tc["module_name"], tc["module_2level_name"], tc["module_3level_name"]] if i])
                tc["version_name"] = f"V{tc['version']}.0"
                tc["status_name"] = t_status_map.get(tc["status"])

                if result["test_case_extra_args"] and result["test_case_extra_args"].get(str(tc.get("id"))):
                    tc.update(result["test_case_extra_args"].get(str(tc.get("id"))))

                if not tc.get("reTest"):
                    tc["reTest"] = False

            if machine_number:
                result["test_cases"] = [
                    i for i in result["test_cases"] if
                    ((not i.get("disabled")) and
                     (i.get("machine_number") == machine_number or not i.get("machine_number")))
                ]
            else:
                result["test_cases"] = [
                    i for i in result["test_cases"] if not i.get("disabled")
                ]

        sql = """
            SELECT id, test_plan_id, test_case_id, test_case_version, exec_id, result, result_two,"value", remark, generation_mode,
                creator_name, creator_email, create_time, update_time
            FROM public.test_case_result
            WHERE test_plan_id = %(test_plan_id)s
        """

        result2 = sql_fetchall_dict(sql, {"test_plan_id": pk})
        result2 = {(i.get("test_case_id"), i.get("test_case_version")): i for i in result2}

        if result:
            for i in result["test_cases"]:
                i["result"] = result2.get((i.get("id"), i.get("version")), {})

        if result:
            if result["test_product_id"]:
                result["test_product"] = TestProductModel().retrieve(result["test_product_id"])
            else:
                result["test_product"] = None

        return result

    def retrieve3(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, "name", "desc", test_type, test_type_name, plan_use, plan_type, p_start_time, p_end_time, pic_name,
                pic_email, sample_information, status, test_cases, test_case_extra_args, project_name, project_number,
                project_id, creator_name, creator_email, m_version, product_version, abnormal_stop, finish_notice, create_time, 
                update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["product_version"] = json.loads(result["product_version"])
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")
            if result["test_case_extra_args"]:
                result["test_case_extra_args"] = json.loads(result["test_case_extra_args"])
            result["test_cases"] = json.loads(result["test_cases"])
            result["test_cases"] = [i.get("id") for i in result["test_cases"]]

        return result

    def retrieve4(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", "desc", test_type, test_type_name, plan_use, plan_type, p_start_time, p_end_time, pic_name,
                    pic_email, sample_information, status, test_cases, test_case_extra_args, project_name, project_number,
                    project_id, creator_name, creator_email, m_version, sub_versions, product_version, abnormal_stop, finish_notice, create_time, 
                    update_time
            FROM {table_name}
            WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["product_version"] = json.loads(result["product_version"])
            result["test_cases"] = json.loads(result["test_cases"])
            if result["test_case_extra_args"]:
                result["test_case_extra_args"] = json.loads(result["test_case_extra_args"])
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def get_test_plan_test_cases(self, pk, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)
        disabled = kwargs.get("disabled")
        execute_mode = kwargs.get("execute_mode")
        module = kwargs.get("module")
        name = kwargs.get("name")
        status = kwargs.get("status")
        result_two = kwargs.get("result_two")

        params = {
            "id": pk,
        }
        sql = """
            SELECT test_cases, test_case_extra_args
            FROM {table_name}
            WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if not result:
            return

        result["test_cases"] = json.loads(result["test_cases"])
        if result["test_case_extra_args"]:
            result["test_case_extra_args"] = json.loads(result["test_case_extra_args"])

        sql = """
            SELECT id, test_plan_id, test_case_id, test_case_version, exec_id, result, result_two,"value", remark, generation_mode, file,
                creator_name, creator_email, create_time, update_time
            FROM public.test_case_result
            WHERE test_plan_id = %(test_plan_id)s
        """

        result2 = sql_fetchall_dict(sql, {"test_plan_id": pk})
        result2 = {(i.get("test_case_id"), i.get("test_case_version")): i for i in result2}

        for i in result["test_cases"]:
            i["result"] = result2.get((i.get("id"), i.get("version")), {})

        if disabled is not None:
            if disabled:
                f_tc_ids = [int(i) for i in result["test_case_extra_args"]
                            if result["test_case_extra_args"][i].get("disabled")]
            else:
                f_tc_ids = [int(i) for i in result["test_case_extra_args"]
                            if not result["test_case_extra_args"][i].get("disabled")]
            result["test_cases"] = [i for i in result["test_cases"] if i.get("id") in f_tc_ids]
        if execute_mode:
            result["test_cases"] = [i for i in result["test_cases"] if i.get("execute_mode") in execute_mode]
        if module:
            module = module.split("-")
            if len(module) == 1:
                result["test_cases"] = [i for i in result["test_cases"] if i.get("module") == module[0]]
            elif len(module) == 2:
                result["test_cases"] = [i for i in result["test_cases"] if i.get("module") == module[0] and
                                        i.get("module_2level") == module[1]]
            elif len(module) == 3:
                result["test_cases"] = [i for i in result["test_cases"] if i.get("module") == module[0] and
                                        i.get("module_2level") == module[1] and i.get("module_3level") == module[2]]
        if name:
            # 模糊查询
            result["test_cases"] = [i for i in result["test_cases"] if i.get("name").find(name) != -1]

        if status:
            result["test_cases"] = [i for i in result["test_cases"] if i.get("status") == status]

        if result_two:
            tcl = []
            for i in result["test_cases"]:
                tcr = i.get("result", {})
                if tcr.get("id") is None:
                    if "null" in result_two:
                        tcl.append(i)
                elif tcr.get("result_two") is None:
                    if "pending" in result_two:
                        tcl.append(i)
                else:
                    if str(tcr.get("result_two")) in result_two:
                        tcl.append(i)
            result["test_cases"] = tcl

        result["count"] = len(result["test_cases"])
        result["test_cases"] = result["test_cases"][(page - 1) * pagesize: page * pagesize]

        return result

    def delete_test_plan_test_cases(self, pk, test_case_ids):
        params = {
            "id": pk,
        }
        sql = """
            SELECT test_cases
            FROM {table_name}
            WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if not result:
            return

        result["test_cases"] = json.loads(result["test_cases"])
        result["test_cases"] = [i for i in result["test_cases"] if i.get("id") not in test_case_ids]

        sql = """
            UPDATE {table_name}
                SET test_cases = %(test_cases)s
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        sql_execute(sql, {"id": pk, "test_cases": json.dumps(result["test_cases"])})

    def add_test_plan_test_cases(self, pk, test_case_ids):
        params = {
            "id": pk,
        }
        sql = """
            SELECT test_cases
            FROM {table_name}
            WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if not result:
            return

        result["test_cases"] = json.loads(result["test_cases"])
        result["test_cases"] += [TestCaseModel().retrieve(i) for i in test_case_ids]

        sql = """
            UPDATE {table_name}
                SET test_cases = %(test_cases)s
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        sql_execute(sql, {"id": pk, "test_cases": json.dumps(result["test_cases"])})

    def update_test_plan_test_cases(self, pk, test_case_ids):
        params = {
            "id": pk,
        }
        sql = """
            SELECT test_cases
            FROM {table_name}
            WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if not result:
            return

        result["test_cases"] = json.loads(result["test_cases"])

        if not test_case_ids:
            for i in range(len(result["test_cases"])):
                result["test_cases"][i] = TestCaseModel().retrieve(result["test_cases"][i].get("id"))
        else:
            for i in range(len(result["test_cases"])):
                if result["test_cases"][i].get("id") in test_case_ids:
                    result["test_cases"][i] = TestCaseModel().retrieve(result["test_cases"][i].get("id"))

        sql = """
            UPDATE {table_name}
                SET test_cases = %(test_cases)s
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        sql_execute(sql, {"id": pk, "test_cases": json.dumps(result["test_cases"])})

    def check(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT  test_cases
            FROM {table_name}
            WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if not result:
            return False, False

        result["test_cases"] = json.loads(result["test_cases"])

        sql = """
            SELECT id, test_plan_id, test_case_id, test_case_version, result
            FROM public.test_case_result
            WHERE test_plan_id = %(test_plan_id)s
        """

        result2 = sql_fetchall_dict(sql, {"test_plan_id": pk})
        result2 = {(i.get("test_case_id"), i.get("test_case_version")): i for i in result2}

        e_f = True
        r_f = True

        for i in result["test_cases"]:
            r = result2.get((i.get("id"), i.get("version")), {}).get("result")

            if r is None:
                e_f = False

            if not r:
                r_f = False

        return e_f, r_f

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
    """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()
            params = {
                "id": kwargs.get("pk"),
                "update_time": now,
            }

            sql_set_list = []

            name = kwargs.get("name")
            if name is not None:
                sql_set_list.append("name = %(name)s")
                params["name"] = name

            desc = kwargs.get("desc")
            if desc is not None:
                sql_set_list.append("\"desc\" = %(desc)s")
                params["desc"] = desc

            abnormal_stop = kwargs.get("abnormal_stop")
            if abnormal_stop is not None:
                sql_set_list.append("abnormal_stop = %(abnormal_stop)s")
                params["abnormal_stop"] = abnormal_stop

            finish_notice = kwargs.get("finish_notice")
            if finish_notice is not None:
                sql_set_list.append("finish_notice = %(finish_notice)s")
                params["finish_notice"] = finish_notice

            test_type = kwargs.get("test_type")
            if test_type is not None:
                sql_set_list.append("test_type = %(test_type)s")
                params["test_type"] = test_type

            test_type_name = kwargs.get("test_type_name")
            if test_type_name is not None:
                sql_set_list.append("test_type_name = %(test_type_name)s")
                params["test_type_name"] = test_type_name

            plan_use = kwargs.get("plan_use")
            if plan_use is not None:
                sql_set_list.append("plan_use = %(plan_use)s")
                params["plan_use"] = plan_use

            p_start_time = kwargs.get("p_start_time")
            if p_start_time is not None:
                sql_set_list.append("p_start_time = %(p_start_time)s")
                params["p_start_time"] = p_start_time

            p_end_time = kwargs.get("p_end_time")
            if p_end_time is not None:
                sql_set_list.append("p_end_time = %(p_end_time)s")
                params["p_end_time"] = p_end_time

            pic_name = kwargs.get("pic_name")
            if pic_name is not None:
                sql_set_list.append("pic_name = %(pic_name)s")
                params["pic_name"] = pic_name

            pic_email = kwargs.get("pic_email")
            if pic_email is not None:
                sql_set_list.append("pic_email = %(pic_email)s")
                params["pic_email"] = pic_email

            sample_information = kwargs.get("sample_information")
            if sample_information is not None:
                sql_set_list.append("sample_information = %(sample_information)s")
                params["sample_information"] = sample_information

            status = kwargs.get("status")
            if status is not None:
                sql_set_list.append("status = %(status)s")
                params["status"] = status

            result = kwargs.get("result")
            if result is not None:
                sql_set_list.append("result = %(result)s")
                params["result"] = result

            result_two = kwargs.get("result_two")
            if result_two is not None:
                sql_set_list.append("result_two = %(result_two)s")
                params["result_two"] = result_two

            m_version = kwargs.get("m_version")
            if m_version is not None:
                sql_set_list.append("m_version = %(m_version)s")
                params["m_version"] = m_version

            sub_versions = kwargs.get("sub_versions")
            if sub_versions is not None:
                sql_set_list.append("sub_versions = %(sub_versions)s")
                params["sub_versions"] = sub_versions

            product_version = kwargs.get("product_version")
            if product_version is not None:
                sql_set_list.append("product_version = %(product_version)s")
                params["product_version"] = product_version

            if "test_product_id" in kwargs:
                test_product_id = kwargs.get("test_product_id")
                sql_set_list.append("test_product_id = %(test_product_id)s")
                params["test_product_id"] = test_product_id

            # test_cases = kwargs.get("test_cases")
            # if test_cases is not None:
            #     sql_set_list.append("test_cases = %(test_cases)s")
            #     params["test_cases"] = json.dumps([TestCaseModel().retrieve(i) for i in test_cases])

            test_case_extra_args = kwargs.get("test_case_extra_args")
            if test_case_extra_args is not None:
                sql_set_list.append("test_case_extra_args = %(test_case_extra_args)s")
                params["test_case_extra_args"] = test_case_extra_args

            if not sql_set_list:
                return

            sql_set = ", ".join(sql_set_list)

            sql = """
                UPDATE {table_name}     
                    SET 
                    {sql_set},
                    update_time = %(update_time)s
                    WHERE id = %(id)s;
            """.format(
                table_name=self.table_name,
                sql_set=sql_set,
            )
            sql_execute(sql, params)

    def copy(self, tp, user, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            params = {
                "name": kwargs.get("name"),
                "desc": kwargs.get("desc"),
                "plan_use": kwargs.get("plan_use"),
                "plan_type": kwargs.get("plan_type"),
                "test_type": kwargs.get("test_type"),
                "test_type_name": kwargs.get("test_type_name"),
                "p_start_time": kwargs.get("p_start_time"),
                "p_end_time": kwargs.get("p_end_time"),
                "pic_name": kwargs.get("pic_name"),
                "pic_email": kwargs.get("pic_email"),
                "sample_information": kwargs.get("sample_information"),
                "status": "DEBUGGING",
                "test_cases": json.dumps(tp.get("test_cases")),
                "test_case_extra_args": json.dumps(tp.get("test_case_extra_args")),
                "m_version": kwargs.get("m_version"),
                "sub_versions": kwargs.get("sub_versions"),
                "product_version": kwargs.get("product_version"),
                "creator_name": user.username,
                "creator_email": user.email,
                "project_name": kwargs.get("project_name"),
                "project_number": kwargs.get("project_number"),
                "project_id": kwargs.get("project_id"),
                "abnormal_stop": kwargs.get("abnormal_stop"),
                "finish_notice": kwargs.get("finish_notice"),
                "create_time": now,
                "update_time": now,
            }

            sql = """
                INSERT INTO {table_name} ("name", "desc", test_type, test_type_name, plan_use, plan_type, p_start_time, 
                p_end_time, pic_name, pic_email, sample_information, status,
                test_cases, test_case_extra_args, abnormal_stop, finish_notice, creator_name, creator_email, project_name,
                project_number, project_id, m_version, sub_versions, product_version, create_time, update_time) 
                VALUES (%(name)s, %(desc)s, %(test_type)s, %(test_type_name)s, %(plan_use)s, %(plan_type)s, %(p_start_time)s, 
                    %(p_end_time)s, %(pic_name)s, %(pic_email)s, %(sample_information)s, %(status)s,
                    %(test_cases)s, %(test_case_extra_args)s, %(abnormal_stop)s, %(finish_notice)s,
                    %(creator_name)s, %(creator_email)s, %(project_name)s, %(project_number)s, %(project_id)s, 
                    %(m_version)s, %(sub_versions)s, %(product_version)s, %(create_time)s, %(update_time)s)
                RETURNING id;
                ;
            """.format(table_name=self.table_name)

            r = sql_fetchone_dict(sql, params)
            tp_id = r.get("id")

            plan_create.send(
                self.__class__,
                project_number=kwargs.get("project_number"),
                version_name=kwargs.get("m_version")
            )

            sub_versions = json.loads(kwargs.get("sub_versions"))
            for i in sub_versions:
                plan_create.send(
                    self.__class__,
                    project_number=kwargs.get("project_number"),
                    version_name=i.get("name")
                )

            return tp_id


class TestCaseResultModel:
    def __init__(self):
        self.table_name = "public.test_case_result"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            file = kwargs.get("file")
            if file:
                prefix = str(uuid.uuid4())
                dir_name = f"{now.year}_{now.month}"
                pathname = at_file_fs.save(os.path.join(dir_name, prefix + file.name), file)

                at_file = AtFile.objects.create(
                    pathname=at_file_fs.path(pathname),
                    relative_pathname=pathname,
                    title=file.name,
                    extension=file.name.split(".")[-1],
                    size=file.size,
                    added_by=kwargs.get("creator_name"),
                    added_date=now,
                )

            params = {
                "test_plan_id": kwargs.get("test_plan_id"),
                "test_case_id": kwargs.get("test_case_id"),
                "test_case_version": kwargs.get("test_case_version"),
                "exec_id": kwargs.get("exec_id"),
                "result": kwargs.get("result"),
                "result_two": kwargs.get("result_two"),
                "value": kwargs.get("value"),
                "remark": kwargs.get("remark"),
                "generation_mode": kwargs.get("generation_mode"),
                "creator_name": kwargs.get("creator_name"),
                "creator_email": kwargs.get("creator_email"),
                "create_time": now,
                "update_time": now,
            }

            sql = """
                INSERT INTO {table_name} (test_plan_id, test_case_id, test_case_version, exec_id, result, result_two,"value", remark, 
                generation_mode, creator_name, creator_email, create_time, update_time)
                VALUES (%(test_plan_id)s, %(test_case_id)s, %(test_case_version)s, %(exec_id)s, %(result)s, %(result_two)s, %(value)s, 
                    %(remark)s, %(generation_mode)s, %(creator_name)s, %(creator_email)s, %(create_time)s, %(update_time)s)
                ON CONFLICT (test_plan_id, test_case_id)
                DO UPDATE SET test_case_version = EXCLUDED.test_case_version, exec_id = EXCLUDED.exec_id,
                    result = EXCLUDED.result, result_two = EXCLUDED.result_two, "value" = EXCLUDED."value", remark = EXCLUDED.remark,
                    generation_mode = EXCLUDED.generation_mode, creator_name = EXCLUDED.creator_name, 
                    creator_email = EXCLUDED.creator_email, update_time = EXCLUDED.update_time
                RETURNING id;
                ;
            """.format(table_name=self.table_name)

            id_ = sql_fetchone(sql, params)[0]

            if file:
                old_at_file = AtFile.objects.filter(object_type="test_case_result", object_id=id_,
                                                    deleted=False).first()
                if old_at_file:
                    at_file_fs.delete(old_at_file.relative_pathname)
                    old_at_file.deleted = True
                    old_at_file.save()

                at_file.object_type = "test_case_result"
                at_file.object_id = id_
                at_file.save()

                sql = """
                    UPDATE {table_name}
                        SET file = %(file)s
                        WHERE id = %(id)s;
                """.format(table_name=self.table_name)
                sql_execute(sql, {"id": id_, "file": str(at_file.id)})
