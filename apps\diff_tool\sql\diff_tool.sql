CREATE TABLE IF NOT EXISTS public.diff_tool_usage_records(
    id serial primary key, --id
    project_name varchar(255),
    project_number varchar(255),
    input_file_type varchar(255),
    old_package_name VA<PERSON>HAR(255),
    old_package_version VARCHAR(255),
    old_package_md5 VARCHAR(255),
    new_package_name VA<PERSON><PERSON><PERSON>(255),
    new_package_version VARCHAR(255),
    new_package_md5 VARCHAR(255),
    diff_package_name VARCHAR(255),
    diff_package_md5 VARCHAR(255),
    diff_package_path VARCHAR(255),
    diff_package_version VARCHAR(255),
    diff_package_status int default 0, -- 0 未验证 1 验证通过 2 验证不通过
    operator_name VARCHAR(255),
    operator_email VARCHAR(255),
    create_time timestamp --创建时间
);
ALTER TABLE public.diff_tool_usage_records OWNER TO atpms;


CREATE TABLE IF NOT EXISTS public.diff_package_share_urls(
    id serial primary key, --id
    diff_record_id int,
    token varchar(255),
    expiration_time int,
    creator_name <PERSON><PERSON><PERSON><PERSON>(255),
    creator_email VARCHAR(255),
    create_time timestamp --创建时间
);
ALTER TABLE public.diff_package_share_urls OWNER TO atpms;