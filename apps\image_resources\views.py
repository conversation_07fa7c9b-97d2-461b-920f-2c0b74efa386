import logging
import os
import traceback
import uuid

from django.conf import settings
from django.core.files.storage import FileSystemStorage
from django.core.paginator import Paginator
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication

from .ImageResource import ImageResource
from .serializers import (
    ImageResourceListSerializer, ImageResourceSerializer, ImageResourceUpdateSerializer
)

logger = logging.getLogger("image_resources")

image_resource_fs = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, "image_resources"),
                                      base_url="/media/image_resources")


class ImageResourceView(APIView):
    # 登录认证【如果是调试模式可以屏蔽以下代码】
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = ImageResourceListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            page = serializer.validated_data.get("page", 1)
            pagesize = serializer.validated_data.get("pagesize", 10)
            name_re = serializer.validated_data.get("name_re")

            obj_list = ImageResource.objects.all().reverse()

            if name_re is not None:
                obj_list = obj_list.filter(name__iregex=name_re)

            obj_list = obj_list.order_by("-id")

            paginator = Paginator(obj_list, pagesize)

            page_obj = paginator.get_page(page)

            results = list(page_obj.object_list.values())

            for i in results:
                i["image"] = image_resource_fs.url(i["image"])
                i["thumbnail"] = image_resource_fs.url(i["thumbnail"])

            content = {
                "count": paginator.count,
                "results": results
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            # 指定人员才有权限增加图片资源数据
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            serializer = ImageResourceSerializer(data=request.data)

            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            image_type = serializer.validated_data.get("type")
            image_path, thumbnail_image_path, index, rgb = "", "", 0, ""
            if image_type == 0:
                image = serializer.validated_data.get("image")
                if image:
                    dir_name = str(uuid.uuid4())
                    image_path = image_resource_fs.save(os.path.join(dir_name, image.name), image)
                    thumbnail_image_path = os.path.join(dir_name, f"thumbnail_{image.name}")
                    # 生成缩略图
                    ImageResource.generate_thumbnail(image_resource_fs.path(image_path),
                                                     image_resource_fs.path(thumbnail_image_path))
                    image_resource = ImageResource.objects.filter(type=0).last()
                    if image_resource is not None:
                        index = image_resource.index + 1
            elif image_type == 1:
                rgb = serializer.validated_data.get("rgb")

            ImageResource.objects.create(
                name=serializer.validated_data.get("name"),
                type=serializer.validated_data.get("type"),
                image=image_path,
                thumbnail=thumbnail_image_path,
                index=index,
                rgb=rgb,
                desc=serializer.validated_data.get("desc")
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ImageResourceDetailView(APIView):
    # 登录认证【如果是调试模式可以屏蔽以下代码】
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            obj = ImageResource.objects.get(id=pk)
            return Response({"err_code": 0, "data": obj.to_dict()}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            serializer = ImageResourceUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            name = serializer.validated_data.get("name")
            desc = serializer.validated_data.get("desc")

            obj = ImageResource.objects.get(id=pk)

            obj.name = name
            obj.desc = desc

            obj.save()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            result = ImageResource.objects.get(id=pk)
            result.delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
