import datetime
import logging

from django.db import models
from django.db import transaction
from django.db.models import Text<PERSON>ield

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict

logger = logging.getLogger("machines")


class StressTestModel(models.Model):
    verification = TextField(null=True, blank=True, verbose_name=u'描述')
    project_name = models.CharField(max_length=255, null=True, blank=True, verbose_name=u'名称')  # 添加 project_name 字段
    project_number = models.CharField(max_length=255, null=True, blank=True,
                                      verbose_name=u'项目编号')  # 添加 project_number 字段
    status = models.IntegerField(null=True, blank=True, verbose_name=u'问题状态')

    class Meta:
        managed = False
        app_label = 'stress_tests'
        db_table = 'stress_test'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.table_name = "public.stress_test"  # 定义 table_name 属性

    def create(self, **kwargs):
        now = datetime.datetime.now()

        params = {
            "project_name": kwargs.get("project_name"),
            "project_number": kwargs.get("project_number"),
            "verification": kwargs.get("verification"),
            "status": kwargs.get("status"),
            "create_time": now,
            "update_time": now,
        }

        sql = """
            INSERT INTO {table_name} ("project_name", "project_number", verification, status, create_time, update_time)
            VALUES (%(project_name)s, %(project_number)s, %(verification)s, %(status)s, %(create_time)s, %(update_time)s)
            RETURNING id;
        """.format(table_name=self.table_name)

        sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        project_name = kwargs.get("project_name")
        if project_name is not None and project_name != '':
            sql_where_list.append("project_name = %(project_name)s")
            params["project_name"] = project_name

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        verification = kwargs.get("verification")
        if verification is not None and verification != '':
            sql_where_list.append("verification = %(verification)s")
            params["verification"] = verification

        status = kwargs.get("status")
        if status is not None:
            sql_where_list.append("status = %(status)s")
            params["status"] = status

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY update_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} 
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id,"project_name", project_number, verification, status, create_time, update_time
            FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, "project_name", project_number, verification, status, create_time, update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def update(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()
            params = {
                "id": kwargs.get("pk"),
                "update_time": now,
            }

            sql_set_list = []

            project_name = kwargs.get("project_name")
            if project_name is not None:
                sql_set_list.append("project_name = %(project_name)s")
                params["project_name"] = project_name

            project_number = kwargs.get("project_number")
            if project_number is not None:
                sql_set_list.append("project_number = %(project_number)s")
                params["project_number"] = project_number

            verification = kwargs.get("verification")
            if verification is not None:
                sql_set_list.append("verification = %(verification)s")
                params["verification"] = verification

            status = kwargs.get("status")
            if status is not None:
                sql_set_list.append("status = %(status)s")
                params["status"] = status

            if not sql_set_list:
                return

            sql_set = ", ".join(sql_set_list)

            sql = """
                UPDATE {table_name}     
                    SET 
                    {sql_set},
                    update_time = %(update_time)s
                    WHERE id = %(id)s;
            """.format(
                table_name=self.table_name,
                sql_set=sql_set,
            )
            sql_execute(sql, params)

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result
