#!/bin/bash

INI_FILE="uwsgi.ini"
LOG_FILE="uwsgi.log"  
WAIT_TIME=2          

# 检查工具文件的执行权限
HDIFFZ_PATH="apps/diff_tool/h56c_diff_tool/linux/hdiffz"
PACKER_PATH="apps/pack_tool/tools/v1/packer/linux/packer"

echo "检查工具文件权限..."
# 尝试设置权限，但忽略错误
chmod a+x "$HDIFFZ_PATH" 2>/dev/null
chmod a+x "$PACKER_PATH" 2>/dev/null

# 检查权限是否设置成功
if [ ! -x "$HDIFFZ_PATH" ] || [ ! -x "$PACKER_PATH" ]; then
    echo "⚠️ 注意: 无法设置工具文件执行权限"
    echo "这可能需要管理员权限。请联系系统管理员执行以下命令:"
    echo "chmod a+x $HDIFFZ_PATH"
    echo "chmod a+x $PACKER_PATH"
    echo "继续重启uwsgi..."
else
    echo "✅ 工具文件权限正常"
fi

echo "正在强制结束所有 uwsgi 进程..."
killall -9 uwsgi 2>/dev/null
sleep $WAIT_TIME
echo "uwsgi 进程已结束。"

echo "正在启动 uwsgi..."
uwsgi --ini "$INI_FILE"
sleep $WAIT_TIME

if pgrep -x uwsgi >/dev/null; then
    echo "✅ uwsgi 启动成功！"
else
    echo "❌ uwsgi 启动失败，请检查日志。"
    if [ -f "$LOG_FILE" ]; then
        echo "以下是 $LOG_FILE 最后 20 行日志："
        tail -n 20 "$LOG_FILE"
    fi
fi 