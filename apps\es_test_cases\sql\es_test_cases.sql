--蔚来测试用例
CREATE TABLE IF NOT EXISTS public.nio_test_cases(
    id VARCHAR(255) primary key, --测试用例编号
    test_category VARCHAR(255), --测试类别
    "name" VARCHAR(255), --测试项目名称
    test_steps TEXT, --测试步骤
    expected_result TEXT, --期望结果
    priority VARCHAR(50), --优先级
    software_iteration VARCHAR(50), --应用软件迭代
    config_changes VARCHAR(50), --配置变化
    boot_iteration VARCHAR(50), --Boot迭代
    hardware_iteration VARCHAR(50), --硬件迭代
    test_result VARCHAR(50), --测试结果
    test_data TEXT,  --测试数据
    remarks TEXT, --备注
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.nio_test_cases OWNER TO atpms;

--岚图测试用例
CREATE TABLE IF NOT EXISTS public.voyah_test_cases(
    id VARCHAR(255) primary key, --测试用例编号
    module_category VARCHAR(255), --模块类别
    test_category VARCHAR(255), --测试类别
    "name" VARCHAR(255), --测试项目名称
    precondition TEXT, --前置条件
    test_steps TEXT, --测试步骤
    expected_result TEXT, --期望结果
    test_purpose TEXT, --测试目的
    test_result VARCHAR(50), --测试结果
    test_data TEXT,  --测试数据
    remarks TEXT, --备注
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.voyah_test_cases OWNER TO atpms;

