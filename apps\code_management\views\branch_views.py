import logging
import traceback
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db import transaction
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.cache import cache
from django.core.serializers import serialize
import json
import os
import shutil
import time
from pathlib import Path

logger = logging.getLogger("code_management")
from apps.code_management.grpc.host_machine_grpc import host_machine_client
from apps.code_management.grpc.git_grpc import git_client
from apps.code_management.models.project_models import ProjectInfoSerializer, CodeProjectInfo, CodeBranchInfo, BranchInfoSerializer
from apps.code_management.models.config_models import NodeTree, NodeTreeSerializer
from users.models import UserFSInfo
from utils.fs_service import FSService
from utils.fs_app import fs_app


local_path = "/home/<USER>/hwcp"
config_path = "/home/<USER>/hwcp/gRPC_Server/HwcpTest"
config_url = "http://*********/python-team/HwcpTest.git"
config_branch = "main"
sdk_git_path = "http://*********/"


def serch_branch(project_code, project_gitlab, user):
    """
        根据项目好和gitlab仓库查询分支信息
    """
    # 存储数据
    person_brnach = f'dev_{user}'
    logger.info("当前创建人的分支 person_brnach：%s", person_brnach)
    branch_data = list()
    # branch_data.append(person_brnach)
    if project_code and project_gitlab:
        projects = CodeBranchInfo.objects.filter(
            project_code=project_code,
            project_gitlab=project_gitlab
        )
        logger.info("查询出的数据：%s", projects)
        if projects:
            for project in projects:
                branch_data.append(project.project_branch)
    if person_brnach in branch_data:
        branch_data.remove(person_brnach)
        branch_data.insert(0, person_brnach)
        logger.info("查询出的数据 branch_data：%s", branch_data)
    else:
        logger.info("当前创建人的分支 + 查询出的数据 branch_data：%s", branch_data)
        branch_data.insert(0, person_brnach)
        logger.info("插入当前创建人的分支：%s", branch_data)
    branch_data.append("dev")
    logger.info("插入 dev 分支：%s", branch_data)
    return branch_data



class ProjectSpaceView(APIView):



    # 查询仓库信息
    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)
            project_code = params.get("project_code")
            # 存储数据
            table_data = []
            # 校验值是否存在
            if "group" in params:
                group = params["group"]
                logger.info("project_code: %s, group: %s", project_code, group)
                if project_code and group:
                    projects = CodeProjectInfo.objects.filter(project_code=project_code, project_group=group)
                    logger.info("查询出的数据：%s", projects)

                    for project in projects:
                        if project.chip:
                            table_data.append(project.project_gitlab)
                    logger.info("查询出的数据 table_data：%s", table_data)
            else:
                logger.info("params 中不包含 group 参数")
                if project_code:
                    projects = CodeProjectInfo.objects.filter(project_code=project_code)
                    logger.info("查询出的数据：%s", projects)
                    for project in projects:
                        if project.chip:
                            table_data.append(project.project_gitlab)
                    logger.info("查询出的数据 table_data：%s", table_data)

            return Response({"status": 1, "message": "查询操作成功", "space_options": table_data})
        except Exception as e:
            logger.info(f"查询数据失败: {str(e)}")
            return Response({"status": 0, "message": f"查询操作失败:{str(e)}"})


class ProjectBranchView(APIView):
    """
        查询数据库中固定项目和仓库的分支信息
    """

    # 人员认证
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)
            project_code = params.get("project_code")

            if "group" in params and "space" in params:
                group = params.get("group")
                space = params.get("space")
                # 拼接project_gitlab
                project_gitlab = f"{sdk_git_path}{group}/{space}"
                logger.info("project_code:%s, group: %s, project_gitlab:%s", project_code, group, project_gitlab)

            else:
                logger.info("params 中不包含 group 和 space 参数")
                project_gitlab = params.get("project_gitlab")
                logger.info("project_code:%s,  project_gitlab:%s", project_code, project_gitlab)
            # 获取用户
            useremail = request.user.email if request.user.is_authenticated else 'anonymous'
            user = str(useremail).split("@")[0]
            logger.info("user:%s", user)
            branch_data = serch_branch(project_code, project_gitlab, user)
            return Response({"status": 1, "message": "查询操作成功", "branch_options": branch_data})

        except Exception as e:
            logger.info("查询数据失败: %s", str(e))
            return Response({"status": 0, "message": f"查询操作失败:{str(e)}"})


class BranchSubmitView(APIView):

    """
        创建分支
        创建分支信息表信息
        校验用户信息
        创建服务器工作目录
        获取配置详情页左侧目录树
    """
    # 人员认证
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get_function(self, data):
        """
            处理数据， 加载左侧目录数据
        """
        func = []
        pick_chips = []
        config_info = {}
        data = json.loads(data)
        for module_name, module_config in data["modules"].items():
            func.append(module_name)

            # 提取文件列表或路径
            files_data = module_config.get("files", [])
            if all(isinstance(item, str) for item in files_data):
                config_info[module_name] = files_data
            else:
                paths = [item.get("path") for item in files_data if isinstance(item, dict) and "path" in item]
                config_info[module_name] = paths

            # 提取 enable_config 中的 status
            enable_config = module_config.get("enable_config", {})
            if enable_config.get("status") == "STD_ON":
                pick_chips.append(module_name)

        return func, pick_chips, config_info



    def create_workspace_dir(self, path):
        """
            创建服务器工作目录
        """
        try:
            # 先检查目录是否已存在
            if os.path.exists(path):
                if os.path.isdir(path):
                    logger.info(f"工作目录已存在: {path}")
                    return True
                else:
                    # 路径存在但不是目录，记录错误
                    logger.info(f"路径已存在但不是目录: {path}")
                    return False

            # 目录不存在，创建它
            os.makedirs(path)
            logger.info(f"工作目录创建成功: {path}")
            return True
        except Exception as e:
            logger.info(f"创建工作目录失败:%s", e)
            return False


    def get_user(self, project_gitlab, project_branch):
        """
            获取数据库中固定分支的创建用户信息
        """
        logger.info("project_gitlab: %s, project_branch:%s", project_gitlab, project_branch)

        # 获取指定工作组下所有的配置信息
        projects = CodeBranchInfo.objects.filter(project_gitlab=project_gitlab, project_branch=project_branch)
        logger.info("在分支信息表中查询出的数据: %s", projects)

        # 提取所有创建人的用户名，并去重
        config_user = projects.values_list('create_person', flat=True).distinct()
        logger.info("在分支信息表中查询出的用户: %s", config_user)

        return config_user.first()


    def call_push_config(self, sdk_path, chip_type, functions, workspace_path, branch_status):
        """
            调用上位机服务，获取配置详情左侧目录原始数据
        """
        try:
            # queue_function = '&'.join(functions)
            logger.info("sdk_path:%s, chip_type:%s, functions:%s, workspace_path:%s, branch_status:%s", sdk_path, chip_type, functions, workspace_path, branch_status)
            # ConfigChip_response = host_machine_client.push_configChipFunctions_serve(sdk_path, chip_type, functions, workspace_path, branch_status)
            # logger.info("调用上位机的ConfigChipFunctions服务: %s", ConfigChip_response)
            # if ConfigChip_response.config_status != 1:
            #     raise ValueError("上位机 ConfigChipFunctionsParams 配置失败")
            # else:
            #     # logger.info("模块解析完成: project_json_tree=%s", ConfigChip_response.project_tree_data)
            #     func, pick_chips, config_info = self.get_function(ConfigChip_response.project_tree_data)
            #     logger.info("config_info 解析完成: config_info=%s", config_info)
            #     pick_tree_info = [
            #         {"label": key, "children": [{"label": item} for item in config_info.get(key, [])]}
            #         for key in functions
            #     ]

            pick_tree_info = []

            # 查询一级模块（type='module'）
            modules = NodeTree.objects.filter(
                type='module',
                name__in=functions
            ).values('parent_id', 'name', 'level', 'titles')

            for module in modules:
                module_id = module['parent_id']

                # 查询该模块下的配置项（type='var'）
                config_items = NodeTree.objects.filter(
                    type='var',
                    parent_id=module_id
                ).values('parent_id', 'name', 'level', 'titles')

                children = []
                for config in config_items:
                    config_id = config['parent_id']

                    # 查询配置项下的子项（type='func'）
                    sub_items = NodeTree.objects.filter(
                        parent_id=config_id,
                        type='func'
                    ).values('name', 'level', 'titles')

                    # 只保留 level 前缀匹配的子项（例如 config=12.1，sub=12.1.1）
                    filtered_sub_items = [
                        {
                            'label': sub['name'],
                            'level': sub['level'],
                            'titles': sub['titles']
                        }
                        for sub in sub_items
                        if sub['level'].startswith(config['level'] + '.')
                    ]

                    config_entry = {
                        'label': config['name'],
                        'level': config['level'],
                        'titles': config['titles'],
                        'children': filtered_sub_items
                    }
                    children.append(config_entry)

                pick_tree_info.append({
                    'label': module['name'],
                    'level': module['level'],
                    'titles': module['titles'],
                    'children': children
                })

            return pick_tree_info
        except Exception as e:
            logger.info("失败: %s", str(e))
            return []


    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)
            project_code = params.get("project_code")
            project_name = params.get("project_name")
            project_gitlab = params.get("project_gitlab")
            project_branch = params.get("project_branch")
            if project_branch in ['release', 'main', 'master']:
                raise ValueError("该分支数据不支持创建，请重新填写要创建的分支名称")

            logger.info("project_gitlab:%s", project_gitlab)
            workgroup = str(project_gitlab).split(sdk_git_path)[-1].split('/')[0]
            workspace = str(project_gitlab).split(workgroup)[-1].strip("/")
            logger.info("workgroup:%s, workspace:%s", workgroup, workspace)

            # 获取用户
            username = request.user.username if request.user.is_authenticated else 'anonymous'
            logger.info("username:%s", username)

            # 分支固定路径
            workspace_path = f"{local_path}/{project_code}/{workgroup}/{workspace}/{project_branch}"
            logger.info("workspace_path:%s", workspace_path)

            # 创建仓库后，在服务器创建对应工作目录
            result = self.create_workspace_dir(workspace_path)
            logger.info("workspace_path: %s, result: %s ", workspace_path, result)
            # 本地
            if result:
                # 查寻仓库表中芯片和功能模块信息
                CodeProject_projects = CodeProjectInfo.objects.filter(
                    project_code=project_code,
                    project_gitlab=project_gitlab
                )
                if not CodeProject_projects.exists():
                    raise ValueError("未找到项目配置数据")
                logger.info("仓库信息表查询数据成功")
                chip_project = CodeProject_projects.first()
                chip_type = chip_project.chip
                functions = chip_project.modules
                logger.info("chip_type:%s, functions:%s", chip_type, functions)

                # 动态仓库固定路径
                sdk_path = f"{local_path}/{project_code}/{workgroup}/{workspace}/hiwaysdk_2.0/Application"
                logger.info("sdk_path：%s", sdk_path)
                branch_space = f"{local_path}/{project_code}/{workgroup}/{workspace}/{project_branch}"
                logger.info("branch_space：%s", branch_space)

                # 插入数据
                # 保存或更新 ProjectInfo
                projects = CodeBranchInfo.objects.filter(
                    project_code=project_code,
                    project_gitlab=project_gitlab,
                    project_branch=project_branch
                )

                if projects.exists():
                    logger.info("当前分支在数据中已存在")
                    branch_info_status = 0
                else:
                    branch_info_status = 1
                logger.info("当branch_info_status: %s", branch_info_status)
                # 服务第工作空间地址
                full_project_name = f"{workgroup}/{workspace}"
                logger.info("send git_grpc create_project_serve full_project_name:%s , project_branch: %s",
                            full_project_name, project_branch)
                # 创建分支
                create_branch_response = git_client.create_branch_serve(full_project_name, project_branch)
                logger.info("git_grpc create_branch_serve response: %s", create_branch_response)
                # 检查创建项目与创建分支的返回状态
                if create_branch_response.code == 409:
                    # 当前分支在gitlab上已被创建, 且数据库有数据
                    if branch_info_status == 0:
                        logger.info("当前分支是历史建立分支，分支信息表中存在其数据")
                        # 项目和分支都已存在
                        branch_status = "0"

                    else:
                        if project_branch == "dev":
                            branch_status = "0"
                            logger.info("当前分支是dev，创建仓库时默认自带")
                        else:
                            logger.info("当前分支是历史建立分支，分支信息表中不存在其数据")
                            raise ValueError("当前分支在gitlab被手动创建，请重新创建分支")

                elif create_branch_response.code == 200:
                    # 当前分支在gitlab上已被创建, 且数据库无数据
                    if branch_info_status == 1:
                        logger.info("当前分支是新建分支，即将把信息写入分支信息表中")
                        # 插入数据库
                        branch_info = {
                            "project_code": project_code,
                            "project_name": project_name,
                            "project_gitlab": project_gitlab,
                            "project_branch": project_branch,
                            "chip": chip_type,
                            "modules": functions,
                            "branch_space": branch_space,
                            "project_group": workgroup,
                            "create_person": username
                        }
                        logger.info("要插入分支信息表的数据：%s", branch_info)
                        serializer = BranchInfoSerializer(data=branch_info)
                        if not serializer.is_valid():
                            raise ValueError("数据错误: " + str(serializer.errors))
                        serializer.save()

                        logger.info("分支信息表写入成功")
                        # 表示新建分支状体
                        branch_status = "1"
                        # clone 对应分支的 仓库 到 指定目录
                        logger.info("git_grpc clone_serve project_gitlab: %s, workspace_path: %s, "
                                    "project_branch: %s", project_gitlab, workspace_path, project_branch)
                        clone_branch_response = git_client.clone_serve(project_gitlab, workspace_path, project_branch)
                        logger.info("git_grpc clone_serve response: %s", clone_branch_response)

                    else:
                        logger.info("当前分支是新建立分支，分支信息表中存在其数据")
                        raise ValueError(f"当前分支在gitlab上被手动删除，请重新创建分支")
                else:
                    raise ValueError("仓库分支创建失败")

                # 获取项目配置树
                pick_tree_info = self.call_push_config(sdk_path, chip_type, functions, workspace_path, branch_status)
                logger.info("pick_tree_info: %s", pick_tree_info)
                if not pick_tree_info:
                    raise ValueError("获取树目录异常，请检查node_tree数据")

                # 查询gitlab地址
                if project_branch != "dev":
                    branch_info = CodeBranchInfo.objects.get(branch_space=workspace_path)
                    project_gitlab = branch_info.project_gitlab
                    # 查询sdk version
                    project_info = CodeProjectInfo.objects.get(project_gitlab=project_gitlab)
                    sdk_version = project_info.sdk_version
                    logger.info("sdk_version:%s", sdk_version)
                    # 查询数据库信息，获取username
                    config_user = self.get_user(project_gitlab, project_branch)
                    logger.info("当前仓库分支得创建人: %s", config_user)

                    # 校验用户信息
                    # 测试临时注释
                    logger.info("当前用户: %s", username)
                    if username != config_user:
                        branch_create = "false"
                    else:
                        branch_create = "true"
                    #     raise ValueError("不是当前仓库分支的创建人，请重新选择")
                else:
                    workspace_path = ""
                    sdk_version = "dev"
                    branch_create = "false"
                return Response({
                    "config_status": 1,
                    "message": "配置成功",
                    "pick_tree_info": pick_tree_info,
                    "work_space": workspace_path,
                    "branch_status": branch_status,
                    "sdk_version": sdk_version,
                    "branch_create": branch_create
                })

            else:
                raise ValueError("请检查project_gitlab")
        except Exception as e:
            logger.info("失败: %s", str(e))
            return Response({"config_status": 0, "message": f"操作失败:{str(e)}"})


