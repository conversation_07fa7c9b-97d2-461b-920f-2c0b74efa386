import datetime
import os

import requests
from jproperties import Properties

from utils.nextcloud import NextCloud

NEXTCLOUD_USERNAME = 'automated_test_ftp1'
NEXTCLOUD_PASSWORD = 'Xu4rYMCw5E'
NEXTCLOUD_UID = 'D6A25366-9A66-4BA4-8AB9-AB918E763DC7'
# NEXTCLOUD_URL = 'https://ecm.hiwaytech.com/remote.php/dav/files/13CB0352-2A54-47C9-BCDE-F9C52A331E85'
NEXTCLOUD_URL = 'https://ecmbeta.hiwaytech.com'
JENKINS_ROOT = 'jenkins_release'
to_js = True
AUTOTEST_ROOT = 'automated_test'

HW_PLATFORM = 'http://10.1.1.132:8010/project/version/public/saveVersion'
# HW_PLATFORM = 'http://10.1.1.146:8010/project/version/public/saveVersion'


cloud = NextCloud(endpoint=NEXTCLOUD_URL, user=NEXTCLOUD_USERNAME, password=NEXTCLOUD_PASSWORD, json_output=to_js)


def upload_dist_to_cloud(project, software_type, folder, dist, qac):
    """
    上传编译产物和QAC报告到Nextcloud平台
    :param project:
    :param software_type:
    :param folder:
    :param dist:
    :param qac:
    :return:
    """
    status = True
    try:
        if not cloud.check(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}'):
            cloud.mkdir(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}')

        if not cloud.check(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{software_type}'):
            temp = software_type.split('/')
            if len(temp) == 1:
                cloud.mkdir(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{software_type}')
            else:
                cloud.mkdir(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{temp[0]}')
                cloud.mkdir(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{temp[0]}/{temp[1]}')

        if not cloud.check(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{software_type}/{folder}'):
            cloud.mkdir(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{software_type}/{folder}')

        if cloud.check(NEXTCLOUD_UID, f'{JENKINS_ROOT}/{project}/{software_type}/{folder}'):
            if os.path.exists(dist):
                name = os.path.basename(dist)
                cloud.upload(NEXTCLOUD_UID, dist, f'{JENKINS_ROOT}/{project}/{software_type}/{folder}/{name}')
                print(
                    f'Version Files Upload File Success: {NEXTCLOUD_URL}/{JENKINS_ROOT}/{project}/{software_type}/{folder}/{name}')

                if qac is not None and os.path.exists(qac):
                    cloud.upload(NEXTCLOUD_UID, qac, f'{JENKINS_ROOT}/{project}/{software_type}/{folder}/qac_reports')
                    print(
                        f'QAC Report Upload File Success: {NEXTCLOUD_URL}/{JENKINS_ROOT}/{project}/{software_type}/{folder}/qac_reports')
        else:
            status = False
    except Exception as e:
        status = False
        print(e.args)

    return status


def create_cloud_share(project, software_type, folder):
    """
    创建cloud平台的分享链接
    :param project:
    :param software_type:
    :param folder:
    :return:
    """
    response = cloud.create_share(f'{JENKINS_ROOT}/{project}/{software_type}/{folder}', 3)
    print(f'shared:{JENKINS_ROOT}/{project}/{software_type}/{folder}, response: {response}')
    if response.is_ok:
        share_url = response.data['url']
    else:
        # 默认是 jenkins_release 的目录
        share_url = 'https://ecm.hiwaytech.com/f/1042881'
    print(f'shared url: {share_url}')
    return share_url


def upload_version_to_hw(project, version, software_type, share):
    """
    将版本信息推送到产品开发平台
    :param project:
    :param version:
    :param software_type:
    :param share:
    :return:
    """
    configs = Properties()
    with open(version, 'rb') as config_file:
        configs.load(config_file)

    if software_type in ('MCU', 'MCU/Master', 'MCU/Slave'):
        desc = f'''
            发布日期：{datetime.datetime.today().strftime('%Y-%m-%d %H:%M:%S')}
            软件版本：{configs.get('MCU_SOFTWARE_VERSION').data}
            默认硬件版本：{configs.get('MCU_HARDWARE_VERSION').data}
            Boot版本：{configs.get('MCU_BOOT_VERSION').data}
            内部软件版本：{configs.get('MCU_SOFTWARE_IN_VERSION').data}
            其它版本：{configs.get('MCU_Other_VERSION').data}
            版本路径：{share}
        '''
        data = {
            'projectCode': f'{project}',
            'type': 'SOFTWARE',
            'property': 'RELEASE',
            'num': f"{configs.get('MCU_Folder_Name').data}",
            'description': desc
        }
    elif software_type == 'HMI':
        desc = f'''
            发布日期：{datetime.datetime.today().strftime('%Y-%m-%d %H:%M:%S')}
            软件版本：{configs.get('APP_VERSION').data}
            版本路径：{share}
        '''
        data = {
            'projectCode': f'{project}',
            'type': 'SOFTWARE',
            'property': 'RELEASE',
            'num': f"{configs.get('APP_VERSION').data}",
            'description': desc
        }
    elif software_type in ('VDS/OS', 'VDS/MCU', 'FCT/OS', 'OS'):
        desc = f'''
            发布日期：{datetime.datetime.today().strftime('%Y-%m-%d %H:%M:%S')}
            OS版本：{configs.get('OS_VERSION').data}
            版本路径：{share}
        '''
        data = {
            'projectCode': f'{project}',
            'type': 'SOFTWARE',
            'property': 'RELEASE',
            'num': f"{configs.get('OS_VERSION').data}",
            'description': desc
        }
    else:
        desc = f'''
            发布日期：{datetime.datetime.today().strftime('%Y-%m-%d %H:%M:%S')}
            软件版本：{configs.get('APP_VERSION').data}
            版本路径：{share}
        '''
        data = {
            'projectCode': f'{project}',
            'type': 'SOFTWARE',
            'property': 'RELEASE',
            'num': f"{configs.get('APP_VERSION').data}",
            'description': desc
        }

    print(HW_PLATFORM, data)
    statue = True
    response = requests.post(HW_PLATFORM, json=data)
    if response.status_code == 200:
        print(f'Version Upload to HW: {response.json()}')
        response_data = response.json()
        if response_data['code'] == 20000:
            print('Version Upload to HW Success')
        else:
            print(f"Version Upload to HW Failure: {response_data['message']}")
            statue = False
    else:
        print('Failed to send POST request:', response.status_code, response.reason)
        statue = False
    return statue


def upload(local_path, remote_path, remote_path_dir=None):
    if remote_path_dir:
        r = cloud.mkdir(NEXTCLOUD_UID, remote_path_dir)
        print(r)
        if not r.is_ok:
            return False, ""
    r = cloud.upload(NEXTCLOUD_UID, local_path, remote_path)
    if not r:
        return False, ""
    r = cloud.create_share(remote_path, 3)
    if r.is_ok:
        return True, r.data['url']
    else:
        return False, ""



def create_path(NEXTCLOUD_UID, path, current_path=""):
    """
    递归创建云盘文件夹路径
    @param NEXTCLOUD_UID: 云盘UID
    @param path: 文件夹路径，格式为: a/b/c/d
    @param current_path: 当前处理到的路径，默认为空字符串
    @return: 创建成功返回True,否则返回False
    """
    if path is None:
        return False
    path = path.strip('/')
    if not path:
        return True
    
    parts = path.split('/', 1)
    first_dir = parts[0]
    remaining_path = parts[1] if len(parts) > 1 else ""
    
    if current_path:
        created_path = f"{current_path.rstrip('/')}/{first_dir}"
    else:
        created_path = first_dir
    
    try:
        if not cloud.check(NEXTCLOUD_UID, created_path):
            success = cloud.mkdir(NEXTCLOUD_UID, created_path)
            if not success.is_ok:
                return False
    except Exception as e:
        return False
    return create_path(NEXTCLOUD_UID, remaining_path, created_path)


if __name__ == '__main__':
    r = cloud.check(NEXTCLOUD_UID, "jenkins_release/")
    print(r)
