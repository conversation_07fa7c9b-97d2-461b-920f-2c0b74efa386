CREATE TABLE public.chip_uart (
    id SERIAL PRIMARY KEY,
    type VARCHAR(10)[] DEFAULT '{}',
    func_work_model VARCHAR(20)[] DEFAULT '{}',
    func_alt_value VARCHAR(50),
    channel VARCHAR(50),
    communication_speed INTEGER[] DEFAULT '{}',
    interupt_flag VARCHAR(5)[] DEFAULT '{}',
    enable_config VARCHAR(10)[] DEFAULT '{}'
);


ALTER TABLE public.chip_uart
ADD COLUMN name VARCHAR(100),  -- 名称
ADD COLUMN des TEXT;           -- 描述