from rest_framework import serializers


class FunctionListSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)
    project_number = serializers.Char<PERSON><PERSON>(max_length=255, required=False, allow_null=True, allow_blank=True)


class FunctionSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.Cha<PERSON><PERSON><PERSON>(max_length=255, required=False, allow_null=True, allow_blank=True)
    parent_id = serializers.IntegerField(required=False)


class FunctionUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
