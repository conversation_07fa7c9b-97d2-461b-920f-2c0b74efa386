CREATE TABLE public.param_origin_location (
  id SERIAL PRIMARY KEY,
  var_name VARCHAR(100) NOT NULL,
  node_level VARCHAR(50) NOT NULL,
  param_key VARCHAR(100) NOT NULL,
  belong VARCHAR(20) NOT NULL CHECK (belong IN ('user_defined', 'variables')),
  location VARCHAR(100) NOT NULL
);

COMMENT ON COLUMN public.param_origin_location.var_name IS '子节点名称';
COMMENT ON COLUMN public.param_origin_location.node_level IS '子节点等级';
COMMENT ON COLUMN public.param_origin_location.param_key IS '参数名';
COMMENT ON COLUMN public.param_origin_location.belong IS '所属模块';
COMMENT ON COLUMN public.param_origin_location.location IS '存储表名';

--- 修改约束 ------
SELECT constraint_name
FROM information_schema.table_constraints
WHERE table_name = 'param_origin_location'
  AND table_schema = 'public'
  AND constraint_type = 'CHECK';



ALTER TABLE public.param_origin_location
DROP CONSTRAINT param_origin_location_belong_check;



ALTER TABLE public.param_origin_location
ADD CONSTRAINT param_origin_location_belong_check  -- 约束名称保持一致（也可自定义）
CHECK (belong IN ('user_defined', 'variables', 'arrays'));


CREATE TABLE public.branch_defined_value (
  id SERIAL PRIMARY KEY,
  -- 项目相关字段
  project_code VARCHAR(50) NOT NULL,
  project_name VARCHAR(100) NOT NULL,
  project_gitlab VARCHAR(255) NOT NULL,
  project_branch VARCHAR(100) NOT NULL,

  -- 参数节点信息（固定值）
  var_name VARCHAR(50) NOT NULL,
  node_level VARCHAR(10) NOT NULL,

  -- 模块信息
  user_defined VARCHAR(100) DEFAULT NULL,
  variables VARCHAR(100) DEFAULT NULL
);

COMMENT ON COLUMN public.branch_defined_value.project_code IS '项目号';
COMMENT ON COLUMN public.branch_defined_value.project_name IS '项目名称';
COMMENT ON COLUMN public.branch_defined_value.project_gitlab IS 'GitLab仓库';
COMMENT ON COLUMN public.branch_defined_value.project_branch IS 'Git分支名称';

COMMENT ON COLUMN public.branch_defined_value.var_name IS '子节点名称';
COMMENT ON COLUMN public.branch_defined_value.node_level IS '子节点等级';

COMMENT ON COLUMN public.branch_defined_value.user_defined IS '用户自定义模块参数';
COMMENT ON COLUMN public.branch_defined_value.variables IS '系统变量模块参数';

ALTER TABLE public.branch_defined_value
add COLUMN arrays VARCHAR(255);