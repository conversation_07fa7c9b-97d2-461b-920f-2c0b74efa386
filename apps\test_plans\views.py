import traceback
import logging

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from .serializers import (
    TestPlanSerializer, TestPlanListSerializer, TestPlanUpdateSerializer, TestSubPlanStatusUpdateSerializer
)
from .models import TestPlanModel

logger = logging.getLogger("machine")


class TestPlansView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestPlanListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestPlanModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = TestPlanSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user

            model = TestPlanModel()
            model.create(
                **serializer.validated_data,
                creator_email=user.email, creator_name=user.username, creator_employee_number=user.employee_number
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = TestPlanModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = TestPlanUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            # user = request.user
            model = TestPlanModel()

            # r = model.retrieve(pk=pk)
            # if r.get("creator_employee_number") != user.employee_number:
            #     return Response({"err_code": 1, "msg": "创建人不符合！"}, status.HTTP_400_BAD_REQUEST)

            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            # user = request.user
            model = TestPlanModel()

            # r = model.retrieve(pk=pk)
            # if r.get("creator_employee_number") != user.employee_number:
            #     return Response({"err_code": 1, "msg": "创建人不符合！"}, status.HTTP_400_BAD_REQUEST)

            model.delete(pk=pk)
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanDetailV2View(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = TestPlanModel()
            result = model.retrieve2(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestSubPlanStatusUpdateView(APIView):
    def post(self, request):
        try:
            serializer = TestSubPlanStatusUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestPlanModel()
            model.update_sub_plan_status(
                plan_id=serializer.validated_data.get("plan_id"),
                sub_plan_id=serializer.validated_data.get("sub_plan_id"),
                status=serializer.validated_data.get("status")
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
