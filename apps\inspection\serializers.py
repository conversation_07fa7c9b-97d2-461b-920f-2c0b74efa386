from rest_framework import serializers


class GetInspectionItemSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    plan = serializers.IntegerField(required=False)


class CreateInspectionItemSerializer(serializers.Serializer):
    parent_id = serializers.IntegerField(default=0)
    name = serializers.CharField(max_length=255)
    code = serializers.CharField(max_length=255)
    desc = serializers.CharField()


class UpdateInspectionItemSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    desc = serializers.CharField()
