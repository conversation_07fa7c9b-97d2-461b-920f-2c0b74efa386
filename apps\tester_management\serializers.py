from rest_framework import serializers

from .models import Tester<PERSON><PERSON>, TesterSkill, Tester


class TesterRoleListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TesterRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = TesterRole
        fields = "__all__"


class TesterListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    department_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TesterSerializer(serializers.ModelSerializer):
    roles = TesterRoleSerializer(many=True, read_only=True)
    role_ids = serializers.ListSerializer(child=serializers.IntegerField(), min_length=1, write_only=True)

    class Meta:
        model = Tester
        fields = "__all__"

    def create(self, validated_data):
        role_ids = validated_data.pop('role_ids', [])
        tester = super().create(validated_data)
        tester.roles.set(role_ids)
        return tester

    def update(self, instance, validated_data):
        role_ids = validated_data.pop('role_ids', [])
        tester = super().update(instance, validated_data)
        tester.roles.set(role_ids)
        return tester


class TesterSkillListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    department_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TesterSkillTypeListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TesterSkillSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=False)
    name = serializers.CharField(max_length=255)
    type_id = serializers.IntegerField()
    type_name = serializers.CharField(max_length=255, read_only=True)
    department = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class TesterSkillTypeSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=False)
    name = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class TesterSkillUpdateSerializer(serializers.Serializer):
    department = serializers.CharField(max_length=255)
    name = serializers.CharField(max_length=255)
    type_id = serializers.CharField(max_length=255, required=False)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class TesterSkillTypeUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)