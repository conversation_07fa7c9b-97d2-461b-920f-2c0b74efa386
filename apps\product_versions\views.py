import traceback
import logging
import datetime
import urllib.parse
import tempfile
import os
import hashlib
import shutil
import zipfile

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from .serializers import (
    ProductVersionListSerializer, ProductVersionSerializer, ProductVersionUpdateStatusSerializer,
    ProductVersionStatusUpdateSerializer, ProductVersionRemarkUpdateSerializer,
)
from .models import ProductVersionModel, PersonProductVersionMap
from utils.fs_service import FSService
from users.models import UserFSInfo, User
from utils.fs_app import fs_app
from utils.NextcloudHelper2 import cloud, NEXTCLOUD_UID, JENKINS_ROOT, download_directory, upload_file, copy_dir

logger = logging.getLogger("product_versions")

fs_service = FSService()

RELEASE_DIR = "HW_Version_Release"
PRODUCTION_DIR = "HW_Production_Software_Version"


class ProductVersionsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = ProductVersionListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = ProductVersionModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProductVersionDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = ProductVersionModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


def get_token():
    username = "<EMAIL>"
    password = "hwtc@666"
    fs = FSService()
    f, data = fs.login2(username=username, password=password)
    if not f:
        raise
    else:
        u_data = data.get("data", {})

        access_token = u_data.get("access_token")
        refresh_token = u_data.get("refresh_token")
        token_type = u_data.get("token_type")
        expires_in = u_data.get("expires_in")
        scope = u_data.get("scope")
        jti = u_data.get("jti")
        user_info = u_data.get("userInfo", {})
        employee_number = user_info.get("employeeNo")

        UserFSInfo.objects.update_or_create(
            employee_number=employee_number,
            defaults={
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": token_type,
                "expires_in": expires_in,
                "scope": scope,
                "jti": jti,
                "update_time": datetime.datetime.now(),
            }
        )

        token = access_token

    return token


class ProductVersionTestSubmitView(APIView):
    def post(self, request):
        try:
            serializer = ProductVersionSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = ProductVersionModel()

            project_id = serializer.validated_data.get("project_id")
            project_name = serializer.validated_data.get("project_name")
            project_number = serializer.validated_data.get("project_number")
            v_type = serializer.validated_data.get("type_name")
            v_name = serializer.validated_data.get("version_name")
            v_num = serializer.validated_data.get("version_number")
            v_use = serializer.validated_data.get("use_name")
            version_package_address = serializer.validated_data.get("version_package_address")
            plan_version = serializer.validated_data.get("plan_version")
            customer_version = serializer.validated_data.get("customer_version")

            response = cloud.create_share(f'automated_test/{project_number}/{v_num}/reports/QAC', 3)
            if response.is_ok:
                qac_url = response.data['url']
            else:
                logger.error("创建QAC分享链接失败！")
                qac_url = ""
            response = cloud.create_share(f'automated_test/{project_number}/{v_num}/reports/Tessy', 3)
            if response.is_ok:
                tessy_url = response.data['url']
            else:
                logger.error("创建TESSY分享链接失败！")
                tessy_url = ""

            pv_id = model.create(
                **serializer.validated_data,
                qac_url=qac_url,
                tessy_url=tessy_url,
            )

            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")

            for i in members:
                if i.get("roleId") == "22":
                    PersonProductVersionMap.objects.create(
                        person_name=i.get("userInfo").get("realName"),
                        person_email=i.get("userInfo").get("username"),
                        product_version_id=pv_id
                    )

            members = [i.get("userInfo").get("openId") for i in members if i.get("roleId") == "22"]
            members.extend(["ou_e82a70ff52d39ba22f7e2179815075b1", "ou_129fee2f6ca21b06a75cb01b2034f37e",
                            "ou_c39136df80f4a89b894d224d144ad9bc", "ou_5b7e19003e6773945cea374bfe335e2e"])

            msg = {
                "template_id": "AAq70pl2T6JKB",
                "template_variable": {
                    "project": f"{project_name}({project_number})",
                    "v_type": v_type,
                    "v_number": v_num,
                    "v_use": v_use,
                    "url": f"http://www.hwauto.com.cn:59999/product_versions/list?version_number={v_num}",
                    "version_package_address": version_package_address,
                }
            }
            f, data = fs_app.send_msg_batch(members, msg, msg_type="interactive")

            if not f:
                logger.error("推送版本发布消息失败！%s", data.get("msg"))
                return Response({"err_code": 2, "msg": "推送版本发布消息失败！{}".format(data.get("msg"))},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProductVersionReleaseView(APIView):
    def post(self, request):
        try:
            logger.info("request.data: %s", request.data)
            serializer = ProductVersionUpdateStatusSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = ProductVersionModel()

            err_msg = []
            project_number = serializer.validated_data.get("project_number")
            for version_name in serializer.validated_data.get("version_names"):

                content = model.list(project_number=project_number, version_name=version_name, status_ex="DEPRECATED")
                if not content["results"]:
                    err_msg.append(f"{project_number} {version_name} 版本不存在。")
                    logger.warning(f"{project_number} {version_name} 版本不存在。")
                    continue

                r = content["results"][0]

                if r.get("status") != "PASSED":
                    err_msg.append(f"{project_number} {version_name} 版本状态({r.get('status')})不是已通过。")
                    logger.warning(f"{project_number} {version_name} 版本状态({r.get('status')})不是已通过。")
                    continue

                model.update_status(r.get("id"), "RELEASED")

                version_number = r.get("version_number")
                version_number = urllib.parse.quote(version_number)

                r = cloud.list_folders(NEXTCLOUD_UID, f"{JENKINS_ROOT}/{project_number}", depth=2)
                copied_count = 0
                if r.is_ok:
                    for i in r.data:
                        href = i.get("href")
                        p = href.split("/")
                        if len(p) < 3:
                            continue
                        un = p[-3]
                        vn = p[-2]
                        if vn == version_number:
                            if not cloud.check(NEXTCLOUD_UID, f'{RELEASE_DIR}/{project_number}'):
                                cloud.mkdir(NEXTCLOUD_UID, f'{RELEASE_DIR}/{project_number}')
                            if not cloud.check(NEXTCLOUD_UID, f'{RELEASE_DIR}/{project_number}/{un}'):
                                cloud.mkdir(NEXTCLOUD_UID, f'{RELEASE_DIR}/{project_number}/{un}')

                            r2 = cloud.copy_path(NEXTCLOUD_UID, f"{JENKINS_ROOT}/{project_number}/{un}/{vn}",
                                                 f"{RELEASE_DIR}/{project_number}/{un}/{vn}")
                            copied_count += 1
                            if not r2.is_ok:
                                logger.error("cloud.copy_path 失败")

                    logger.info("copied_count: %s", copied_count)
                else:
                    logger.error("cloud.list_folders 失败")
                    return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            if err_msg:
                return Response({"err_code": 1, "msg": "".join(err_msg)}, status.HTTP_400_BAD_REQUEST)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProductSpecialVersionReleaseView(APIView):
    def post(self, request):
        try:
            logger.info("request.data : %s", request.data)
            serializer = ProductVersionUpdateStatusSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = ProductVersionModel()

            err_msg = []
            project_number = serializer.validated_data.get("project_number")
            for version_name in serializer.validated_data.get("version_names"):

                content = model.list(project_number=project_number, version_name=version_name, status_ex="DEPRECATED")
                if not content["results"]:
                    err_msg.append(f"{project_number} {version_name} 版本不存在。")
                    logger.error(f"{project_number} {version_name} 版本不存在。")
                    continue

                r = content["results"][0]

                if r.get("status") != "SMOKE_TEST_PASSED":
                    err_msg.append(f"{project_number} {version_name} 版本状态{r.get('status')}不是冒烟测试通过。")
                    logger.error(f"{project_number} {version_name} 版本状态{r.get('status')}不是冒烟测试通过。")
                    continue

                model.update_status(r.get("id"), "SPECIAL_VERSION_RELEASED")

                version_number = r.get("version_number")
                version_number = urllib.parse.quote(version_number)

                r = cloud.list_folders(NEXTCLOUD_UID, f"{JENKINS_ROOT}/{project_number}", depth=2)
                copied_count = 0
                if r.is_ok:
                    for i in r.data:
                        href = i.get("href")
                        p = href.split("/")
                        if len(p) < 3:
                            continue
                        un = p[-3]
                        vn = p[-2]
                        if vn == version_number:
                            if not cloud.check(NEXTCLOUD_UID, f'{PRODUCTION_DIR}/{project_number}'):
                                cloud.mkdir(NEXTCLOUD_UID, f'{PRODUCTION_DIR}/{project_number}')
                            if not cloud.check(NEXTCLOUD_UID, f'{PRODUCTION_DIR}/{project_number}/{un}'):
                                cloud.mkdir(NEXTCLOUD_UID, f'{PRODUCTION_DIR}/{project_number}/{un}')
                            if not cloud.check(NEXTCLOUD_UID, f'{PRODUCTION_DIR}/{project_number}/{un}/temp'):
                                cloud.mkdir(NEXTCLOUD_UID, f'{PRODUCTION_DIR}/{project_number}/{un}/temp')

                            r2 = cloud.copy_path(NEXTCLOUD_UID, f"{JENKINS_ROOT}/{project_number}/{un}/{vn}",
                                                 f"{PRODUCTION_DIR}/{project_number}/{un}/temp/{vn}")
                            copied_count += 1
                            if not r2.is_ok:
                                logger.error("cloud.copy_path 失败")

                    logger.info("copied_count: %s", copied_count)
                else:
                    logger.error("cloud.list_folders 失败")
                    return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            if err_msg:
                return Response({"err_code": 1, "msg": "".join(err_msg)}, status.HTTP_400_BAD_REQUEST)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProductVersionProduceView(APIView):
    def post(self, request):
        try:
            logger.info("request.data: %s", request.data)
            serializer = ProductVersionUpdateStatusSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = ProductVersionModel()

            err_msg = []
            project_number = serializer.validated_data.get("project_number")
            for version_name in serializer.validated_data.get("version_names"):

                content = model.list(project_number=project_number, version_name=version_name, status_ex="DEPRECATED")
                if not content["results"]:
                    err_msg.append(f"{project_number} {version_name} 版本不存在。")
                    logger.error(f"{project_number} {version_name} 版本不存在。")
                    continue

                r = content["results"][0]

                if r.get("status") != "RELEASED":
                    err_msg.append(f"{project_number} {version_name} 版本状态{r.get('status')}不是已发布。")
                    logger.error(f"{project_number} {version_name} 版本状态{r.get('status')}不是已发布。")
                    continue

                model.update_status(r.get("id"), "PRODUCTION")

                version_number = r.get("version_number")
                version_number = urllib.parse.quote(version_number)

                r = cloud.list_folders(NEXTCLOUD_UID, f"{RELEASE_DIR}/{project_number}", depth=2)
                copied_count = 0
                if r.is_ok:
                    for i in r.data:
                        href = i.get("href")
                        p = href.split("/")
                        if len(p) < 3:
                            continue
                        un = p[-3]
                        vn = p[-2]
                        if vn == version_number:
                            if not cloud.check(NEXTCLOUD_UID, f'{PRODUCTION_DIR}/{project_number}'):
                                cloud.mkdir(NEXTCLOUD_UID, f'{PRODUCTION_DIR}/{project_number}')
                            if not cloud.check(NEXTCLOUD_UID, f'{PRODUCTION_DIR}/{project_number}/{un}'):
                                cloud.mkdir(NEXTCLOUD_UID, f'{PRODUCTION_DIR}/{project_number}/{un}')

                            r2 = cloud.copy_path(NEXTCLOUD_UID, f"{RELEASE_DIR}/{project_number}/{un}/{vn}",
                                                 f"{PRODUCTION_DIR}/{project_number}/{un}/{vn}")
                            copied_count += 1
                            if not r2.is_ok:
                                logger.error("cloud.copy_path 失败")

                    logger.info("copied_count: %s", copied_count)
                else:
                    logger.error("cloud.list_folders 失败")
                    return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            if err_msg:
                return Response({"err_code": 1, "msg": "".join(err_msg)}, status.HTTP_400_BAD_REQUEST)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProductVersionStatusUpdateView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = ProductVersionStatusUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            product_version_id = serializer.validated_data.get("id")
            status_ = serializer.validated_data.get("status")
            remark = serializer.validated_data.get("remark")
            package_url = serializer.validated_data.get("package_url")
            target_url = serializer.validated_data.get("target_url")
            attachment_url = serializer.validated_data.get("attachment_url")

            model = ProductVersionModel()

            r = model.retrieve(product_version_id)
            if not r:
                return Response({"err_code": 1, "msg": f"对应id({product_version_id})记录不存在"},
                                status.HTTP_400_BAD_REQUEST)

            if status_ == "TESTING":
                if not (r.get("type") == "HARDWARE" and r.get("status") == "SUBMITTED"):
                    return Response({"err_code": 1, "msg": f"操作不允许！"},
                                    status.HTTP_400_BAD_REQUEST)

            elif status_ == "SMOKE_TEST_PASSED":
                if r.get("status") != "TESTING":
                    return Response({"err_code": 1, "msg": f"状态({r.get('status')})不能变为冒烟测试通过"},
                                    status.HTTP_400_BAD_REQUEST)
            elif status_ == "PASSED":
                if not (r.get("status") in ["SMOKE_TEST_PASSED", "SPECIAL_VERSION_RELEASED"]):
                    return Response({"err_code": 1, "msg": f"状态({r.get('status')})不能变为测试通过"},
                                    status.HTTP_400_BAD_REQUEST)
            elif status_ == "RELEASED":
                if not (r.get("status") in ["PASSED"]):
                    return Response({"err_code": 1, "msg": f"状态({r.get('status')})不能变为已发布"},
                                    status.HTTP_400_BAD_REQUEST)
            elif status_ == "PRODUCTION":
                if not (r.get("status") in ["RELEASED"]):
                    return Response({"err_code": 1, "msg": f"状态({r.get('status')})不能变为已量产"},
                                    status.HTTP_400_BAD_REQUEST)
            elif status_ == "FAILED":
                if not (r.get("status") in ["TESTING", "SMOKE_TEST_PASSED", "SPECIAL_VERSION_RELEASED"]):
                    return Response({"err_code": 1, "msg": f"状态({r.get('status')})不能变为测试失败"},
                                    status.HTTP_400_BAD_REQUEST)
            elif status_ == "DEPRECATED":
                if not (r.get("status") in ["SUBMITTED", "TESTING", "SMOKE_TEST_PASSED", "SPECIAL_VERSION_RELEASED"]):
                    return Response({"err_code": 1, "msg": f"状态({r.get('status')})不能变为废弃"},
                                    status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"err_code": 1, "msg": f"状态({status_})不允许"}, status.HTTP_400_BAD_REQUEST)

            if status_ == "RELEASED":
                if not cloud.check(NEXTCLOUD_UID, package_url):
                    return Response({"err_code": 1, "msg": f"版本包地址({package_url})不存在"},
                                    status.HTTP_400_BAD_REQUEST)
                if not cloud.check(NEXTCLOUD_UID, target_url):
                    return Response({"err_code": 1, "msg": f"目标地址({target_url})不存在"},
                                    status.HTTP_400_BAD_REQUEST)

                with tempfile.TemporaryDirectory(prefix="version_release_") as temp_dir:
                    download_path = os.path.join(temp_dir, "downloaded")
                    os.makedirs(download_path)
                    f, msg = download_directory(package_url, download_path)
                    if not f:
                        return Response({"err_code": 3, "msg": msg}, status.HTTP_500_INTERNAL_SERVER_ERROR)

                    zip_filename = os.path.basename(package_url.rstrip('/')) + '.zip'
                    zip_path = os.path.join(temp_dir, zip_filename)

                    shutil.make_archive(
                        os.path.splitext(zip_path)[0],
                        'zip',
                        download_path
                    )

                    with open(zip_path, 'rb') as f:
                        md5_hash = hashlib.md5()
                        for chunk in iter(lambda: f.read(4096), b""):
                            md5_hash.update(chunk)
                        md5_value = md5_hash.hexdigest()

                    md5_file_path = os.path.join(temp_dir, "md5.txt")
                    with open(md5_file_path, "w") as f:
                        f.write(f"{md5_value}  {zip_filename}\n")

                    f, msg = upload_file(zip_path, target_url)
                    if not f:
                        return Response({"err_code": 3, "msg": msg}, status.HTTP_500_INTERNAL_SERVER_ERROR)
                    upload_file(md5_file_path, target_url)
                    if not f:
                        return Response({"err_code": 3, "msg": msg}, status.HTTP_500_INTERNAL_SERVER_ERROR)
            elif status_ == "PRODUCTION":
                if not cloud.check(NEXTCLOUD_UID, package_url):
                    return Response({"err_code": 1, "msg": f"版本包地址({package_url})不存在"},
                                    status.HTTP_400_BAD_REQUEST)
                if not cloud.check(NEXTCLOUD_UID, target_url):
                    return Response({"err_code": 1, "msg": f"目标地址({target_url})不存在"},
                                    status.HTTP_400_BAD_REQUEST)

                f, msg = copy_dir(package_url, target_url)
                if not f:
                    return Response({"err_code": 2, "msg": msg}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            if (status_ == "FAILED" or status_ == "PASSED") and r.get("type") == "HARDWARE":
                model.update_attachment_url(product_version_id, attachment_url)

            model.update_status(product_version_id, status_)

            o_remark = r.get("remark")
            if o_remark:
                remark = o_remark + "\n" + remark
            model.update_remark(product_version_id, remark)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProductVersionRemarkUpdateView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = ProductVersionRemarkUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            id_ = serializer.validated_data.get("id")
            remark = serializer.validated_data.get("remark")

            model = ProductVersionModel()

            r = model.retrieve(id_)
            if not r:
                return Response({"err_code": 1, "msg": f"对应id({id_})记录不存在"},
                                status.HTTP_400_BAD_REQUEST)

            model.update_remark(id_, remark)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
