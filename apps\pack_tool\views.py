import traceback
import logging
import hashlib
import tempfile
import shutil
import os
import uuid
import time
import urllib.parse
import subprocess
import datetime
import zipfile
import pprint

import requests
import json

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.http import HttpResponse

from diff_tool.h56c_diff_tool.bin2hex import diff_bin_file, diff_hex_file, diff_zip_file
from pack_tool.models import PackageGenerationRecordsModel, PackageShareUrlsModel
from .serializers import (
    PackToolUsageRecordListSerializer, PackToolUsageRecordSerializer,
    PackageShareUrlSerializer, PackToolUsageRecordVerifySerializer
)
from .models import PackToolUsageRecord, PackToolUsageRecordModel
from utils.NextcloudHelper import upload

logger = logging.getLogger("diff_tool")

CLOUD_DIR = "automated_test/WPTSN11/PackingFiles"

DIR = os.path.dirname(os.path.abspath(__file__))


class PackToolUsageRecordsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = PackToolUsageRecordListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = PackToolUsageRecordModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            user = request.user

            serializer = PackToolUsageRecordSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            project_name = serializer.validated_data.get("project_name")
            project_number = serializer.validated_data.get("project_number")
            component_code = serializer.validated_data.get("component_code")
            software_version = serializer.validated_data.get("software_version")
            screen_touchpad_version = serializer.validated_data.get("screen_touchpad_version")
            input_files = serializer.validated_data.get("input_files")
            input_file_identifs = serializer.validated_data.get("input_file_identifs")
            input_file_crc_vailds = serializer.validated_data.get("input_file_crc_vailds")
            output_type = serializer.validated_data.get("output_type")
            srecord_type = serializer.validated_data.get("srecord_type")
            output_path = serializer.validated_data.get("output_path")
            pmic_flag = serializer.validated_data.get("pmic_flag")
            encrypt = serializer.validated_data.get("encrypt")
            encrypt_file = serializer.validated_data.get("encrypt_file")

            try:
                temp_dir = tempfile.mkdtemp()

                output_path = os.path.join(temp_dir, output_path)

                if encrypt == 0:
                    encrypt_file_path = os.path.join(temp_dir, "encrypt_file")
                    with open(encrypt_file_path, 'wb') as destination:
                        for chunk in encrypt_file.chunks():
                            destination.write(chunk)
                else:
                    encrypt_file_path = None

                blocks = []
                input_f = []
                for i, v in enumerate(input_files):
                    f = v
                    ft = os.path.splitext(f.name)[1].lower().replace('.', "")
                    fn = f.name
                    md5 = hashlib.md5()
                    for chunk in f.chunks():
                        md5.update(chunk)
                    f_md5 = md5.hexdigest()
                    p = os.path.join(temp_dir, f_md5 + "-" + fn)

                    with open(p, 'wb') as destination:
                        for chunk in f.chunks():
                            destination.write(chunk)

                    blocks.append({
                        "path": p,
                        "identification": input_file_identifs[i],
                        "crc_vaild": input_file_crc_vailds[i],
                        "file_type": ft,
                    })

                    input_f.append({
                        "file": fn,
                        "identification": input_file_identifs[i],
                        "crc_vaild": input_file_crc_vailds[i],
                        "md5": f_md5,
                    })

                data = {
                    "component_code": component_code,
                    "software_version": software_version,
                    "screen_touchpad_version": screen_touchpad_version,
                    "output_type": output_type,
                    "srecord_type": srecord_type,
                    "output_path": output_path,
                    "encrypt": encrypt,
                    "encrypt_path": encrypt_file_path,
                    "pmic_flag": pmic_flag,
                    "blocks": blocks
                }
                config_file_path = os.path.join(temp_dir, "config_file.json")
                with open(config_file_path, "wt") as destination:
                    json.dump(data, destination)

                if os.name == "nt":
                    command_file_path = os.path.join(DIR, "tools", "v1", "packer", "windows", "packer.exe")
                elif os.name == "posix":
                    command_file_path = os.path.join(DIR, "tools", "v1", "packer", "linux", "packer")
                else:
                    return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

                cmd = f"{command_file_path} -t {project_number} {config_file_path} "

                # 执行命令
                result = subprocess.run(cmd, shell=True, check=False, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                        text=True)

                # 检查返回码
                if result.returncode != 0:
                    logger.error(f"打包失败. %s %s", result.returncode, result.stderr)
                    return Response({"err_code": 2, "msg": f"打包失败。 {result.returncode} {result.stderr}"},
                                    status.HTTP_500_INTERNAL_SERVER_ERROR)

                if not os.path.exists(output_path):
                    logger.error(f"找不到输出文件. %s %s", result.returncode, result.stdout)
                    return Response({"err_code": 2, "msg": f"找不到输出文件。 {result.returncode} {result.stdout}"},
                                    status.HTTP_500_INTERNAL_SERVER_ERROR)

                logger.info("打包成功！")

                with open(output_path, "rb") as f:
                    md5 = hashlib.md5()
                    md5.update(f.read())
                    output_path_md5 = md5.hexdigest()

                fn_d = str(uuid.uuid4())
                fn = os.path.split(output_path)[-1]

                md5_file_path = os.path.join(temp_dir, fn + ".md5")

                with open(md5_file_path, "wt") as f:
                    f.writelines([f"Version={software_version[0]:02d}.{software_version[1]:02d}\n", f"MD5={output_path_md5}"])

                zip_path = os.path.join(temp_dir, os.path.splitext(fn)[0]+ "_" + datetime.datetime.now().strftime("%y%m%d") + ".zip")

                with zipfile.ZipFile(zip_path, 'w') as z:
                    z.write(output_path, os.path.split(output_path)[-1])
                    z.write(md5_file_path, os.path.split(md5_file_path)[-1])

                start = time.time()
                logger.info("上传文件服务器开始")
                f, url = upload(zip_path, f"{CLOUD_DIR}/{fn_d}/{os.path.split(zip_path)[-1]}", f"{CLOUD_DIR}/{fn_d}")
                logger.info(f"上传文件服务器结束 花费{time.time() - start}")
                if not f:
                    logger.error(f"上传文件服务器失败")
                    return Response({"err_code": 2, "msg": "上传文件服务器失败！"},
                                    status.HTTP_500_INTERNAL_SERVER_ERROR)
                else:
                    output_file_url = url

                PackToolUsageRecord.objects.create(
                    project_name=project_name,
                    project_number=project_number,
                    component_code=component_code,
                    software_version=f"{software_version[0]}.{software_version[1]}",
                    screen_touchpad_version=f"{screen_touchpad_version[0]}.{screen_touchpad_version[1]}" if screen_touchpad_version else None,
                    pmic_flag=pmic_flag,
                    encrypt=encrypt,
                    encrypt_file=encrypt_file.name if encrypt == 0 else None,
                    output_type=output_type,
                    srecord_type=srecord_type,
                    input_files=input_f,
                    packer_version="v1",
                    output_file_name=fn,
                    output_file_md5=output_path_md5,
                    output_file_url=output_file_url,
                    operator_name=user.username,
                    operator_email=user.email,
                )
            finally:
                shutil.rmtree(temp_dir)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class PackageMadeByJenkinsView(APIView):
    def post(self, request):
        try:
            serializer = PackToolUsageRecordSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = PackageGenerationRecordsModel()

            project_name = serializer.validated_data.get("project_name")
            project_number = serializer.validated_data.get("project_number")
            pack_old_package_version = serializer.validated_data.get("pack_old_package_version")
            old_package = serializer.validated_data.get("old_package")
            pack_new_package_version = serializer.validated_data.get("pack_new_package_version")
            new_package = serializer.validated_data.get("new_package")
            packer_version = serializer.validated_data.get("packer_version")
            software_version = serializer.validated_data.get("software_version")
            screen_touchpad_version = serializer.validated_data.get("screen_touchpad_version")
            output_file_name = serializer.validated_data.get("output_file_name")
            pack_input_file_type = serializer.validated_data.get("pack_input_file_type")

            if project_number != "ILTCF07":
                return Response({"err_code": 1, "msg": f"暂无项目{project_name}({project_number})差分包制作工具！"},
                                status.HTTP_400_BAD_REQUEST)

            logger.info(f"start diff {old_package.name} {new_package.name} {output_file_name} "
                        f"-version={packer_version} -pack_input_file_type={ pack_input_file_type}")

            if  pack_input_file_type == "bin":
                if ((os.path.splitext(old_package.name)[1].lower() != ".bin") or
                        (os.path.splitext(new_package.name)[1].lower() != ".bin")):
                    logger.error(f"输入文件不是bin文件")
                    return Response({"err_code": 1, "msg": "输入文件不是bin文件"}, status.HTTP_400_BAD_REQUEST)
                if os.path.splitext(output_file_name)[1].lower() != ".hex":
                    output_file_name += ".hex"
            elif  pack_input_file_type == "hex":
                if ((os.path.splitext(old_package.name)[1].lower() != ".hex") or
                        (os.path.splitext(new_package.name)[1].lower() != ".hex")):
                    logger.error(f"输入文件不是hex文件")
                    return Response({"err_code": 1, "msg": "输入文件不是hex文件"}, status.HTTP_400_BAD_REQUEST)
                if os.path.splitext(output_file_name)[1].lower() != ".hex":
                    output_file_name += ".hex"
            elif  pack_input_file_type == "zip":
                if ((os.path.splitext(old_package.name)[1].lower() != ".zip") or
                        (os.path.splitext(new_package.name)[1].lower() != ".zip")):
                    logger.error(f"输入文件不是zip文件")
                    return Response({"err_code": 1, "msg": "输入文件不是zip文件"}, status.HTTP_400_BAD_REQUEST)
                if os.path.splitext(output_file_name)[1].lower() != ".zip":
                    output_file_name += ".zip"

            pack_old_package_name = old_package.name
            md5 = hashlib.md5()
            for chunk in old_package.chunks():
                md5.update(chunk)
            pack_old_package_md5 = md5.hexdigest()
            pack_new_package_name = new_package.name
            md5 = hashlib.md5()
            for chunk in new_package.chunks():
                md5.update(chunk)
            pack_new_package_md5 = md5.hexdigest()

            temp_dir = tempfile.mkdtemp()
            try:
                old_output_file_url = os.path.join(temp_dir, pack_old_package_name)
                with open(old_output_file_url, 'wb+') as destination:
                    for chunk in old_package.chunks():
                        destination.write(chunk)

                new_output_file_url = os.path.join(temp_dir, pack_new_package_name)
                with open(new_output_file_url, 'wb+') as destination:
                    for chunk in new_package.chunks():
                        destination.write(chunk)

                output_file_url = os.path.join(temp_dir, output_file_name)

                if  pack_input_file_type == "bin":
                    r = diff_bin_file(old_output_file_url, new_output_file_url, output_file_url, packer_version,
                                      temp_dir)
                elif  pack_input_file_type == "hex":
                    r = diff_hex_file(old_output_file_url, new_output_file_url, output_file_url, packer_version,
                                      temp_dir)
                elif  pack_input_file_type == "zip":
                    r = diff_zip_file(old_output_file_url, new_output_file_url, output_file_url, packer_version,
                                      temp_dir)
                else:
                    logger.error(f" pack_input_file_type不是bin, hex, zip中的一个")
                    return Response({"err_code": 1, "msg": " pack_input_file_type不是bin, hex, zip中的一个！"},
                                    status.HTTP_400_BAD_REQUEST)

                if not r:
                    logger.error(f"制作差分包失败")
                    return Response({"err_code": 2, "msg": "制作差分包失败！"}, status.HTTP_400_BAD_REQUEST)
                else:

                    with open(output_file_url, "rb") as f:
                        md5 = hashlib.md5()
                        md5.update(f.read())
                        output_file_md5 = md5.hexdigest()
                    if model.exists(diff_package_md5=output_file_md5):
                        return Response({"err_code": 1, "msg": "已存在相同差分包MD5! 不能重复制作差分包！"},
                                        status.HTTP_400_BAD_REQUEST)

                    logger.info("差分包制作成功！")

                    fn_d = str(uuid.uuid4())
                    fn = output_file_name
                    start = time.time()
                    logger.info("差分包上传文件服务器开始")
                    f, url = upload(output_file_url, f"{CLOUD_DIR}/{fn_d}/{fn}", f"{CLOUD_DIR}/{fn_d}")
                    logger.info(f"差分包上传文件服务器结束 花费{time.time() - start}")
                    if not r:
                        logger.error(f"差分包上传文件服务器失败")
                        return Response({"err_code": 2, "msg": "差分包上传文件服务器失败！"},
                                        status.HTTP_400_BAD_REQUEST)
                    else:
                        output_file_url = url
            finally:
                shutil.rmtree(temp_dir)

            model.create(
                project_name=project_name, project_number=project_number, pack_input_file_type= pack_input_file_type,
                pack_old_package_name=pack_old_package_name, pack_old_package_md5=pack_old_package_md5,
                pack_old_package_version=pack_old_package_version,
                pack_new_package_name=pack_new_package_name, pack_new_package_md5=pack_new_package_md5,
                pack_new_package_version=pack_new_package_version,
                software_version=software_version, screen_touchpad_version=screen_touchpad_version,
                output_file_name=output_file_name, output_file_md5=output_file_md5,
                output_file_url=output_file_url, packer_version=packer_version,
                operator_name="Jenkins", operator_email="<EMAIL>",
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class PackToolUsageRecordDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = PackToolUsageRecordModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            user = request.user

            record = PackToolUsageRecord.objects.get(pk=pk)

            if record.operator_email != '<EMAIL>' and record.operator_email != user.email:
                return Response({"err_code": 1, "msg": "创建人不符合！"}, status.HTTP_400_BAD_REQUEST)

            record.delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class PackToolUsageRecordVerifyView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = PackToolUsageRecordVerifySerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            id = serializer.validated_data.get("id")
            package_status = serializer.validated_data.get("package_status")

            user = request.user

            model = PackageGenerationRecordsModel()

            r = model.retrieve(id)
            if not r:
                return Response({"err_code": 1, "msg": "对应id差分包记录不存在！"}, status.HTTP_400_BAD_REQUEST)
            if r.get("operator_email") != user.email:
                return Response({"err_code": 1, "msg": "操作人不符！"}, status.HTTP_400_BAD_REQUEST)
            if r.get("package_status") != 0:
                return Response({"err_code": 1, "msg": "差分包状态不为未验证！"}, status.HTTP_400_BAD_REQUEST)

            model.update_verify_status(id=id, package_status=package_status)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class PackageShareUrlsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            user = request.user

            serializer = PackageShareUrlSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = PackageShareUrlsModel()

            pack_record_id = serializer.validated_data.get("pack_record_id")
            expiration_time = serializer.validated_data.get("expiration_time")
            token = str(uuid.uuid4())

            r = PackageGenerationRecordsModel().retrieve(pack_record_id)
            if not r:
                return Response({"err_code": 1, "msg": "差分包记录不存在"}, status.HTTP_400_BAD_REQUEST)

            if r.get("package_status") != 1:
                return Response({"err_code": 1, "msg": "差分包未验证通过"}, status.HTTP_400_BAD_REQUEST)

            model.create(
                pack_record_id=pack_record_id, token=token, expiration_time=expiration_time,
                creator_name=user.username, creator_email=user.email,
            )

            return Response({"err_code": 0, "data": {"token": token}, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class PackageShareUrlDetailView(APIView):
    authentication_classes = []  # 禁用认证
    permission_classes = []  # 禁用权限

    def get(self, request, token=None):
        try:
            model = PackageShareUrlsModel()
            result = model.retrieve(token=token)
            if not result:
                return Response({"err_code": 1, "msg": "无效的链接或链接已过期"}, status.HTTP_400_BAD_REQUEST)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class PackageDownloadView(APIView):
    def get(self, request, token=None):
        try:
            model = PackageShareUrlsModel()
            r = model.retrieve(token)
            if not r:
                return HttpResponse("链接失效", status=400)
            url = r.get("output_file_url")
            filename = r.get("output_file_name")
            filename = urllib.parse.quote(filename)

            resp = requests.get(url + "/download/" + filename, stream=True)

            response = HttpResponse(
                resp.content,
                content_type="application/octet-stream"
            )

            response['Content-Disposition'] = f'attachment; filename={filename}'

            return response
        except Exception:
            logger.error(traceback.format_exc())
            return HttpResponse("服务器错误", status=500)
