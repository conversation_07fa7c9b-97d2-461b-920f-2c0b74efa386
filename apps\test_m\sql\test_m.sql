--测试用例表
CREATE TABLE IF NOT EXISTS public.test_cases(
    id serial primary key, --id
    "name" varchar(120),
    project_number varchar(255) unique ,
    submitter varchar(255),
    tc_file varchar(255),
    version int,
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.test_cases OWNER TO atpms;

--测试用例类型表
CREATE TABLE IF NOT EXISTS public.test_case_types(
    id serial primary key, --id
    "name" varchar(255), --名称
    "number" varchar(255),  --编号
    "desc" varchar(255),  --描述
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.test_case_types OWNER TO atpms;

--测试用例类型表
CREATE TABLE IF NOT EXISTS public.test_case_status(
    id serial primary key, --id
    "name" varchar(255), --名称
    "number" varchar(255),  --编号
    "desc" varchar(255),  --描述
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.test_case_status OWNER TO atpms;