import cantools
from pprint import pprint


class DBCParser:
    def __init__(self):
        self.db = None
        self.version = None
        self.nodes = None
        self.messages = None
        self.content = None

    def parse(self, dbc_file):
        self.db = cantools.database.load_file(dbc_file)
        self.version = self.db.version
        self.nodes = self.db.nodes
        self.messages = self.db.messages

        self.content = {
            "version": self.version,
            "messages": []
        }

        for message in self.messages:
            msg_content = {
                "name": message.name,
                "frame_id": message.frame_id,
                "length": message.length,
                "comment": message.comment,
                "senders": list(message.senders),
                "receivers": list(message.receivers),
                "send_type": message.send_type,
                "cycle_time": message.cycle_time,
                "protocol": message.protocol,
                "signals": [],
            }
            for signal in message.signals:

                choices_dict = {}
                if signal.choices:
                    for key, value in signal.choices.items():
                        # 确保键是可序列化的
                        serializable_key = int(key) if hasattr(key, '__int__') else str(key)
                        choices_dict[serializable_key] = str(value)

                signal_content = {
                    "name": signal.name,
                    "start": signal.start,
                    "length": signal.length,
                    "byte_order": signal.byte_order,
                    "is_signed": signal.is_signed,
                    "scale": signal.scale,
                    "offset": signal.offset,
                    "minimum": signal.minimum,
                    "maximum": signal.maximum,
                    "choices": choices_dict,
                    "is_float": signal.is_float,
                    "unit": signal.unit,
                    "initial": (signal.raw_initial * signal.scale + signal.offset) if (
                            signal.raw_initial is not None) else signal.minimum,
                    "raw_initial": signal.raw_initial,
                    "comment": signal.comment,
                }
                msg_content["signals"].append(signal_content)
            self.content["messages"].append(msg_content)

    def signal2message(self, message):
        frame_id = message.get("frame_id")
        signal_c = {}
        for signal in message.get("signals"):
            signal_c[signal.get("name")] = signal.get("actualValue")

        msg = self.db.encode_message(
            frame_id,
            signal_c,
        )

        msg = msg.hex().upper()

        return msg

    def message2signal(self, message):
        frame_id = message.get("frame_id")
        msg = message.get("msg")

        signal_c = self.db.decode_message(
            frame_id,
            bytes.fromhex(msg),
            decode_choices=False,
        )

        return signal_c
