import datetime
import json

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone
from django.db import transaction, models


class DBCFile(models.Model):
    project_id = models.CharField(max_length=255, blank=True, null=True)
    project_name = models.CharField(max_length=255, blank=True, null=True)
    project_number = models.CharField(max_length=50, blank=True, null=True)
    file_name = models.CharField(max_length=255, unique=True)
    file_path = models.TextField()
    file_url = models.TextField()
    content = models.TextField()
    version = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    upload_user_name = models.CharField(max_length=50)
    upload_user_email = models.Char<PERSON>ield(max_length=50)
    status = models.SmallIntegerField(default=1)
    tags = models.TextField(blank=True, null=True)
    create_time = models.DateT<PERSON><PERSON><PERSON>(auto_now_add=True)
    update_time = models.DateTime<PERSON>ield(auto_now=True)

    class Meta:
        db_table = 'dbc_files'
        managed = False
        app_label = 'can_dbc'

    def to_dict(self):
        return {
            "id": self.id,
            "project_id": self.project_id,
            "project_name": self.project_name,
            "project_number": self.project_number,
            "file_name": self.file_name,
            "file_path": self.file_path,
            "file_url": self.file_url,
            "content": json.loads(self.content),
            "version": self.version,
            "description": self.description,
            "upload_user_name": self.upload_user_name,
            "upload_user_email": self.upload_user_email,
            "status": self.status,
            "tags": self.tags,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S") if self.create_time else None,
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S") if self.update_time else None,
        }


class DBCModel:
    def __init__(self):
        self.table_name = "public.dbc_files"

    def create(self, user, **kwargs):
        dbc_file = DBCFile.objects.create(
            project_id=kwargs.get("project_id"),
            project_name=kwargs.get("project_name"),
            project_number=kwargs.get("project_number"),
            file_name=kwargs.get("file_name"),
            file_path=kwargs.get("file_path"),
            file_url=kwargs.get("file_url"),
            content=kwargs.get("content"),
            version=kwargs.get("version"),
            description=kwargs.get("description"),
            upload_user_name=user.username,
            upload_user_email=user.email,
            tags=kwargs.get("tags"),
        )
        return dbc_file.id

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        query = DBCFile.objects.all()

        file_name = kwargs.get("file_name")
        if file_name:
            query = query.filter(file_name__icontains=file_name)
        version = kwargs.get("version")
        if version:
            query = query.filter(version__icontains=version)
        project_number = kwargs.get("project_number")
        if project_number:
            query = query.filter(project_number=project_number)
        upload_user_name = kwargs.get("upload_user_name")
        if upload_user_name:
            query = query.filter(upload_user_name__icontains=upload_user_name)

        count = query.count()

        query = query.order_by("-update_time")
        query = query[(page - 1) * pagesize: page * pagesize]

        results = query.values(
            "id", "project_id", "project_name", "project_number",
            "file_name", "file_url",
            "version", "description", "upload_user_name",
            "upload_user_email",
            "create_time", "update_time"
        )

        for result in results:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S") if result["create_time"] else None
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S") if result["update_time"] else None

        content = {
            "count": count,
            "results": results,
        }

        return content

    def retrieve(self, pk):
        dbc = DBCFile.objects.filter(id=pk).first()
        if not dbc:
            return None
        dbc = dbc.to_dict()
        return dbc

    def delete(self, pk):
        DBCFile.objects.get(id=pk).delete()

    def update(self, pk, **kwargs):
        dbc = DBCFile.objects.filter(id=pk).first()
        if dbc:
            dbc.description = kwargs.get("description")
            dbc.tags = kwargs.get("tags")

            dbc.save()