from rest_framework import serializers


class TestProductListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    m_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    project_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    type = serializers.Char<PERSON>ield(max_length=255, required=False, allow_blank=True, allow_null=True)


class TestProductSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255)
    type = serializers.CharField(max_length=50, required=False, allow_blank=True, allow_null=True)
    m_number = serializers.Char<PERSON>ield(max_length=255, required=False, allow_blank=True, allow_null=True)
    hardware_versions = serializers.J<PERSON><PERSON>ield()
    software_versions = serializers.J<PERSON><PERSON>ield()
    project_name = serializers.CharField(max_length=255)
    project_number = serializers.Char<PERSON>ield(max_length=255)
    project_id = serializers.CharField(max_length=255)
    components = serializers.ListField(
        child=serializers.IntegerField(), required=False, allow_empty=True, allow_null=True
    )


class TestProductUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    type = serializers.CharField(max_length=50, required=False, allow_blank=True, allow_null=True)
    m_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    hardware_versions = serializers.JSONField()
    software_versions = serializers.JSONField()
    components = serializers.ListField(
        child=serializers.IntegerField(), required=False, allow_empty=True, allow_null=True
    )
