import logging
import traceback

from django.core.paginator import Paginator
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication

from .TestPrototypes import TestPrototypes
from .serializers import (
    TestPrototypesListSerializer, TestPrototypesSerializer, TestPrototypesUpdateSerializer
)

logger = logging.getLogger("test_prototypes")


class TestPrototypesView(APIView):
    # 登录认证【如果是调试模式可以屏蔽以下代码】
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestPrototypesListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            page = serializer.validated_data.get("page", 1)
            pagesize = serializer.validated_data.get("pagesize", 10)
            name_re = serializer.validated_data.get("name_re")
            number_re = serializer.validated_data.get("number_re")
            obj_list = TestPrototypes.objects.all().reverse()
            if name_re is not None and name_re != "":
                obj_list = obj_list.filter(name__iregex=name_re)
            if number_re is not None and number_re != "":
                obj_list = obj_list.filter(number__iregex=number_re)

            obj_list = obj_list.order_by("-id")

            paginator = Paginator(obj_list, pagesize)

            page_obj = paginator.get_page(page)

            content = {
                "count": paginator.count,
                "results": list(page_obj.object_list.values())
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            # 指定人员才有权限增加产品类型数据
            # user = request.user
            # if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
            #     return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            serializer = TestPrototypesSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            number = f"HW-TP-0000"
            test_prototypes = TestPrototypes.objects.last()
            if test_prototypes is not None:
                last_number = test_prototypes.number
                if last_number is not None and last_number.__contains__("-"):
                    index = int(last_number.split("-")[2])
                    number = f"HW-TP-{str(index + 1).zfill(4)}"

            TestPrototypes.objects.create(
                name=serializer.validated_data.get("name"),
                type=serializer.validated_data.get("type"),
                status=serializer.validated_data.get("status"),
                number=number,
                project=serializer.validated_data.get("project"),
                project_name=serializer.validated_data.get("project_name"),
                user_name=serializer.validated_data.get("user_name"),
                user_email=serializer.validated_data.get("user_email"),
                desc=serializer.validated_data.get("desc"),
                prototype_stage=serializer.validated_data.get("prototype_stage")
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPrototypesDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            obj = TestPrototypes.objects.get(id=pk)
            return Response({"err_code": 0, "data": obj.to_dict()}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            # user = request.user
            # if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
            #     return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)
            serializer = TestPrototypesUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            name = serializer.validated_data.get("name")
            prototypes_type = serializer.validated_data.get("type")
            prototypes_status = serializer.validated_data.get("status")
            project = serializer.validated_data.get("project")
            project_name = serializer.validated_data.get("project_name")
            user_name = serializer.validated_data.get("user_name")
            user_email = serializer.validated_data.get("user_email")
            desc = serializer.validated_data.get("desc")
            prototype_stage = serializer.validated_data.get("prototype_stage")

            obj = TestPrototypes.objects.get(id=pk)

            obj.name = name
            obj.type = prototypes_type
            obj.status = prototypes_status
            obj.project = project
            obj.project_name = project_name
            obj.user_name = user_name
            obj.user_email = user_email
            obj.desc = desc
            obj.prototype_stage = prototype_stage

            obj.save()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            # user = request.user
            # if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
            #     return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            result = TestPrototypes.objects.get(id=pk)
            result.delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPrototypesTypeView(APIView):
    # 登录认证【如果是调试模式可以屏蔽以下代码】
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestPrototypesListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            content = {
                "types": TestPrototypes.TYPE_CHOICE
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPrototypesStatusView(APIView):
    # 登录认证【如果是调试模式可以屏蔽以下代码】
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestPrototypesListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            content = {
                "status": TestPrototypes.STATUS_CHOICE
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

