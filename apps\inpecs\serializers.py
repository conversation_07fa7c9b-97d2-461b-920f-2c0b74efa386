from rest_framework import serializers


class GetInspecItemSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)


class CreateInspecItemSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    code = serializers.CharField(max_length=255)
    desc = serializers.CharField()


class UpdateInspecItemSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255)
    desc = serializers.CharField()
