CREATE TABLE public.chip_spi (
    id SERIAL PRIMARY KEY,
    type VARCHAR(10)[] DEFAULT '{}',                   -- 类型
    func_work_model VARCHAR(20)[] DEFAULT '{}',        -- 工作模式
    func_alt_value VARCHAR(50),                        -- 复用值
    spi_type VARCHAR(20)[] DEFAULT '{}',               -- SPI 类型
    soft_spi_type VARCHAR(20)[] DEFAULT '{}',          -- 软件 SPI 类型
    channel VARCHAR(50),                               -- 通道号
    communication_speed VARCHAR(50) DEFAULT '500',     -- 通讯速率[kHz]
    transfor_bits VARCHAR(5)[] DEFAULT '{}',           -- 传输位数
    work_mode VARCHAR(10)[] DEFAULT '{}',              -- 工作模式
    enable_config VARCHAR(10)[] DEFAULT '{}'           -- 是否启用配置
);

ALTER TABLE public.chip_spi
ADD COLUMN name VARCHAR(100),  -- 名称
ADD COLUMN des TEXT;           -- 描述