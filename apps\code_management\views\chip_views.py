import logging
import traceback
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db import transaction
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.cache import cache
from django.core.serializers import serialize
import json
import os
import shutil
import time
from pathlib import Path
from django.db import models
from django.contrib.postgres.fields import ArrayField
from django.forms.models import model_to_dict

logger = logging.getLogger("code_management")
from apps.code_management.grpc.host_machine_grpc import host_machine_client
from apps.code_management.grpc.git_grpc import git_client
from apps.code_management.models.project_models import CodeBranchInfo
from apps.code_management.models.chip_models import (
            ChipPinConfig,
            Chip,
            ChipModule,
            ChipMap,
            ChipGPIO,
            ChipADC,
            ChipPWM,
            ChipIIC,
            ChipSPI,
            ChipEXIT,
            ChipUART,
            ChipPinType,
        ChipModuleSerializer,
        ChipGpioSerializer,
        ChipSPISerializer,
        ChipUARTSerializer,
        ChipIICSerializer,
        ChipADCSerializer,
        ChipPWMSerializer,
        ChipEXITSerializer
)
from apps.code_management.models.config_models import MainSpecialEnum

from users.models import UserFSInfo
from utils.fs_service import FSService
from utils.fs_app import fs_app


local_path = "/home/<USER>/hwcp"
config_path = "/home/<USER>/hwcp/gRPC_Server/HwcpTest"
config_url = "http:/,fanh*********/python-team/HwcpTest.git"
config_branch = "main"
sdk_git_path = "http://*********/"


def queryset_to_dict(queryset_or_instance):
    """
    通用函数：将 QuerySet 或模型实例转换为字典格式
    自动处理不同字段类型，包括 ArrayField、JSONField 等
    """
    def model_instance_to_dict(instance):
        """将单个模型实例转换为字典"""
        data = {}

        # 获取模型的所有字段
        for field in instance._meta.fields:
            field_name = field.name
            field_value = getattr(instance, field_name)

            # 处理不同类型的字段
            if field_value is None:
                data[field_name] = None
            elif hasattr(field, 'base_field'):  # ArrayField
                # PostgreSQL ArrayField 转换为列表
                data[field_name] = list(field_value) if field_value else []
            elif isinstance(field, models.JSONField):
                # JSONField 保持原样（已经是 dict 或 list）
                data[field_name] = field_value
            elif isinstance(field, (models.DateTimeField, models.DateField, models.TimeField)):
                # 日期时间字段转换为字符串
                data[field_name] = field_value.isoformat() if field_value else None
            elif isinstance(field, models.BooleanField):
                # 布尔字段
                data[field_name] = bool(field_value)
            elif isinstance(field, (models.IntegerField, models.BigIntegerField, models.SmallIntegerField)):
                # 整数字段
                data[field_name] = int(field_value) if field_value is not None else None
            elif isinstance(field, (models.FloatField, models.DecimalField)):
                # 浮点数字段
                data[field_name] = float(field_value) if field_value is not None else None
            else:
                # 其他字段类型（CharField, TextField 等）转换为字符串
                data[field_name] = str(field_value) if field_value is not None else None

        return data

    # 判断输入类型并处理
    if hasattr(queryset_or_instance, '__iter__') and not isinstance(queryset_or_instance, (str, dict)):
        # QuerySet 或列表
        return [model_instance_to_dict(instance) for instance in queryset_or_instance]
    else:
        # 单个模型实例
        return model_instance_to_dict(queryset_or_instance)


def get_label(field_name):
    """
    获取字段名对应的中文 label（假设 ChipMap 表中有 name 和 label 两个字段）
    """
    try:
        logger.info("field_name: %s", field_name)
        label_obj = ChipMap.objects.using("hwcp").filter(name=field_name)
        data = dict()
        for item in label_obj:
            data = {
                "name": item.name,
                "label": item.label
            }
        logger.info("data: %s", data)
        logger.info("data.label: %s", data["label"])
        return data["label"]  # 假设字段叫 label
    except ChipMap.DoesNotExist:
        return field_name  # 找不到就返回原字段名



def get_name(u_name):
    """
    获取字段名对应的中文 label（假设 ChipMap 表中有 name 和 label 两个字段）
    """
    try:
        logger.info("u_name: %s", u_name)
        name_obj = ChipMap.objects.using("hwcp").filter(label=u_name)
        data = dict()
        for item in name_obj:
            data = {
                "name": item.name,
                "label": item.label
            }
        logger.info("data: %s", data)
        logger.info("data.name: %s", data["name"])
        return data["name"]  # 假设字段叫 label
    except ChipMap.DoesNotExist:
        return u_name  # 找不到就返回原字段名

class ChipViews(APIView):

    def get_model_data(self, model_class, database="hwcp", filters=None, **kwargs):
        """
        通用方法：获取任意模型的数据并转换为字典格式

        Args:
            model_class: Django 模型类
            database: 数据库别名，默认为 "hwcp"
            filters: 过滤条件字典，如 {"chip": "KF32A158"}
            **kwargs: 其他查询参数

        Returns:
            list: 转换后的字典列表
        """
        try:
            # 构建查询
            queryset = model_class.objects.using(database)

            # 应用过滤条件
            if filters:
                queryset = queryset.filter(**filters)

            # 应用其他查询参数
            if kwargs:
                queryset = queryset.filter(**kwargs)

            # 获取所有数据
            data = queryset.all()

            # 转换为字典格式
            return queryset_to_dict(data)

        except Exception as e:
            logger.error(f"获取 {model_class.__name__} 数据失败: {e}")
            return []


    def get_chip(self, project_code, project_gitlab):
        # 查找芯片名称
        try:
            # project = CodeBranchInfo.objects.only("chip").get(
            #     project_code=project_code,
            #     project_gitlab=project_gitlab,
            #     project_branch=project_branch
            # )
            project = CodeBranchInfo.objects.only("chip").filter(
                project_code=project_code,
                project_gitlab=project_gitlab
            ).first()
        except CodeBranchInfo.DoesNotExist:
            return Response({"message": "未找到项目"})
        except CodeBranchInfo.MultipleObjectsReturned:
            return Response({"message": "存在多个匹配项目，请检查数据"})
        # 如果查到，返回 chip 字段
        chip_value = project.chip
        logger.info("chip_value: %s", chip_value)
        return chip_value


    def get_module_color(self):
        # 获取模块颜色 - 使用通用方法
        return self.get_model_data(ChipModule)


    def get_chip_type_info(self, chip):
        """
        查询ChipPinType中对应chip的数据，并按module进行分类

        Args:
            chip: 芯片名称

        Returns:
            dict: 按module分类的type信息
        """
        try:
            # 查询ChipPinType中对应chip的所有数据
            chip_pin_types = ChipPinType.objects.using("hwcp").filter(chip=chip)

            if not chip_pin_types.exists():
                logger.info(f"未找到芯片 {chip} 的ChipPinType数据")
                return {}

            # 按module进行分类
            type_info = {}
            for chip_pin_type in chip_pin_types:
                module = chip_pin_type.model
                if module not in type_info:
                    type_info[module] = []

                type_info[module].append({
                    "pin_id": chip_pin_type.pin_id,
                    "type": chip_pin_type.type,
                    "alt_values": chip_pin_type.alt_values
                })

            logger.info(f"芯片 {chip} 的type_info数据: {type_info}")
            return type_info

        except Exception as e:
            logger.error(f"查询ChipPinType数据失败: {e}")
            return {}

    def get_chip_table(self, chip, project_code=None, project_name=None, project_gitlab=None, project_branch=None):
        # 查询所有module字段，返回QuerySet
        modules_qs = ChipModule.objects.using("hwcp").values_list('module', flat=True)
        # 如果需要转成列表
        io = list(modules_qs)

        # 获取引脚配置表数据 - 使用通用方法
        table_info = self.get_model_data(Chip, filters={"chip": chip})
        logger.info("默认table_info: %s", table_info)
        # 如果提供了项目参数，检查ChipPinConfig中是否有数据
        if all([project_code, project_name, project_gitlab, project_branch]):
            try:
                # 查询ChipPinConfig中是否有对应项目的数据
                chip_pin_configs = ChipPinConfig.objects.using("hwcp").filter(
                    project_code=project_code,
                    project_name=project_name,
                    project_gitlab=project_gitlab,
                    project_branch=project_branch,
                    chip=chip
                )

                if chip_pin_configs.exists():
                    logger.info("找到ChipPinConfig数据，开始更新table_info的Module字段")
                    # 创建pin_id到module的映射
                    pin_module_map = {}
                    for config in chip_pin_configs:
                        pin_module_map[config.pin_id] = config.module

                    # 更新table_info中对应pin_id的module字段
                    for item in table_info:
                        pin_id = item.get('pin_id')
                        if pin_id in pin_module_map:
                            item['model'] = pin_module_map[pin_id]  # 保持数组格式
                            logger.info(f"更新pin_id {pin_id}的model为: {pin_module_map[pin_id]}")
                            item['status'] = True  # 保持数组格式
                            logger.info(f"更新pin_id {pin_id}status: True 已配置")
                else:
                    logger.info("ChipPinConfig中未找到对应项目数据，使用默认table_info")

            except Exception as e:
                logger.error(f"查询ChipPinConfig数据失败: {e}")
                logger.info("使用默认table_info")

        return io, table_info


    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)
            project_code = params.get("project_code")
            project_name = params.get("project_name")
            project_gitlab = params.get("project_gitlab")
            project_branch = params.get("project_branch")

            # 参数验证
            if not all([project_code, project_gitlab]):
                return Response({
                    "status": 0,
                    "message": "缺少必要参数，请提供 project_code, project_gitlab"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取芯片
            chip = self.get_chip(project_code, project_gitlab)

            # 如果 get_chip 返回了 Response 对象，说明出错了
            if isinstance(chip, Response):
                return chip

            # 获取引脚颜色
            color = self.get_module_color()

            # 获取引脚配置表数据
            io, table_info = self.get_chip_table(chip, project_code, project_name, project_gitlab, project_branch)

            # 获取芯片类型信息，按module分类
            type_info = self.get_chip_type_info(chip)

            logger.info("chip: %s, color:%s, io:%s, table:%s, type_info:%s", chip, color, io, table_info, type_info)
            return Response({
                "status": 1,
                "message": "数据获取成功",
                "data": {
                            "chip": chip,
                            "color": color,
                            "io": io,
                            "table": table_info,
                            "type_info": type_info
                        }
                })

        except Exception as e:
            logger.error("获取芯片配置数据失败: %s", str(e))
            logger.error(traceback.format_exc())
            return Response({
                "status": 0,
                "message": f"获取芯片配置数据失败: {str(e)}"
            })

    def get_any_chip_data(self, request):
        """
        演示方法：展示如何使用通用函数获取任意芯片相关数据
        支持动态查询不同的芯片模型数据
        """
        try:
            # 获取查询参数
            model_name = request.query_params.get("model", "chip")  # 默认查询 chip 表
            chip_name = request.query_params.get("chip")

            # 模型映射
            model_mapping = {
                "chip": Chip,
                "chip_module": ChipModule,
                "chip_gpio": ChipGPIO,
                "chip_adc": ChipADC,
                "chip_pwm": ChipPWM,
                "chip_iic": ChipIIC,
                "chip_spi": ChipSPI,
                "chip_uart": ChipUART,
                "chip_exti": ChipEXIT,
                "chip_map": ChipMap,
            }

            # 获取对应的模型类
            model_class = model_mapping.get(model_name.lower())
            if not model_class:
                return Response({
                    "status": 0,
                    "message": f"不支持的模型: {model_name}，支持的模型: {list(model_mapping.keys())}"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 构建过滤条件
            filters = {}
            if chip_name and hasattr(model_class, 'chip'):
                filters['chip'] = chip_name

            # 使用通用方法获取数据
            data = self.get_model_data(model_class, filters=filters)

            return Response({
                "status": 1,
                "message": f"成功获取 {model_name} 数据",
                "data": {
                    "model": model_name,
                    "count": len(data),
                    "items": data
                }
            })

        except Exception as e:
            logger.error(f"获取 {model_name} 数据失败: {e}")
            logger.error(traceback.format_exc())
            return Response({
                "status": 0,
                "message": f"获取数据失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



class ChipConfigViews(APIView):





    def get_chipinfo(self, pin_id, chip):
        """
        获取 Chip 配置并格式化为包含 label 的字段列表
        """
        info = Chip.objects.using("hwcp").filter(chip=chip, pin_id=pin_id)

        formatted_data = []

        for item in info:
            fields = ["pin_id", "pin_name", "module"]
            for field in fields:
                if field == "id":
                    continue
                value = getattr(item, field, None)
                logger.info("value: %s", value)
                label = get_label(field)
                formatted_data.append({
                    "key": field,
                    "display": label,
                    "value": value,
                })

        return formatted_data

    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)
            pin_id = params.get("pinId")
            chip = params.get("chip")

            # 获取引脚信息
            pin_info = self.get_chipinfo(pin_id, chip)

            logger.info("pin_info: %s", pin_info)
            return Response({
                "status": 1,
                "message": "数据获取成功",
                "data": {
                    "pin_info": pin_info
                }
            })

        except Exception as e:
            logger.info("获取引脚数据失败: %s", str(e))
            return Response({"status": 0, "message": "Exception:" + str(e)})



class ChipModulViews(APIView):

    def get_chip_pin_type_default(self, chip, pin_id, module):
        """
        从ChipPinType表中获取指定芯片、引脚、模块的默认类型值

        Args:
            chip: 芯片名称
            pin_id: 引脚号
            module: 模块类型

        Returns:
            str: 默认类型值，如果未找到则返回None
        """
        try:
            logger.info(f"查询ChipPinType: chip={chip}, pin_id={pin_id}, module={module}")

            chip_pin_type = ChipPinType.objects.using("hwcp").filter(
                chip=chip,
                pin_id=str(pin_id),
                model=module
            ).first()

            if chip_pin_type:
                logger.info(f"找到ChipPinType默认值: chip={chip}, pin_id={pin_id}, module={module}, type={chip_pin_type.type}")
                # 检查type字段是否为空字符串或None
                if chip_pin_type.type and chip_pin_type.type.strip():
                    return chip_pin_type.type
                else:
                    logger.info(f"ChipPinType的type字段为空: '{chip_pin_type.type}'")
                    return None
            else:
                logger.info(f"未找到ChipPinType数据: chip={chip}, pin_id={pin_id}, module={module}")
                # 尝试查询所有相关数据进行调试
                all_types = ChipPinType.objects.using("hwcp").filter(chip=chip).values('pin_id', 'model', 'type')
                logger.info(f"芯片 {chip} 的所有ChipPinType数据: {list(all_types)}")

                # 尝试查询同一芯片同一模块的其他引脚，看看是否有有效的type值
                same_module_types = ChipPinType.objects.using("hwcp").filter(
                    chip=chip,
                    model=module
                ).exclude(type__isnull=True).exclude(type__exact='').values('pin_id', 'type')
                logger.info(f"芯片 {chip} 模块 {module} 的有效type数据: {list(same_module_types)}")

                # 如果找到了同模块的有效type，可以使用第一个作为默认值
                if same_module_types:
                    default_type = same_module_types[0]['type']
                    logger.info(f"使用同模块的默认type值: {default_type}")
                    return default_type

                return None

        except Exception as e:
            logger.error(f"查询ChipPinType失败: chip={chip}, pin_id={pin_id}, error={e}")  # 包含参数
            logger.error(f"数据库连接信息: hwcp数据库连接可能存在问题")
            return None

    def get_model_Serializer(self, module):
        model_name = None
        Serializer_name = None
        if module == "GPIO":
            logger.info("这是通用输入输出 GPIO")
            model_name = ChipGPIO
            Serializer_name = ChipGpioSerializer
        elif module == "SPI":
            logger.info("这是串行外设接口 SPI")
            model_name = ChipSPI
            Serializer_name = ChipSPISerializer
        elif module == "UART":
            logger.info("这是串口 UART")
            model_name = ChipUART
            Serializer_name = ChipUARTSerializer
        elif module == "IIC":
            logger.info("这是 I²C 接口")
            model_name = ChipIIC
            Serializer_name = ChipIICSerializer
        elif module == "ADC":
            logger.info("这是模拟转数字 ADC")
            model_name = ChipADC
            Serializer_name = ChipADCSerializer
        elif module == "PWM":
            logger.info("这是 PWM 波形输出")
            model_name = ChipPWM
            Serializer_name = ChipPWMSerializer
        elif module == "EXIT":
            logger.info("这是外部中断 EXIT")
            model_name = ChipEXIT
            Serializer_name = ChipEXITSerializer
        elif module == "OTHER":
            logger.info("这是其他功能模块")
        else:
            logger.info("不是设定中的模块")


        return model_name, Serializer_name


    def get_dev_module(self, module, chip_name=None, pin_id=None):

        model_name, Serializer_name = self.get_model_Serializer(module)
        if model_name:
            logger.info("model_name:%s", model_name)
            data = model_name.objects.using("hwcp").all()
            # 使用序列化器
            serializer = Serializer_name(data, many=True)

            # # 取序列化后的数据
            info = serializer.data

            # 转换为表单字段配置
            form_config = []
            if info:  # 确保不为空
                logger.info("info: %s", info)

                # 获取第一个配置对象（OrderedDict）
                # 获取第一个配置对象（OrderedDict）
                item = info[0]

                for key, value in item.items():
                    if key == "id":
                        continue

                    field = {
                        "key": key,
                        "label": get_label(key)
                    }

                    # 检查是否为类型字段，如果是则从ChipPinType获取默认值
                    default_type_value = None
                    if field["label"] == "类型" and chip_name and pin_id:
                        default_type_value = self.get_chip_pin_type_default(chip_name, pin_id, module)

                    if isinstance(value, list):
                        field["type"] = "select"
                        field["options"] = [{"label": v, "value": v} for v in value]

                        # 对于类型字段，尝试从ChipPinType获取默认值
                        if field["label"] == "类型" and chip_name and pin_id:
                            list_type_default = self.get_chip_pin_type_default(chip_name, pin_id, module)
                            if list_type_default:
                                field["placeholder"] = list_type_default
                                logger.info(f"在list字段处理时使用ChipPinType默认值: {list_type_default} for pin {pin_id}")
                            else:
                                field["placeholder"] = ""
                        else:
                            field["placeholder"] = ""

                    elif isinstance(value, str) and "min" in value and "max" in value:
                        logger.info("value: %s", value)
                        # 试着把字符串转成数字（支持0x开头的十六进制）
                        try:
                            value = json.loads(value) if isinstance(value, str) else value
                            min_val = int(str(value["min"]), 0)  # 明确转为 str
                            max_val = int(str(value["max"]), 0)
                            # 转成功，表示这是数字范围的值，变成number类型
                            field["type"] = "number"
                            field["min"] = min_val  # 如果你有最小最大范围，可以另外处理
                            field["max"] = max_val
                            # 十六进制转十进制
                            field["placeholder"] = ""
                        except ValueError:
                            # 转失败，说明就是普通字符串，保持原逻辑
                            field["type"] = "select"
                            field["options"] = [{"label": value, "value": value}]
                            field["placeholder"] = ""


                    elif isinstance(value, dict) and "min" in value and "max" in value:
                        field["type"] = "number"
                        field["min"] = int(value["min"])
                        field["max"] = int(value["max"])
                        field["placeholder"] = ""

                    else:
                        field["type"] = "text"
                        field["placeholder"] = ""

                    # 统一处理类型字段的默认值（在所有字段类型处理完成后）
                    if field["label"] == "类型" and default_type_value:
                        field["placeholder"] = default_type_value
                        logger.info(f"设置类型字段默认值: {default_type_value} for pin {pin_id}")

                    form_config.append(field)




        else:
            form_config = None

        return form_config




    def get_default_value(self, project_code, gitlab, project_branch, chip_name, pin_id, module):
        # 获取默认值
        try:
            config = ChipPinConfig.objects.using("hwcp").only('pin_id', 'func', 'config').get(
                project_code=project_code,
                project_gitlab=gitlab,
                project_branch=project_branch,
                chip=chip_name,
                pin_id=pin_id,
                module=module
            )

            logger.info("查询成功，初始配置为: %s", config)
            default = model_to_dict(config)
            logger.info("查询成功，配置为: %s", default)
            logger.info("type: %s", type(default))
            # 扁平化数据
            flat = {}

            for k, v in default.items():
                # 先尝试解析字符串成字典（例如 JSON 字符串）
                if isinstance(v, str):
                    try:
                        parsed = json.loads(v)
                        if isinstance(parsed, dict):
                            v = parsed
                    except json.JSONDecodeError:
                        pass  # 是普通字符串，跳过

                if isinstance(v, dict):
                    for subk, subv in v.items():
                        flat[f"{subk}"] = subv
                else:
                    flat[k] = v

            logger.info("扁平化后的数据为: %s", flat)
            return flat

        except ChipPinConfig.DoesNotExist:
            return "0"

        except ChipPinConfig.MultipleObjectsReturned:
            raise ValueError("查询的数据存在多条")

        except Exception as e:
            logger.info("查询数据错误: %s", e)
            return {}




    def get_branch_module(self, project_code, gitlab, project_branch, chip_name, pin_id, module):
        # 获取模块规则值
        model_name, Serializer_name = self.get_model_Serializer(module)
        if model_name:
            logger.info("model_name:%s", model_name)
            data = model_name.objects.using("hwcp").all()
            # 使用序列化器
            serializer = Serializer_name(data, many=True)

            # # 取序列化后的数据
            info = serializer.data

            # 转换为表单字段配置
            form_config = []
            if info:  # 确保不为空
                logger.info("info: %s", info)

                # 获取第一个配置对象（OrderedDict）
                # 获取第一个配置对象（OrderedDict）
                item = info[0]


                # 获取默认值
                default = self.get_default_value(project_code, gitlab, project_branch, chip_name, pin_id, module)

                logger.info("default: %s", default)
                if not default:
                    form_config = None
                else:

                    # 处理 item 中的字段
                    for key, value in item.items():
                        if key == "id":
                            continue

                        field = {
                            "key": key,
                            "label": get_label(key)
                        }
                        default_val = default.get(key) if default and (default != "0") else None
                        logger.info("default_val:%s", default_val)
                        # 检查是否为类型字段，如果是则从ChipPinType获取默认值
                        type_default_val = None
                        logger.info(f"处理字段: {key}, label: {field['label']}, default_val: {default_val}")
                        if field["label"] == "类型" and chip_name and pin_id:
                            logger.info(f"开始查询ChipPinType: chip_name={chip_name}, pin_id={pin_id}, module={module}")
                            type_default_val = self.get_chip_pin_type_default(chip_name, pin_id, module)
                            logger.info(f"ChipPinType查询结果: {type_default_val}")
                            # 如果从ChipPinType获取到了默认值，且当前没有有效的默认值，则使用ChipPinType的值
                            if type_default_val and type_default_val.strip() and (not default_val or default_val == ""):
                                default_val = type_default_val
                                logger.info(f"使用ChipPinType默认值: {type_default_val} for pin {pin_id}")
                            else:
                                logger.info(f"不使用ChipPinType默认值: type_default_val='{type_default_val}', default_val={default_val}")


                        if isinstance(value, list):
                            field["type"] = "select"
                            field["options"] = [{"label": v, "value": v} for v in value]

                            logger.info(f"处理list字段: {key}, label: {field['label']}, default_val: {default_val}")

                            # 对于类型字段，如果没有默认值，尝试从多个来源获取
                            if field["label"] == "类型" and not default_val and chip_name and pin_id:
                                logger.info(f"list字段处理中查询ChipPinType: chip_name={chip_name}, pin_id={pin_id}, module={module}")
                                type_default_from_db = self.get_chip_pin_type_default(chip_name, pin_id, module)
                                logger.info(f"list字段处理中ChipPinType查询结果: {type_default_from_db}")

                                # 如果ChipPinType中没有有效值，尝试从PinInfo中获取
                                if module in ["SPI", "UART", "IIC"]:
                                    if not (type_default_from_db and type_default_from_db.strip()):
                                        logger.info("ChipPinType中没有有效的type值，尝试从PinInfo中获取")
                                        pin_info = default.get('PinInfo', []) if default else []
                                        if pin_info and isinstance(pin_info, list):
                                            for pin_info_item in pin_info:
                                                if (isinstance(pin_info_item, dict) and
                                                    str(pin_info_item.get('pin_id')) == str(pin_id) and
                                                    pin_info_item.get('type')):
                                                    type_default_from_db = pin_info_item.get('type')
                                                    logger.info(f"从PinInfo中获取到type值: {type_default_from_db}")
                                                    break
                                else:
                                    type_default_from_db = module
                                if type_default_from_db and type_default_from_db.strip():
                                    default_val = type_default_from_db
                                    logger.info(f"在字段处理时使用默认值: {type_default_from_db} for pin {pin_id}")

                            if default_val:
                                field["placeholder"] = default_val
                                logger.info(f"设置placeholder为: {default_val}")
                            else:
                                # 最后一次尝试：如果是类型字段且placeholder为空，尝试多种方式获取默认值
                                if field["label"] == "类型" and chip_name and pin_id:
                                    logger.info(f"最后尝试查询ChipPinType: chip_name={chip_name}, pin_id={pin_id}, module={module}")
                                    final_type_default = self.get_chip_pin_type_default(chip_name, pin_id, module)

                                    # 如果ChipPinType中没有有效值，尝试从PinInfo中获取
                                    if not (final_type_default and final_type_default.strip()):
                                        logger.info("最后尝试：从PinInfo中获取type值")
                                        pin_info = default.get('PinInfo', []) if default else []
                                        if pin_info and isinstance(pin_info, list):
                                            for pin_info_item in pin_info:
                                                if (isinstance(pin_info_item, dict) and
                                                    str(pin_info_item.get('pin_id')) == str(pin_id) and
                                                    pin_info_item.get('type')):
                                                    final_type_default = pin_info_item.get('type')
                                                    logger.info(f"最后尝试：从PinInfo中获取到type值: {final_type_default}")
                                                    break

                                    if final_type_default and final_type_default.strip():
                                        field["placeholder"] = final_type_default
                                        logger.info(f"最终设置类型字段placeholder为: {final_type_default}")
                                    else:
                                        field["placeholder"] = ""
                                        logger.info(f"所有尝试都失败，placeholder设置为空字符串")
                                else:
                                    field["placeholder"] = ""
                                    logger.info(f"placeholder设置为空字符串")

                        elif isinstance(value, str) and "min" in value and "max" in value:
                            logger.info("value: %s", value)
                            # 试着把字符串转成数字（支持0x开头的十六进制）
                            try:
                                value = json.loads(value) if isinstance(value, str) else value
                                min_val = int(str(value["min"]), 0)  # 明确转为 str
                                max_val = int(str(value["max"]), 0)

                                field["type"] = "number"
                                field["min"] = min_val
                                field["max"] = max_val
                                field["placeholder"] = str(int(default_val, 0)) if default_val else ""
                            except Exception as e:
                                logger.warning("数值范围字段解析失败: %s", e)
                                field["type"] = "select"
                                field["options"] = [{"label": str(value), "value": str(value)}]
                                field["placeholder"] = default_val or ""



                        elif isinstance(value, dict) and "min" in value and "max" in value:
                            field["type"] = "number"
                            field["min"] = int(value["min"])
                            field["max"] = int(value["max"])
                            if default_val:
                                field["placeholder"] = default_val
                            else:
                                field["placeholder"] = ""

                        else:
                            field["type"] = "text"
                            if default_val:
                                field["placeholder"] = default_val
                            else:
                                field["placeholder"] = ""

                        form_config.append(field)

                    # 处理 default 中存在但 item 中不存在的特殊字段（如 PinInfo）
                    if default and isinstance(default, dict):
                        # 排除的系统字段
                        system_fields = {'id', 'pin_id', 'chip', 'module', 'project_code', 'project_name', 'project_gitlab', 'project_branch'}

                        for key in default.keys():
                            if key not in item and key not in system_fields:
                                logger.info(f"处理额外字段: {key}")

                                # 获取字段标签
                                field_label = get_label(key)
                                if not field_label or field_label == key:
                                    # 如果没有找到标签，使用默认标签
                                    field_label_map = {
                                        'PinInfo': '引脚信息',
                                        'func': '功能配置',
                                        'config': '配置参数'
                                    }
                                    field_label = field_label_map.get(key, key)

                                field = {
                                    "key": key,
                                    "label": field_label
                                }

                                default_val = default.get(key)

                                # 根据字段类型设置不同的处理方式
                                if key == 'PinInfo' and isinstance(default_val, list):
                                    # PinInfo 是一个数组，包含引脚信息
                                    field["type"] = "array"
                                    field["value"] = default_val
                                    field["placeholder"] = ""
                                elif isinstance(default_val, dict):
                                    # 字典类型的字段
                                    field["type"] = "object"
                                    field["value"] = default_val
                                    field["placeholder"] = ""
                                elif isinstance(default_val, list):
                                    # 数组类型的字段
                                    field["type"] = "array"
                                    field["value"] = default_val
                                    field["placeholder"] = ""
                                else:
                                    # 其他类型的字段
                                    field["type"] = "text"
                                    field["placeholder"] = str(default_val) if default_val is not None else ""

                                form_config.append(field)

        else:
            form_config = None

        return form_config

    def get(self, request):

        try:
            params = request.query_params
            logger.info("receive info: %s", params)
            project_code = params.get("project_code")
            gitlab = params.get("gitlab")
            project_branch = params.get("project_branch")
            branch_status = params.get('branch_status')
            chip_name = params.get("chip_name")
            pin_id = params.get("pin_number")
            module = params.get("module")

            if project_branch == "dev":
                logger.info("---- dev -----")
                module_info = self.get_dev_module(module, chip_name, pin_id)
            else:
                logger.info("---- not  dev -----")
                # 新建分支
                if branch_status == "1":
                    logger.info("---- new branch -----")
                    # 获取默认的引脚信息
                    module_info = self.get_dev_module(module, chip_name, pin_id)

                elif branch_status == "0":
                    logger.info("---- not new branch -----")
                    # 查询 pin_config_info 是否存在对应module的值
                    module_info = self.get_branch_module(project_code, gitlab, project_branch, chip_name, pin_id, module)
            if module_info:
            # 需要剔除的key
                exclude_keys = {'func_alt_value', 'func_work_model'}

                # 过滤数据（保留key不在剔除列表中的项）
                filtered_data = [item for item in module_info if item.get('key') not in exclude_keys]

                logger.info("filtered_data: %s", filtered_data)
                return Response({
                    "status": 1,
                    "message": "数据获取成功",
                    "data": {
                        "module": filtered_data
                    }
                })
            else:
                return Response({
                    "status": 0,
                    "message": "获取引脚数据失败：module_info为空"
                })

        except Exception as e:
            logger.info("获取引脚模块数据失败: %s", str(e))
            return Response({"status": 0, "message": str(e)})



class ChipTypeViews(APIView):
    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)

            chip_name = params.get("chip_name")
            pin_id = params.get("pin_number")
            config_module = params.get("function_type")
            
            # 获取当前芯片和模块得全部数据
            item = list(ChipPinType.objects.using("hwcp").filter(
                chip=chip_name,
                model=config_module
            ).values())


            
            

           

            logger.info("hwcp数据库事务成功提交")

            return Response({
                "status": 1,
                "message": "数据修改成功"
            })

        except Exception as e:
            logger.info("修改引脚数据失败: %s", str(e))
            return Response({"status": 0, "message": str(e)})




#
class ChipChangeViews(APIView):


    def modify_chip_info(self, sdk_path, workspace_path, branch_status, config_module, chip_name, item, pin_id):
        try:
            logger.info("receive info: sdk_path = %s", sdk_path)
            logger.info("receive info: workspace_path = %s", workspace_path)
            logger.info("receive info: branch_status = %s", branch_status)
            logger.info("receive info: config_module = %s", config_module)
            logger.info("receive info: chip_name = %s", chip_name)
            logger.info("receive info: item = %s", item)
            logger.info("receive info: pin_id = %s", pin_id)
            name = item.get("name")
            response = host_machine_client.modify_chip_parms(sdk_path, workspace_path, branch_status, chip_name, config_module, item, name, pin_id)

            logger.info(" 修改mcu芯片信息 response = %s", response)

            return response
        except Exception as e:
            logger.error(f"调用modify_chip_parms失败: {e}")
            return ""




    def insert_many_data(self, project_code, project_name, project_gitlab, project_branch,
                    pin_id, chip_name, config_module, module_config, func, config):
        logger.info("开始插入其他引脚的数据")
        pin_info = module_config.get("PinInfo", [])
        for pin in pin_info:
            # 这里假设 pin 是一个包含 pin_id 的字典
            pin_id_in_info = pin.get('pin_id')  # 获取每个 pin_id
            if pin_id_in_info:
                # 查询是否存在该 pin_id 的记录
                try:
                    pin_obj = ChipPinConfig.objects.using("hwcp").get(
                        project_code=project_code,
                        project_name=project_name,
                        project_gitlab=project_gitlab,
                        project_branch=project_branch,
                        pin_id=pin_id_in_info,
                        chip=chip_name
                    )
                    # 如果记录存在，更新
                    pin_obj.func = func
                    pin_obj.config = config
                    pin_obj.save()
                    logger.info(f"更新 PinInfo 中的 pin_id {pin_id_in_info} 的记录")
                except ChipPinConfig.DoesNotExist:
                    # 如果记录不存在，插入新记录
                    new_pin_obj = ChipPinConfig(
                        project_code=project_code,
                        project_name=project_name,
                        project_gitlab=project_gitlab,
                        project_branch=project_branch,
                        pin_id=pin_id_in_info,
                        chip=chip_name,
                        module=config_module,
                        func=func,
                        config=config
                    )
                    new_pin_obj.save()
                    logger.info(f"为 PinInfo 中的 pin_id {pin_id_in_info} 插入新记录")




    def delete_info(self, project_code, project_name, project_gitlab, project_branch, pin_id, chip_name, pin_id_to_delete):
        try:
            deleted_count, _ = ChipPinConfig.objects.using("hwcp").filter(project_code=project_code,
                project_name=project_name,
                project_gitlab=project_gitlab,
                project_branch=project_branch,
                pin_id=pin_id_to_delete,
                chip=chip_name).delete()
            if deleted_count > 0:
                logger.info(f"成功删除 pin_id={pin_id_to_delete} 的 ChipPinConfig 数据记录")
            else:
                logger.warning(f"未找到 pin_id={pin_id_to_delete} 的 ChipPinConfig 记录可删除")
        except Exception as e:
            logger.error(f"删除 pin_id={pin_id_to_delete} 的记录时出错: {e}")


    def insert_data(self, project_code, project_name, project_gitlab, project_branch,
                    pin_id, chip_name, config_module, module_config):
        try:

            # 先查询全匹配的记录
            obj = ChipPinConfig.objects.using("hwcp").get(
                project_code=project_code,
                project_name=project_name,
                project_gitlab=project_gitlab,
                project_branch=project_branch,
                pin_id=pin_id,
                chip=chip_name
            )
            logger.info("已找到匹配的 pin_id 数据记录")
            if "PinInfo" in module_config:
                logger.info(f"标签 PinInfo 存在, 开始删除历史关联得引脚数据")
                # 查找 func["PinInfo"] 中是否有 pin_id == 当前 pin_id 的数据
                pininfo_list = obj.func.get("PinInfo", [])

                if isinstance(pininfo_list, list):
                    for item in pininfo_list:
                        if isinstance(item, dict):
                            pin_id_to_delete = item.get("pin_id")
                            if pin_id_to_delete:
                                logger.info(f"准备删除 PinInfo 中的引脚 pin_id (不包含当前pin_id): {pin_id_to_delete}")
                                if pin_id_to_delete != pin_id:
                                    logger.info(f"删除 PinInfo 中的引脚 pin_id: {pin_id_to_delete}")
                                    self.delete_info(
                                        project_code,
                                        project_name,
                                        project_gitlab,
                                        project_branch,
                                        pin_id,
                                        chip_name,
                                        pin_id_to_delete
                                    )
                                else:
                                    logger.info(f"跳过 PinInfo 中的引脚 pin_id: {pin_id_to_delete}")
                            else:
                                logger.warning(f"PinInfo 项中未包含 pin_id 字段: {item}")
                        else:
                            logger.warning(f"PinInfo 项不是字典类型: {item}")
                else:
                    logger.warning(f"func['PinInfo'] 不是列表格式，实际类型为：{type(pininfo_list)}")



            # 找到则更新
            # 判断数据区分func和config
            func = dict()
            config = dict()
            for key, value in module_config.items():
                logger.info(f"key: {key}")
                group = self.get_litem_func(config_module, key)

                if group == "func":
                    func[key] = value
                elif group == "config":
                    config[key] = value
                elif group == "0":
                    pass
                else:
                    raise ValueError("数据标签不存在，请检查字段名称")

            obj.module = config_module
            obj.func = func
            obj.config = config
            obj.save()
            # 如果 config_module 中包含 "PinInfo" 则遍历其中的 pin_id
            if "PinInfo" in module_config:
                logger.info(f"标签 PinInfo 存在, 开始插入其他引脚的数据")
                self.insert_many_data(project_code, project_name, project_gitlab, project_branch,
                                      pin_id, chip_name, config_module, module_config, func, config)
            else:
                logger.info(f"成功更新值: {module_config}")
            return True

        except ChipPinConfig.DoesNotExist:
            # 插入新记录
            new_obj = ChipPinConfig(
                project_code=project_code,
                project_name=project_name,
                project_gitlab=project_gitlab,
                project_branch=project_branch,
                pin_id=pin_id,
                chip=chip_name,
                module=config_module
            )
            # 判断数据区分func和config
            func = dict()
            config = dict()
            for key, value in module_config.items():
                group = self.get_litem_func(config_module, key)

                if group == "func":
                    func[key] = value
                elif group == "config":
                    config[key] = value
                elif group == "0":
                    func[key] = value
                else:
                    raise ValueError("数据标签不存在，请检查字段名称")

            new_obj.func = func
            new_obj.config = config

            # 如果 config_module 中包含 "PinInfo" 则遍历其中的 pin_id
            logger.info("module_config:%s", module_config)
            if "PinInfo" in module_config:
                new_obj.save()
                logger.info(f"标签 PinInfo 存在, 开始插入其他引脚的数据")
                self.insert_many_data(project_code, project_name, project_gitlab, project_branch,
                                      pin_id, chip_name, config_module, module_config, func, config)
            else:
                new_obj.save()
                logger.info(f"插入新记录值为: {module_config}")

            return True

        except ChipPinConfig.MultipleObjectsReturned:
            raise ValueError("存在多条符合条件的记录，请确认唯一性")

        except Exception as e:
            logger.error(f"更新异常: {e}")
            return False

    def found_name(self, project_code, project_name, project_gitlab, project_branch, pin_id, chip_name, config_module):
        try:
            obj = ChipPinConfig.objects.using("hwcp").only("name").get(
                project_code=project_code,
                project_name=project_name,
                project_gitlab=project_gitlab,
                project_branch=project_branch,
                pin_id=pin_id,
                chip=chip_name,
                module=config_module
            )
            name = obj.name
            return name

        except ChipPinConfig.DoesNotExist:
            # 查询不到记录
            return None

        except ChipPinConfig.MultipleObjectsReturned:
            # 有多条记录时报错（.get 要求唯一）
            return None


    def get_litem_func(self, module: str, key: str) -> str:
        """
        判断给定 module 下，key 是属于 func 还是 config 字段
        :param module: 模块名，如 'GPIO'
        :param key: 要查找的 key，如 'name'
        :return: 'func' | 'config' | ''（未找到）
        """
        try:
            # 查询 func（JSON数组）中是否包含该 key
            in_func = ChipModule.objects.using("hwcp").filter(
                module=module,
                func__contains=[key]
            ).exists()

            # 查询 config（JSON对象）中是否存在该 key
            in_config = ChipModule.objects.using("hwcp").filter(
                module=module,
                config__has_key=key
            ).exists()

            if in_func:
                logger.info(f"模块 {module} 中，标签 {key} 属于 func")
                return "func"
            elif in_config:
                logger.info(f"模块 {module} 中，标签 {key} 属于 config")
                return "config"
            else:
                if key == "type":
                    logger.info(f"模块 {module} 中，普通标签 type 不属于 func 和 config")
                    return '0'
                elif key == "PinInfo":
                    logger.info(f"模块 {module} 中，标签 {key} 属于 func")
                    return "func"
                else:
                    logger.info(f"模块 {module} 中，标签 {key} 不属于 func 和 config")
                    return ""
        except Exception as e:
            logger.error(f"查询ChipModule失败: {e}")
            # 如果查询失败，使用默认逻辑
            # 根据常见的字段名称进行分类
            func_fields = ['name', 'des', 'type', 'work_model', 'alt_value', 'alt_values']
            if key in func_fields:
                logger.info(f"使用默认逻辑：模块 {module} 中，标签 {key} 属于 func")
                return "func"
            else:
                logger.info(f"使用默认逻辑：模块 {module} 中，标签 {key} 属于 config")
                return "config"



    def parse_module_config(self, querydict):
        module_config = {}

        # 先处理普通字段
        for key, value in querydict.items():
            if key.startswith("module_config") and "PinInfo" not in key:
                # 例如 module_config[name] -> name
                inner_key = key[len("module_config["):-1]
                module_config[inner_key] = value

        # 处理 PinInfo 数组
        pin_ids = querydict.getlist("module_config[PinInfo][pin_id]")
        pin_types = querydict.getlist("module_config[PinInfo][type]")

        pin_info_list = []
        for i in range(min(len(pin_ids), len(pin_types))):
            pin_info_list.append({
                "pin_id": pin_ids[i],
                "type": pin_types[i]
            })

        if pin_info_list:
            module_config["PinInfo"] = pin_info_list

        return module_config


    def updata_value(self, pin_id, func):
        if "PinInfo" not in func.keys():
            func["pin_id"] = pin_id
        else:
            pass
        return func



    def insert_type(self, project_code,project_name, project_gitlab,project_branch,config_module, module_config):
        try:
            first_item = MainSpecialEnum.objects.using("hwcp").get(
                type=config_module,
                project_code=project_code,
                project_name=project_name,
                project_gitlab=project_gitlab,
                project_branch=project_branch
            )

            logger.info(f"first_item:{first_item}")
            if module_config["name"] in first_item.enum_key.keys():
               return True
            else:
                updated_enum = first_item.enum_key.copy()
                updated_option = first_item.optional_key.copy()
                logger.info(f"module_config['channel']:{module_config['channel']}")
                if module_config["channel"]:
                    if config_module == "IIC":
                        updated_enum[module_config["name"]] = "IIC_INDEX_" + module_config["channel"]
                    elif config_module == "PWM":
                        updated_enum[module_config["name"]] = "PWM_INDEX_" + module_config["channel"]
                    elif config_module == "EXIT":
                        updated_enum[module_config["name"]] = "EXTI_INDEX_" + module_config["channel"]
                    elif config_module == "ADC":
                        updated_enum[module_config["name"]] = "ADC_INDEX_" + module_config["channel"]

                    if module_config["name"] not in updated_option:
                        updated_option.append(module_config["name"])
                else:
                    logger.info("没有通道号，跳过")


                first_item.enum_key = updated_enum
                first_item.optional_key = updated_option
                first_item.save(using="hwcp")  # 指定数据库连接保存

                return True


        except MainSpecialEnum.DoesNotExist:
            logger.info("没有记录，准备插入数据")
            new_spacia = MainSpecialEnum(
                project_code=project_code,
                project_name=project_name,
                project_gitlab=project_gitlab,
                project_branch=project_branch,
                type=config_module,
            )

            type_enum = dict()
            option = []
            if config_module == "IIC":
                type_enum[module_config["name"]] = "IIC_INDEX_" + module_config["channel"]
            elif config_module == "PWM":
                type_enum[module_config["name"]] = "PWM_INDEX_" + module_config["channel"]
            elif config_module == "EXIT":
                type_enum[module_config["name"]] = "EXTI_INDEX_" + module_config["channel"]
            elif config_module == "ADC":
                type_enum[module_config["name"]] = "ADC_INDEX_" + module_config["channel"]

            option.append(module_config["name"])

            new_spacia.enum_key = type_enum
            new_spacia.optional_key = option

            new_spacia.save()

            return True
        except Exception as e:
            logger.info("Exception: %s", e)
            return False

    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)

            project_code = params.get("project_code")
            project_name = params.get("project_name")
            project_gitlab = params.get("gitlab")
            project_branch = params.get("project_branch")
            branch_status = params.get('branch_status')
            chip_name = params.get("chip_name")
            pin_id = params.get("pin_number")
            config_module = params.get("function_type")
            workspace_path = params.get("workspace_path")

            module_config = self.parse_module_config(params)
            logger.info("module_config: %s", module_config)

            # module_config = params.get("module_config")

            sdk_path = os.path.dirname(workspace_path) + "/hiwaysdk_2.0/Application"


            # 字节地址
            if "device_address" in module_config:
                module_config['device_address'] = hex(int(module_config['device_address']))


            # 开启事务控制
            with transaction.atomic(using='hwcp'):
                logger.info("开始hwcp数据库事务")
                # 插入数据表
                i_res = self.insert_data(
                    project_code,
                    project_name,
                    project_gitlab,
                    project_branch,
                    pin_id,
                    chip_name,
                    config_module,
                    module_config
                )
                if not i_res:
                    raise ValueError("修改数据至数据库异常，请查看")
                logger.info("插入引脚数据成功")
                if config_module in ["IIC", "PWM", "EXIT", "ADC"]:
                    i_type = self.insert_type(
                        project_code,
                        project_name,
                        project_gitlab,
                        project_branch,
                        config_module,
                        module_config
                    )
                    if not i_type:
                        raise ValueError("插入类型数据至数据库异常，请查看")
                    logger.info("插入类型的引脚名称和复用值成功")

                func = dict()
                func["config"] = dict()

                # 判断数据区分func和config
                for key, value in module_config.items():
                
                    group = self.get_litem_func(config_module, key)

                    if group == "func":
                        func[key] = value
                    elif group == "config":
                        func["config"][key] = value
                    elif group == "0":
                        pass
                    else:
                        raise ValueError("数据标签不存在，请检查字段名称")
                # 拼接grpc请求数据
                item = self.updata_value(pin_id, func)
                logger.info("item: %s", item)
                logger.info("config_module: %s", config_module)
                if config_module not in ['UART', 'IIC', 'SPI']:
                    item['pin_id'] = item['PinInfo'][0]['pin_id']
                    del item['PinInfo']
                    logger.info("当功能类型不是多引脚关联类型时item: %s", item)
                # 请求grpc
                res = self.modify_chip_info(
                    sdk_path,
                    workspace_path,
                    branch_status,
                    config_module,
                    chip_name,
                    item,
                    pin_id
                )
                if res is None or (isinstance(res, dict) and res.get("config_status") != 1):
                    logger.error("gRPC调用失败，将回滚所有数据库操作")
                    raise ValueError("UpdateGPIOConfigItem 配置修改失败，状态码错误")


            logger.info("hwcp数据库事务成功提交")

            return Response({
                "status": 1,
                "message": "数据修改成功"
            })

        except Exception as e:
            logger.info("修改引脚数据失败: %s", str(e))
            return Response({"status": 0, "message": str(e)})

