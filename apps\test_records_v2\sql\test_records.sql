--测试记录表
CREATE TABLE IF NOT EXISTS public.test_records_v2(
    id varchar(255) primary key, --id
    project_number varchar(255),
    project_name varchar(255),
    test_plan_name varchar(255),
    test_plan_id int,
    tester_email varchar(255),
    tester_name varchar(255),
    machine_number varchar(255),
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.test_records_v2 OWNER TO atpms;

--测试记录项表
CREATE TABLE IF NOT EXISTS public.test_record_items_v2(
    id serial primary key, --id
    test_record_id varchar(255),
    "order" int,
    project_number varchar(255),
    project_name varchar(255),
    test_plan_name varchar(255),
    test_plan_id int,
    test_case_number varchar(255),
    test_case_name varchar(255),
    test_case_id int,
    test_case_version varchar(255),
    start_time timestamp ,
    end_time timestamp ,
    tester_email varchar(255),
    tester_name varchar(255),
    machine_number varchar(255),
    result bool default null,
    status int default 0, -- 状态  0 未处理 1 已处理
    raw text,  -- 原始值
    is_false_alarm bool default false,
    false_alarm_reasons text,
    is_manual_determinated bool default false,
    create_time timestamp, --创建时间
    update_time timestamp, --更新时间
    unique (test_record_id, "order")
);
ALTER TABLE public.test_record_items_v2 OWNER TO atpms;

--测试记录项关联外部资源
CREATE TABLE IF NOT EXISTS public.test_record_item_resources_v2(
    id serial primary key, --id
    test_record_item_id int,
    resource_name varchar(255),
    resource_path varchar(255),
    create_time timestamp --创建时间
);
ALTER TABLE public.test_record_item_resources_v2 OWNER TO atpms;


CREATE TABLE IF NOT EXISTS public.test_record_item_collect(
    id serial primary key, --id
    user_id int, -- 用户ID
    test_record_item_id int, -- 测试记录项ID
    UNIQUE(user_id, test_record_item_id) -- 用户和测试记录项的唯一组合
);
ALTER TABLE public.test_record_item_collect OWNER TO atpms;
