import logging
import traceback

from django.core.paginator import Paginator
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication

from utils.fs_service import FSService
from .models import StressTestModel
from .serializers import StressTestSerializer, StressTestUpdateSerializer

fs_service = FSService()
logger = logging.getLogger("stress_tests")


class StressTestView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = StressTestSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            page = serializer.validated_data.get("page", 1)
            pagesize = serializer.validated_data.get("pagesize", 10)
            project_number = serializer.validated_data.get("project_number")
            status_re = serializer.validated_data.get("status")

            obj_list = StressTestModel.objects.all()

            if project_number is not None and project_number != "":
                obj_list = StressTestModel.objects.filter(project_number__iregex=project_number)
            if status_re is not None and status_re != "":  # 仅在 status_re 有值时过滤
                obj_list = obj_list.filter(status=status_re)

            obj_list = obj_list.order_by("-id")
            paginator = Paginator(obj_list, pagesize)

            page_obj = paginator.get_page(page)

            content = {
                "count": paginator.count,
                "results": list(page_obj.object_list.values())
            }
            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = StressTestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user

            project_number = serializer.validated_data.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            model = StressTestModel()
            model.create(
                **serializer.validated_data,
                creator_email=user.email, creator_name=user.username, creator_employee_number=user.employee_number
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class StressTestDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = StressTestModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = StressTestUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = StressTestModel()
            model.update(pk=pk, **serializer.data)

            r = model.retrieve(pk=pk)

            if not r:
                return Response({"err_code": 1, "msg": "对应id测试用例不存在！"}, status.HTTP_400_BAD_REQUEST)

            # if r.get("creator_employee_number") != user.employee_number:
            #     return Response({"err_code": 1, "msg": "创建人不符合！"}, status.HTTP_400_BAD_REQUEST)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            model = StressTestModel()
            model.delete(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class StressTestSyncView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            t_id = request.data.get("id")
            if t_id is None:
                return Response({"err_code": 1, "msg": "id不能为空！"}, status.HTTP_400_BAD_REQUEST)

            model = StressTestModel()
            t = model.retrieve(pk=t_id)

            project_number = t.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            if not t:
                return Response({"err_code": 1, "msg": "id无效，对应计划不存在！"}, status.HTTP_400_BAD_REQUEST)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class StressTestStatusUpdateView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = StressTestUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = StressTestModel()
            model.update(pk=serializer.validated_data.get("id"), status=serializer.validated_data.get("status"))

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
