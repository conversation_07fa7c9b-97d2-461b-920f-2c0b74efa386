from rest_framework import serializers


class BugListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    test_case_number = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255)


class BugTestRecordAddSerializer(serializers.Serializer):
    bug_id = serializers.IntegerField()
    task_version = serializers.CharField()
    task_scheme = serializers.CharField()
    task_conclusion = serializers.CharField()
