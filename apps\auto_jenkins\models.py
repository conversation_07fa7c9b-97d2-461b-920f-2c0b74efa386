from django.db import models

class PipelineProject(models.Model):
    """Pipeline项目配置"""
    project_name = models.CharField(max_length=100, verbose_name="项目名称")
    git_url = models.URLField(verbose_name="Git仓库地址")
    branch = models.CharField(max_length=100, default='master', verbose_name="分支名称")
    engineering_group = models.CharField(max_length=50, verbose_name="工程组")  # MCU或python
    engineering_path = models.CharField(max_length=200, verbose_name="项目路径")  # 完整路径
    agent = models.CharField(max_length=50, default='slave_win10', verbose_name="Jenkins Agent")
    pipeline_config = models.JSONField(verbose_name="Pipeline配置")  # Vue-flow配置
    jenkinsfile_content = models.TextField(verbose_name="生成的Jenkinsfile")
    jenkins_job_name = models.Char<PERSON><PERSON>(max_length=100, verbose_name="Jenkins Job名称")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    creator = models.CharField(max_length=50, verbose_name="创建者")
    belong_project = models.CharField(max_length=100, default='', verbose_name="所属项目")
    flow_config = models.CharField(max_length=256, default='', verbose_name="流程配置")
    frontend_raw_config = models.JSONField(default=dict, verbose_name="前端原始配置")
    enable_webhook = models.BooleanField(default=False, verbose_name="启用WebHook")
    
    class Meta:
        db_table = 'pipeline_projects'
        managed = False
        app_label = 'auto_jenkins'
        verbose_name = 'Pipeline项目'
        verbose_name_plural = 'Pipeline项目'
        unique_together = [['git_url', 'branch']]

class PipelineBuild(models.Model):
    """Pipeline构建记录"""
    project = models.ForeignKey(PipelineProject, on_delete=models.CASCADE, verbose_name="项目")
    task_id = models.CharField(max_length=100, unique=True, verbose_name="任务ID")  # 用这个id 来追踪整个任务的执行情况 
    status = models.CharField(max_length=100, verbose_name="状态")  # starting, running, success, failed
    start_time = models.DateTimeField(verbose_name="开始时间")
    jenkins_url = models.CharField(max_length=200, verbose_name="Jenkins URL")
    upload_url = models.CharField(max_length=200, verbose_name="上传URL")     #  upload_exe  传过来的url
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="结束时间")
    error_message = models.TextField(null=True, blank=True, verbose_name="错误信息")
    created_by = models.CharField(max_length=100, null=True, blank=True, verbose_name="执行人")
    duration = models.DecimalField(null=True, blank=True, verbose_name="执行时长(秒)")
    
    
    class Meta:
          db_table = 'pipeline_builds'
          managed = False
          app_label = 'auto_jenkins'
          verbose_name = 'Pipeline构建表'
          verbose_name_plural = 'Pipeline构建表'
          ordering = ['-start_time']  # 按开始时间倒序