-- 表1：code_management
-- DDL generated by <PERSON>eaver
-- WARNING: It may differ from actual native database DDL
--CREATE TABLE information_schema.code_management (
--	id serial NOT NULL,
--	project_code varchar(50) NULL,
--	project_name varchar(120) NULL,
--	project_gitlab varchar(255) NULL,
--	project_branch varchar(50) NULL,
--	selected_chip varchar(120) NULL,
--	selected_modules _text NULL,
--	workspace varchar(255) NULL,
--	workgroup varchar(120) NULL,
--	project_description varchar(255) NULL,
--	version_rule varchar(255) NULL,
--	create_person varchar(120) NULL
--	PRIMARY KEY (id, project_gitlab, workspace)
--);
--ALTER TABLE public.code_management OWNER TO atpms;
--
--
---- 表2：code_management_project
---- DDL generated by DBeaver
---- WARNING: It may differ from actual native database DDL
--CREATE TABLE information_schema.code_management_project (
--	id serial NOT NULL,
--	project_code varchar(50) NULL,
--	project_name varchar(120) NULL,
--	project_gitlab _text NOT NULL,
--	create_person varchar(120) NULL,
--	create_time timestamp NULL
--	PRIMARY KEY (id, project_code, project_gitlab)
--);
--ALTER TABLE public.code_management_project OWNER TO atpms;






CREATE TABLE public.code_project_info (
    id serial,
    project_code varchar(50) NOT NULL,                -- 必填：项目代号
    project_name varchar(120) NOT NULL,               -- 必填：项目名称
    project_gitlab varchar(255) PRIMARY key NOT NULL,             -- 必填：GitLab 地址
    chip varchar(120),                                -- 可空：芯片类型
    modules text[],                                   -- 可空：模块数组
    project_space varchar(255),                       -- 可空：项目路径
    project_group varchar(120),                       -- 可空：所属组
    project_description varchar(255),                 -- 可空：描述
    version_rule varchar(255),                        -- 可空：版本策略
    create_person varchar(120)                        -- 可空：创建人
);

ALTER TABLE public.code_project_info OWNER TO atpms;


CREATE TABLE public.code_branch_info (
    id serial,
    project_code varchar(50) NOT NULL,                -- 必填：项目代号
    project_name varchar(120) NOT NULL,               -- 必填：项目名称
    project_gitlab varchar(255) NOT NULL,             -- 必填：GitLab 地址
    project_branch varchar(255)  NOT NULL,             -- 必填：分支名
    chip varchar(120),                                -- 可空：芯片
    modules text[],                                   -- 可空：模块数组
    branch_space varchar(255),                        -- 可空：分支路径
    project_group varchar(120),                       -- 可空：组名
    create_person varchar(120)                        -- 可空：创建人
);

ALTER TABLE public.code_branch_info OWNER TO atpms;
