import logging
import traceback

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.paginator import Paginator

from .serializers import (
    TagListSerializer, TagSerializer, TagUpdateSerializer
)
from .models import (
    TagModel
)

logger = logging.getLogger("machine")


class TagsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TagListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            page = serializer.validated_data.get("page", 1)
            pagesize = serializer.validated_data.get("pagesize", 10)

            obj_list = TagModel.objects.all()
            obj_list = obj_list.order_by("-id")

            paginator = Paginator(obj_list, pagesize)

            page_obj = paginator.get_page(page)

            content = {
                "count": paginator.count,
                "results": list(page_obj.object_list.values())
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            serializer = TagSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            TagModel.objects.create(**serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TagDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:

            obj = TagModel.objects.get(id=pk)

            return Response({"err_code": 0, "data": obj.to_dict()}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:

            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            serializer = TagUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            name = serializer.validated_data.get("name")
            desc = serializer.validated_data.get("desc")

            obj = TagModel.objects.get(id=pk)

            obj.name = name
            obj.desc = desc

            obj.save()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            result = TagModel.objects.get(id=pk)
            result.delete()

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
