--
CREATE TABLE IF NOT EXISTS public.testers(
    id serial PRIMARY KEY,
    "name" VA<PERSON><PERSON><PERSON>(255),
    email VARCHAR(255),
    department VARCHAR(255),
    "group" VARCHAR(255),
    work_start_date DATE,
    work_experience TEXT,
    status VARCHAR(255),
    create_time TIMESTAMP,
    update_time TIMESTAMP
);
ALTER TABLE public.testers OWNER TO atpms;


CREATE TABLE IF NOT EXISTS public.tester_roles(
    id serial PRIMARY KEY,
    "name" VARCHAR(255),
    code VARCHAR(255) UNIQUE,
    "desc" TEXT,
    create_time TIMESTAMP,
    update_time TIMESTAMP
);
ALTER TABLE public.tester_roles OWNER TO atpms;


CREATE TABLE IF NOT EXISTS public.tester_tester_role_map(
    id serial PRIMARY KEY,
    tester_id int REFERENCES public.testers(id) on delete cascade,
    role_id int REFERENCES public.tester_roles(id) on delete cascade,
    create_time TIMESTAMP,
    update_time TIMESTAMP
);
ALTER TABLE public.tester_tester_role_map OWNER TO atpms;

--创建测试技能表
CREATE TABLE IF NOT EXISTS public.tester_skills(
    id serial PRIMARY KEY,
    "name" VARCHAR(255),
    code VARCHAR(255) UNIQUE,
    type_id int REFERENCES public.tester_skill_types(id) on delete set null,
    "desc" TEXT,
    create_time TIMESTAMP,
    update_time TIMESTAMP
);
ALTER TABLE public.tester_skills OWNER TO atpms;

--创建测试技能类型表
CREATE TABLE IF NOT EXISTS public.tester_skill_types(
    id serial PRIMARY KEY,
    "name" VARCHAR(255),
    "desc" TEXT,
    create_time TIMESTAMP,
    update_time TIMESTAMP
);
ALTER TABLE public.tester_skill_types OWNER TO atpms;


CREATE TABLE IF NOT EXISTS public.tester_role_skill_map(
    id serial PRIMARY KEY,
    role_id int REFERENCES public.tester_roles(id) on delete cascade,
    skill_id int REFERENCES public.tester_skills(id) on delete cascade,
    max_score int,
    weight int,
    "order" int,
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    unique(role_id, skill_id)
);
ALTER TABLE public.tester_role_skill_map OWNER TO atpms;


CREATE TABLE IF NOT EXISTS public.tester_skill_scores(
    id serial PRIMARY KEY,
    tester_id INT REFERENCES public.testers(id) on delete cascade,
    role_id INT REFERENCES public.tester_roles(id) on delete cascade,
    skill_id INT REFERENCES public.tester_skills(id) on delete cascade,
    score int,
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    unique(tester_id, skill_id)
);
ALTER TABLE public.tester_skill_scores OWNER TO atpms;