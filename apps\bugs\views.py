import traceback
import logging

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from .serializers import (
    BugListSerializer, BugTestRecordAddSerializer,
)
from utils.fs_service import FSService
from users.models import UserFSInfo

logger = logging.getLogger("project")

fs_service = FSService()


class BugsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = BugListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            f, data = fs_service.get_bug_list(token=token, **serializer.validated_data)
            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            content = {
                "count": len(data.get("data")) if data.get("data") else [],
                "results": data.get("data") if data.get("data") else [],
            }

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class BugTestRecordAddView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = BugTestRecordAddSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            f, data = fs_service.add_bug_test_record(token=token, **serializer.validated_data)
            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
