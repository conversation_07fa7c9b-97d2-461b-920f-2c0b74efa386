

from django.db import models
from rest_framework import serializers
from django.contrib.postgres.fields import ArrayField

#
# 模型定义
#

class CodeProjectInfo(models.Model):
    id = models.AutoField(primary_key=True)
    project_code = models.CharField(max_length=50, verbose_name='项目代号')
    project_name = models.CharField(max_length=120, verbose_name='项目名称')
    sdk_version = models.CharField(max_length=50, blank=True, null=True, verbose_name='sdk 分支/标签信息')
    project_gitlab = models.CharField(max_length=255, primary_key=True, verbose_name='GitLab 地址')
    chip = models.CharField(max_length=120, blank=True, null=True, verbose_name='芯片类型')
    modules = ArrayField(models.JSONField(blank=True, null=True, verbose_name='功能模块'), default=list)
    project_space = models.Cha<PERSON><PERSON><PERSON>(max_length=255, blank=True, null=True, verbose_name='项目路径')
    project_group = models.CharField(max_length=120, blank=True, null=True, verbose_name='所属组')
    project_description = models.CharField(max_length=255, blank=True, null=True, verbose_name='项目描述')
    version_rule = models.CharField(max_length=255, blank=True, null=True, verbose_name='版本策略')
    create_person = models.CharField(max_length=120, blank=True, null=True, verbose_name='创建人')


    class Meta:
        db_table = 'code_project_info'
        app_label = 'code_project_info'
        managed = False



class ProjectInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = CodeProjectInfo
        fields = '__all__'



# 序列化器定义
class CodeBranchInfo(models.Model):

    id = models.AutoField(primary_key=True)
    project_code = models.CharField(max_length=50, verbose_name='项目代号')
    project_name = models.CharField(max_length=120, verbose_name='项目名称')
    project_gitlab = models.CharField(max_length=255, verbose_name='GitLab 地址')
    project_branch = models.CharField(max_length=255, verbose_name='分支名称')
    chip = models.CharField(max_length=120, blank=True, null=True, verbose_name='芯片类型')
    modules = ArrayField(models.JSONField(blank=True, null=True, verbose_name='功能模块'), default=list)
    branch_space = models.CharField(max_length=255, blank=True, null=True, verbose_name='分支空间')
    project_group = models.CharField(max_length=120, blank=True, null=True, verbose_name='所属组')
    create_person = models.CharField(max_length=120, blank=True, null=True, verbose_name='创建人')
    mr_iid = models.IntegerField(blank=True, null=True, verbose_name='merge_id')
    class Meta:
        db_table = 'code_branch_info'
        app_label = 'code_branch_info'
        managed = False



class BranchInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = CodeBranchInfo
        fields = '__all__'



