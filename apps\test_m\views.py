import traceback
import logging
import os
import uuid

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.files.storage import FileSystemStorage
from django.conf import settings

from .serializers import (
    TestCaseListSerializer, TestCaseSerializer, TestCaseFileDownLoadSerializer,
    TestCaseTypeListSerializer, TestCaseTypeSerializer, TestCaseTypeUpdateSerializer,
    TestCaseStatusListSerializer, TestCaseStatusSerializer, TestCaseStatusUpdateSerializer,
)
from .models import TestCaseModel, TestCaseTypeModel, TestCaseStatusModel

logger = logging.getLogger("machine")

test_case_fs = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, "test_cases"), base_url="/media/test_cases")


class TestCasesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestCaseListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseModel()
            content = model.list(**serializer.validated_data)

            for i in content["results"]:
                i["update_time"] = i["update_time"].strftime("%Y-%m-%d %H:%M:%S")
                i["tc_file"] = test_case_fs.url(i["tc_file"])

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)

        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    # def post(self, request):
    #     try:
    #         serializer = TestCaseSerializer(data=request.data)
    #         if not serializer.is_valid():
    #             return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)
    #         user = request.user
    #
    #         file = serializer.validated_data.get("tc_file")
    #         dir_name = str(uuid.uuid4())
    #         tc_file_path = test_case_fs.save(os.path.join(dir_name, file.name), file)
    #         version = serializer.validated_data.get("version")
    #         version_ = version
    #         model = TestCaseModel()
    #
    #         r = model.get(project_number=serializer.validated_data.get("project_number"))
    #         if r:
    #             version = serializer.validated_data.get("version")
    #             r_version = r.get("version")
    #             if version != r_version:
    #                 return Response(
    #                     {
    #                         "err_code": 1,
    #                         "msg": f"上传version({version})与记录中version({r_version})不符."
    #                     },
    #                     status.HTTP_400_BAD_REQUEST
    #                 )
    #             version_ += 1
    #
    #             fn = r.get("tc_file")
    #             dir_n = os.path.dirname(test_case_fs.path(fn))
    #             test_case_fs.delete(fn)
    #             os.rmdir(dir_n)
    #
    #         r = model.create(**serializer.validated_data, submitter=user.employee_number, tc_file_path=tc_file_path,
    #                          version_=version_)
    #         if r:
    #             r["update_time"] = r["update_time"].strftime("%Y-%m-%d %H:%M:%S")
    #
    #         return Response({"err_code": 0, "data": r, "msg": "ok"}, status.HTTP_200_OK)
    #     except Exception:
    #         logger.error(traceback.format_exc())
    #         return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = TestCaseModel()
            content = model.retrieve(pk=pk)

            if content:
                content["update_time"] = content["update_time"].strftime("%Y-%m-%d %H:%M:%S")
                content["tc_file"] = test_case_fs.url(content["tc_file"])

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseFileView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestCaseFileDownLoadSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            project_number = serializer.validated_data.get("project_number")

            r = TestCaseModel().get(project_number=project_number)
            if not r:
                return Response({"err_code": 1, "msg": "项目编号未上传测试文件"}, status.HTTP_400_BAD_REQUEST)

            tc_file_path = test_case_fs.url(r["tc_file"])

            data = {
                "tc_file_path": tc_file_path,
                "version": r.get("version"),
            }

            return Response({"err_code": 0, "data": data, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseTypesView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestCaseTypeListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseTypeModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = TestCaseTypeSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseTypeModel()
            model.create(**serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseTypeDetailView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = TestCaseTypeModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = TestCaseTypeUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseTypeModel()
            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            model = TestCaseTypeModel()
            model.delete(pk=pk)
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseStatusView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestCaseStatusListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseStatusModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = TestCaseStatusSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseStatusModel()
            model.create(**serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseStatusDetailView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = TestCaseStatusModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = TestCaseStatusUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseStatusModel()
            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            model = TestCaseStatusModel()
            model.delete(pk=pk)
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


