import logging
import traceback
import datetime

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from .serializers import (
    FunctionSerializer, FunctionListSerializer, FunctionUpdateSerializer,
)
from .models import (
    FunctionModel,
)

logger = logging.getLogger("machine")


class FunctionsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = FunctionListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = FunctionModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            # user = request.user
            # if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]:
            #     return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            serializer = FunctionSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = FunctionModel()
            model.create(**serializer.validated_data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class FunctionDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = FunctionModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            user = request.user
            if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            serializer = FunctionUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = FunctionModel()
            model.update(pk=pk, **serializer.data)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            # user = request.user
            # if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]:
            #     return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            model = FunctionModel()
            model.delete(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class FunctionDeprecateView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, pk=None):
        try:
            # user = request.user
            # if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]:
            #     return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            model = FunctionModel()
            model.update(pk=pk, deprecated=True)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseStatView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:

            model = FunctionModel()
            project_number = request.query_params.get("project_number")
            action_type = request.query_params.get("action_type")

            content = model.test_case_stat(project_number=project_number, action_type=action_type)

            return Response({"err_code": 0, "data": content}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)