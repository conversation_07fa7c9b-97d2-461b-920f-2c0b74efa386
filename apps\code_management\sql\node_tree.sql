
CREATE TABLE public.node_tree (
    id SERIAL PRIMARY KEY,
    parent_id INT REFERENCES node_tree(id) ON DELETE SET NULL,
    name VA<PERSON>HAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('module', 'var', 'func')),
    level VARCHAR(50) NOT NULL,
    des TEXT,
    node_path TEXT,
    file_path TEXT
);

-- 添加titles字段
ALTER TABLE public.node_tree
ADD COLUMN titles JSONB NOT NULL DEFAULT '[]'::JSONB;

ALTER TABLE node_tree
ALTER COLUMN parent_id TYPE INT,  -- 确保类型为整数
DROP CONSTRAINT IF EXISTS node_tree_parent_id_fkey;  -- 删除原外键约束
