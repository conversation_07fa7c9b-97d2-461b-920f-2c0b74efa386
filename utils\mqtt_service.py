import os
import json
import logging
import traceback

import paho.mqtt.client as mqtt

logger = logging.getLogger("mqtt")

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
CONF_PATH = os.path.join(BASE_DIR, "config", "config.json")

with open(CONF_PATH, "rb") as f:
    CONF = json.load(f)

MQTT_CONF = CONF.get("mqtt")

MQTT_SERVER = MQTT_CONF.get("mqtt_server")
MQTT_PORT = MQTT_CONF.get("mqtt_port")
MQTT_KEEPALIVE = MQTT_CONF.get("mqtt_keepalive")
MQTT_USER = MQTT_CONF.get("mqtt_user")
MQTT_PASSWORD = MQTT_CONF.get("mqtt_password")


def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        logger.info('Connected successfully')
        mqtt_client.subscribe('intelligentParking/inMessage')  # 订阅主题
        mqtt_client.subscribe('intelligentParking/event')  # 订阅主题
    else:
        logger.info('Bad connection. Code:', rc)


def on_message(mqtt_client, userdata, msg):
    logger.info(f'Received message on topic: {msg.topic} with payload: {msg.payload}')


def on_disconnect(mqtt_client, userdata, rc):
    logger.info('disconnected: %s', rc)


class MQTTService:
    def __init__(self):
        self.client = mqtt.Client()
        self.client.on_connect = on_connect
        self.client.on_message = on_message
        self.client.on_disconnect = on_disconnect
        self.client.username_pw_set(MQTT_USER, MQTT_PASSWORD)
        self.client.connect(
            host=MQTT_SERVER,
            port=MQTT_PORT,
            keepalive=MQTT_KEEPALIVE
        )
        self.client.loop_start()

    def publish(self, payload, topic="intelligentParking/inMessage", qos=2, retain=False):
        for i in range(3):
            try:
                rc, mid = self.client.publish(topic=topic, payload=payload, qos=qos, retain=retain)
                break
            except Exception:
                logger.error("推送mqtt消息出错。%s", traceback.format_exc())


mqtt_client = MQTTService()
