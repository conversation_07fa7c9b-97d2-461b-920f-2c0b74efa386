from rest_framework import serializers


class DBCListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    file_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    version = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    project_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    upload_user_name = serializers.Char<PERSON>ield(max_length=255, required=False, allow_blank=True, allow_null=True)


class DBCSerializer(serializers.Serializer):
    project_number = serializers.Char<PERSON>ield(max_length=255)
    project_name = serializers.CharField(max_length=255)
    project_id = serializers.Char<PERSON>ield(max_length=255)
    file = serializers.FileField()
    description = serializers.Char<PERSON><PERSON>(required=False, allow_blank=True, allow_null=True)
    tags = serializers.CharField(required=False, allow_blank=True, allow_null=True)


class DBCUpdateSerializer(serializers.Serializer):
    description = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    tags = serializers.CharField(required=False, allow_blank=True, allow_null=True)


class Signal2MessageSerializer(serializers.Serializer):
    dbc_id = serializers.IntegerField()
    message = serializers.JSONField()
