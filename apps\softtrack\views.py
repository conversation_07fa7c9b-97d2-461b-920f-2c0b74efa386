import gitlab
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.pagination import PageNumberPagination
from django.core.cache import cache
from .models import SoftwareTrack, SoftwareVersionProfile
from pathlib import PurePosixPath
import logging
import grpc
from utils.proto import logdir_pb2_grpc
from utils.proto import logdir_pb2
from utils.fs_app import fs_app
from utils.fs_app import fs_app
from users.models import User
from apps.code_management.views.config_views import get_user_email
from apps.code_management.models.project_models import CodeBranchInfo
from rest_framework import status
from django.utils import timezone



logger = logging.getLogger("softtrack")


GITLAB_URL = "http://10.1.1.99"   
JENKINS_TOKEN= "**************************"

class SoftTrackPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class SoftTrackView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = SoftTrackPagination

    def calculate_main_version(self,project,version_meta):
        """
        根据逻辑计算主版本号
        """
        today = timezone.now().date()   # 当前日期（不带时分秒）
        values = [item["value"] for item in version_meta]
        version_str = "_".join(values)
        today_str = today.strftime("%Y%m%d")
        main_version_start = f"{version_str}{today_str}"
        
        latest = (
        SoftwareTrack.objects.filter(
            project=project,
            creation_time__date=today,
            main_version__startswith=main_version_start
          )
            .order_by("-creation_time")
            .first()
        )
        if not latest:                                     #当天没有直接返回_1
            return f"{main_version_start}_1"
        latest_version = latest.main_version              # 如果有的话呢 我们直接取最新的+1
        parts = latest_version.split("_")
        if parts[-1].isdigit():
            return f"{main_version_start}_{int(parts[-1]) + 1}"

    def get(self, request):
        logger.info("SoftTrackView GET function called")

        paginator = self.pagination_class()
        queryset = SoftwareTrack.objects.all().order_by('-creation_time')
        project_filter = request.GET.get('project')
        if project_filter:
            queryset = queryset.filter(project__contains=project_filter)
            logger.info(f"Filtering by project: {project_filter}")
        
        page = paginator.paginate_queryset(queryset, request)
        
        results = []
        for item in page:
            result = {
                'id': item.id,
                'creation_time': item.creation_time.strftime('%Y-%m-%d %H:%M:%S'),
                'project': item.project,
                'software_type': item.software_type,
                'main_version': item.main_version,
                'status': item.status,
                'creator': item.creator,
                'engineering_group': item.engineering_group,
                'engineering_path': item.engineering_path,
                'source_branch': item.source_branch,
                'target_branch': item.target_branch,
                'change_description': item.change_description,
                'version_meta': item.version_meta,
                'version_usage': item.version_usage,
            }
            results.append(result)
        
        return paginator.get_paginated_response(results)

    def post(self, request):
        logger.info(f"SoftTrackView POST request received: {request.data=}")
        data = request.data
        software_type = data.get('software_type')
        version_fields = data.get('version_fields', {})
        version_usage = data.get('version_usage')
        profile = SoftwareVersionProfile.objects.filter(version_type=software_type).first() # 查找对应的SoftwareVersionProfile
        version_meta = profile.version_meta.copy() if profile.version_meta else []
        for item in version_meta:
            code = item.get('code')
            if code in version_fields:
                item['value'] = version_fields[code]
            
        track_data = {
            'project': data.get('project'),
            'software_type': data.get('software_type'),
            'change_description': data.get('change_description'),
            'engineering_group': data.get('engineering_group'),
            'engineering_path': data.get('engineering_path'),
            'source_branch': data.get('source_branch'),
            'target_branch': data.get('target_branch'),
            'status': data.get('status'),
            'creator': request.user.username if request.user.is_authenticated else '',
            'version_meta': version_meta,
            'version_usage': version_usage,
        }
        main_version = self.calculate_main_version(data.get('project'),version_meta)  # 计算主版本的逻辑
        track_data['main_version'] = main_version
        user = request.user  
        persons_email, leader_email = get_user_email(user)  # 获取当前人，以及当前人员上级得邮箱
        try:
            track = SoftwareTrack(**track_data)
            engineering_group = data.get('engineering_group')              # 获取相关参数
            engineering_path = data.get('engineering_path')
            source_branch = data.get('source_branch')
            target_branch = data.get('target_branch')
            creator = request.user.username if request.user.is_authenticated else ''
            change_description = data.get('change_description')
            
            # 发送MR和飞书通知
            mr_success, mr_result = send_message_to_fs(
                engineering_group, 
                engineering_path, 
                source_branch, 
                target_branch, 
                leader_email,
                creator,
                change_description
            )
            if mr_success and 'mr_iid' in mr_result:
                track.mr_iid = mr_result['mr_iid']
                logger.info(f"已保存 MR_IID: {mr_result['mr_iid']} 到记录 {track.id}")
            else:
                logger.info(f"创建mr_iid失败")
            track.save()
            
            response_data = {"message": "添加成功", "id": track.id, "code": 200}
            if mr_success:
                response_data.update({
                    "mr_info": mr_result,
                    "mr_created": True
                })
            else:
                response_data.update({
                    "mr_error": mr_result,
                    "mr_created": False
                })
            return Response(response_data)
        except Exception as e:
            logger.error(f"版本提测添加失败 track: {str(e)}")
            return Response({"message": f"添加失败: {str(e)}", "code": 500})

    def delete(self, request):
        logger.info(f"SoftTrackView DELETE request received: {request.data=}")
        id = request.query_params.get('id')
        if not id:
            return Response({"message": "缺少id参数", "code": 400}, status=400)
        try:
            track = SoftwareTrack.objects.get(id=id)
            track.delete()
            logger.info(f"软件提测记录删除成功. ID: {id}")
            return Response({"message": "删除成功", "code": 200})
        except SoftwareTrack.DoesNotExist:
            logger.error(f"删除失败，未找到ID: {id}")
            return Response({"message": "未找到该记录", "code": 404}, status=404)
        except Exception as e:
            logger.error(f"删除失败: {str(e)}")
            return Response({"message": f"删除失败: {str(e)}", "code": 500}, status=500)



def get_all_projects_recursive(gl, group_id):
    all_projects = []
    visited_groups = set()

    def fetch_group_projects(group_id):
        if group_id in visited_groups:
            return
        visited_groups.add(group_id)

        group = gl.groups.get(group_id)
        page = 1
        while True:
            projects = group.projects.list(page=page, per_page=100)
            if not projects: #  这个就代表不是最后一层  
                break
            for group_project in projects:
                project = gl.projects.get(group_project.id)
                branches = project.branches.list()
                branch_names = [branch.name for branch in branches]
                all_projects.append({
                    "project": project.name,
                    "path": group_project.path_with_namespace,  # ←需要知道指定的层级   可能是递归的
                    "branch": branch_names
                })
            page += 1
        subgroups = group.subgroups.list()
        for subgroup in subgroups:
            fetch_group_projects(subgroup.id)
    fetch_group_projects(group_id)
    return all_projects



class GitlabView(APIView):
  #  authentication_classes = [JWTAuthentication]
  #  permission_classes = [IsAuthenticated]

    def get(self, request):
        engineering_group = request.query_params.get('engineering_group')
        cache_key = f"gitlab:projects:{engineering_group}"

        # 先查 Redis 缓存（使用 Django cache）
        if engineering_group:
            cached_data = cache.get(cache_key)
            if cached_data:
                logger.info(f"Cache hit for {engineering_group}")
                return Response({
                    "engineering_group": engineering_group,
                    "projects": cached_data
                })
  
        result = list()

        # 如果缓存未命中，从 GitLab 拉取数据
        if engineering_group:
            try:
                gl = gitlab.Gitlab(url=GITLAB_URL, private_token=JENKINS_TOKEN)
                group = gl.groups.get(engineering_group)
                result = get_all_projects_recursive(gl, group.id)
                
                logger.info(f"Retrieved {len(result)} projects for engineering group: {engineering_group}")

                # 存入 Redis，缓存 7 天
                cache.set(cache_key, result, timeout=7 * 24 * 3600)

            except Exception as e:
                logger.error(f"Error retrieving gitlab projects: {str(e)}")
                return Response({"message": f"获取项目失败: {str(e)}", "code": 500})

        return Response({
            "engineering_group": engineering_group,
            "projects": result
        })


class MergeRequestView(APIView):
        authentication_classes = [JWTAuthentication]
        permission_classes = [IsAuthenticated]

        def post(self, request):
            data = request.data
            channel = grpc.insecure_channel("localhost:50051")
            gitlab_stub = logdir_pb2_grpc.GitlabServiceStub(channel)
            engineering_group = data["engineering_group"]
            engineering_path = data["engineering_path"]
            cache_key = f"gitlab:projects:{engineering_group}"
            cached_data = cache.get(cache_key)
            if cached_data:
                for item in cached_data:
                    if item['project'] == engineering_path:
                        engineering_path = item['path'] 
                        break
            request = logdir_pb2.CreateOrGetMergeRequestRequest(
                engineering_path = engineering_path,    # path mcu-team/alps/icscn22_alps    project ICSCN22_ALPS
                source_branch = data["source_branch"],
                target_branch = data["target_branch"],
            )
            gitlab_response = gitlab_stub.CreateOrGetMergeRequest(request)
            return Response({
                "message": "MR创建成功",
                "mr_iid":     gitlab_response.mr_iid,
                "project":    gitlab_response.project_path,
                "merge_status": gitlab_response.merge_status,  # checking / can_be_merged / cannot_be_merged
                "code":       gitlab_response.grpc_status_code,
                "mr_url":     gitlab_response.mr_url
            }, status=202)



class MergeRequestStatusView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):     # get 接口是查能否合并
            project_path = request.query_params.get("project_path")
            mr_iid = request.query_params.get("mr_iid")
            engineering_group = request.query_params.get("engineering_group")
            cache_key = f"gitlab:projects:{engineering_group}"
            cached_data = cache.get(cache_key)
            if cached_data:
                for item in cached_data:
                    if item['project'] == project_path:
                        project_path = item['path'] 
                        break
            if not project_path or not mr_iid or not engineering_group: 
                return Response({
                    "message": "参数不全",
                    "code": 400
                }, status=400)
            
            try:
                channel = grpc.insecure_channel("localhost:50051")
                stub = logdir_pb2_grpc.GitlabServiceStub(channel)
                grpc_request = logdir_pb2.QueryMRStatusRequest(
                project_path=project_path,
                mr_iid=int(mr_iid)
                )
                grpc_response = stub.QueryMRStatus(grpc_request)
            except Exception as e:
                logger.error(f" MergeRequestStatusView get  gRPC调用失败: {e}")
                return Response({
                    "message": f"gRPC调用失败: {e}",
                    "code": 500
                }, status=500)

            return Response({
                    "mr_iid": grpc_response.mr_iid,
                    "merge_status": grpc_response.merge_status,
                    # "merge_status": "can_be_merged",  # 测试
                   "state": grpc_response.state,
                    # "state": "opened",  # 测试
                    "message": grpc_response.message,
                    "code": grpc_response.code,
                }, status=200 if grpc_response.code == 200 else 500)


    def post(self, request):
        project_path = request.data.get("project_path")
        engineering_group = request.data.get("engineering_group")
        mr_iid = request.data.get("mr_iid")
        id = request.data.get("id")
        cache_key = f"gitlab:projects:{engineering_group}"
        cached_data = cache.get(cache_key)
        if cached_data:
            for item in cached_data:
                if item['project'] == project_path:
                    project_path = item['path'] 
                    break
        if not project_path or not mr_iid:
            return Response({"message": "缺少参数"}, status=400)
        try:
            channel = grpc.insecure_channel("localhost:50051")
            stub = logdir_pb2_grpc.GitlabServiceStub(channel)
            grpc_request = logdir_pb2.MergeMRRequest(
                project_path=project_path,
                mr_iid=int(mr_iid)
            )
            grpc_response = stub.MergeMR(grpc_request)
        except Exception as e:
            logger.error(f" post MergeMR gRPC调用失败: {e}")
            return Response({"message": f"gRPC调用失败: {e}"}, status=500)

        # 转为 REST 响应 
        if grpc_response.code == 200:  # 200就是已经合并了 就需要改状态   
             obj = SoftwareTrack.objects.get(id=id)
             obj.status = "已审核"
             obj.merge_commit_hash = grpc_response.commit_id    # 存储merge commit hash
             obj.save()
             logger.info(f"MR 项目 {project_path} 已合并，状态已更新为已审核")
        return Response({
            "mr_iid": grpc_response.mr_iid,
            "state": grpc_response.state,
            "message": grpc_response.message,
            "code": grpc_response.code,
        }, status=200 if grpc_response.code == 200 else 202)


class VersionByHashView(APIView):
    authentication_classes = []  # Jenkins 调用 不需要认证
    permission_classes = []
    
    def post(self, request):
        logger.info(f"VersionByHashView POST request received: {request.data=}")
        
        hash_value = request.data.get('hash_value')
        if not hash_value:
            return Response({"message": "缺少合并哈希值参数", "code": 400}, status=400)
        
        try:
            # 根据哈希值查找对应的记录
            track = SoftwareTrack.objects.filter(merge_commit_hash=hash_value).first()
            
            if not track: 
                return Response({"message": "未找到对应哈希值的版本记录", "code": 404}, status=404)
            
            result = {
                "main_version": track.main_version,
                "version_meta": track.version_meta,
                "code": 200
            }
            logger.info(f"版本信息查询成功. Hash: {hash_value}")
            return Response(result)
        except Exception as e:
            logger.error(f"版本信息查询失败: {str(e)}")
            return Response({"message": f"查询失败: {str(e)}", "code": 500}, status=500)


class GitlabWebhookView(APIView):
    authentication_classes = []  # GitLab 调用，无需认证
    permission_classes = []

    def post(self, request):
        """
        1. 解析项目 path_with_namespace
        2. 提取 MR IID
        3. 判断是否已合并 (action == 'merge' 且 state == 'merged')
        4. 获取源 / 目标分支
        5. 如果已合并，拿到 merge_commit_sha
        """
        data = request.data
        logger.info(f"GitlabWebhookView received: {data=}")
        project_path = data.get("project", {}).get("path_with_namespace")
        attrs        = data.get("object_attributes", {})

        mr_iid       = attrs.get("iid")                    # MR ID
        source_branch= attrs.get("source_branch")          # 源分支
        target_branch= attrs.get("target_branch")          # 目标分支
        action       = attrs.get("action")                 # open / update / merge / close
        state        = attrs.get("state")                  # opened / merged / closed
        merge_sha    = attrs.get("merge_commit_sha")       # 合并成功的 Hash（合并完成时才有值）


        is_merged = (action == "merge" and state == "merged")

        logger.info(
            f"MR !{mr_iid} | proj={project_path} | {source_branch}→{target_branch} | "
            f"action={action} state={state} merged={is_merged}"
        )
        if is_merged and target_branch == "release": # 就说明是推送到release 
             logger.info(f"合并的hash值是{merge_sha}")
             track = SoftwareTrack.objects.filter(
                 engineering_path=project_path,target_branch=target_branch,status="待审核",mr_iid=mr_iid   # 精确匹配这个MR
                 ).order_by("-creation_time").first()
             if track:
                 track.merge_commit_hash = merge_sha
                 track.status = "已审核"
                 track.save()  
                 logger.info(f"找到待审核记录: {track.id},记录下当前的hash值{merge_sha},并更改状态")
             else:
                 logger.warning(f"未找到项目 {project_path} 的待审核记录")
        if is_merged and target_branch == "dev":
            logger.info(f"当前mege请求是sdk的开发分支合入")
            res = send_merge_fs_info(mr_iid, project_path, source_branch, target_branch)
            if res:
                logger.info("当前sdk的开发分支合入完成")
            else:
                logger.info("当前sdk的开发分支合入错误，请分析")
        return Response({
            "project_path"   : project_path,
            "mr_iid"         : mr_iid,
            "source_branch"  : source_branch,
            "target_branch"  : target_branch,
            "is_merged"      : is_merged,
            "merge_commit_sha": merge_sha,
            "code"           : 200
        }, status=200)
    

def send_message_to_fs(engineering_group, engineering_path, source_branch, target_branch, creator_email,creator,change_description):
    """
    发送消息推送给飞书
    1. 创建MR
    2. 发送飞书通知
    """
    try:
        channel = grpc.insecure_channel("localhost:50051")
        gitlab_stub = logdir_pb2_grpc.GitlabServiceStub(channel)
        
        # 路径转换
        original_path = engineering_path
        # 创建MR请求
        request = logdir_pb2.CreateOrGetMergeRequestRequest(
            engineering_path=engineering_path,
            source_branch=source_branch,
            target_branch=target_branch,
        )
        
        # 调用gRPC创建MR
        gitlab_response = gitlab_stub.CreateOrGetMergeRequest(request)
        
        # 检查MR创建是否成功
        if gitlab_response.merge_status != "":
            # 发送飞书通知
            success, result = fs_app.send_mr_notification_msg(
                receive_email=creator_email,
                project_path=original_path,  # 使用原始项目名
                source_branch=source_branch,
                target_branch=target_branch,
                mr_url=gitlab_response.mr_url,
                user=creator,
                changeDescription=change_description,
                leader=True
            )
            
            if success:
                logger.info(f"MR创建成功并发送飞书通知 - MR IID: {gitlab_response.mr_iid}, 项目: {original_path}")
                return True, {
                    "mr_iid": gitlab_response.mr_iid,
                    "mr_url": gitlab_response.mr_url,
                    "project_path": gitlab_response.project_path,
                    "notification_sent": True
                }
            else:
                logger.warning(f"MR创建成功但飞书通知发送失败 - MR IID: {gitlab_response.mr_iid}, 错误: {result}")
                return True, {
                    "mr_iid": gitlab_response.mr_iid,
                    "mr_url": gitlab_response.mr_url,
                    "project_path": gitlab_response.project_path,
                    "notification_sent": False,
                    "notification_error": result
                }
        else:
            logger.error(f"MR创建失败 - 状态码: {gitlab_response.grpc_status_code}, 项目: {engineering_path}")
            return False, {
                "error": f"MR创建失败,状态码: {gitlab_response.grpc_status_code}",
                "message": getattr(gitlab_response, 'message', '未知错误')
            }
            
    except Exception as e:
        logger.error(f"发送MR和飞书通知异常: {str(e)}")
        return False, {"error": f"操作失败: {str(e)}"}

    finally:
        try:
            channel.close()
        except:
            pass


def send_merge_fs_info(mr_iid, project_path, source_branch, target_branch):
    try:
        # 获取提交用户
        project = CodeBranchInfo.objects.filter(
            project_branch=source_branch,
            mr_iid=mr_iid
        )
        logger.info("插入mr_iid后, 在分支信息表中查询到的数据:%s", project)
       # 根据用户名获取工号
        user_project = User.objects.filter(username=project.create_person)
        user = user_project.employee_number
        logger.info("当前创建人的工号:%s", user)
        # 获取上级邮箱
        persons_email, leader_email = get_user_email(user)
        logger.info("persons_email: %s, leader_email: %s", persons_email, leader_email)
        # 查询项目分支信息
        project_qs = CodeBranchInfo.objects.filter(
            project_branch=source_branch,
            mr_iid=mr_iid
        )

        if not project_qs.exists():
            logger.warning("未在分支信息表中找到对应数据: project_branch=%s, mr_iid=%s", source_branch, mr_iid)
        else:
            logger.info("插入mr_iid后, 在分支信息表中查询到的数据: %s", list(project_qs.values()))

            # 获取创建人（去重后应该只有一个）
            create_person_list = project_qs.values_list('create_person', flat=True).distinct()
            create_person = create_person_list[0] if create_person_list else None

            if create_person:
                logger.info("分支创建人: %s", create_person)

                # 查询用户工号
                user_qs = User.objects.filter(username=create_person)
                employee_number_list = user_qs.values_list('employee_number', flat=True).distinct()
                employee_number = employee_number_list[0] if employee_number_list else None

                logger.info("当前创建人的工号: %s", employee_number)

                if employee_number:
                    # 获取邮箱信息
                    persons_email, leader_email = get_user_email(employee_number)
                    logger.info("用户邮箱: %s, 上级邮箱: %s", persons_email, leader_email)
                else:
                    logger.warning("未找到用户工号信息")
            else:
                logger.warning("未找到 create_person 信息")


        logger.info("persons_email: %s, leader_email: %s", persons_email, leader_email)
        person_result, person_response = fs_app.send_branch_merge_msg(
            leader_email,
            project_path,
            source_branch,
            target_branch,
            project_path
        )
        # 删除mr_iid
        new_project = CodeBranchInfo.objects.get(
            project_branch=source_branch,
            mr_iid=mr_iid
        )
        new_project.mr_iid = None
        new_project.save()
        logger.info("删除mr_iid")
        if person_result:
            return True
        else:
            return False

    except Exception as e:
        logger.info("meger流程失败: %s", str(e))
        return False




class VersionInfo(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            profiles = SoftwareVersionProfile.objects.all()
            result = []
            for profile in profiles:
                result.append({
                    "id": profile.id,
                    "version_type": profile.version_type,
                    "component_code": profile.component_code,
                    "version_meta": profile.version_meta,
                    "is_upgradeable": profile.is_upgradeable,
                    "is_writable_online": profile.is_writable_online,
                    "created_at": profile.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "updated_at": profile.updated_at.strftime("%Y-%m-%d %H:%M:%S")
                })
            result = sorted(result, key=lambda x: x["id"])
            
            return Response({"err_code": 0, "data": result, "msg": "ok"}, status.HTTP_200_OK)
        except Exception as e :
            logger.error(str(e))
            return Response({"err_code": 2, "msg": "服务器错误"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

