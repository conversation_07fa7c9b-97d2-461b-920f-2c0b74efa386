from rest_framework import serializers


class ExceptionListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    run_id = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    code = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    psn = serializers.Char<PERSON>ield(max_length=255, required=False, allow_blank=True, allow_null=True)
    value = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    project_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class ExceptionSubmitSerializer(serializers.Serializer):
    run_id = serializers.Char<PERSON>ield(max_length=255)
    code = serializers.Char<PERSON>ield(max_length=255)
    name = serializers.CharField(max_length=255)
    psn = serializers.CharField(max_length=255)
    status = serializers.BooleanField()
    value = serializers.CharField(max_length=255)
    extra_info = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    cur_time = serializers.DateTimeField()
    project_name = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255)
    test_plan_name = serializers.CharField(max_length=255)
    test_plan_id = serializers.IntegerField()


class SendExceptionMsgSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)
    test_plan_name = serializers.CharField(max_length=255)
    code = serializers.CharField(max_length=255)
    name = serializers.CharField(max_length=255)
    cur_time = serializers.DateTimeField()
    app_name = serializers.CharField(max_length=255)
    station_name = serializers.CharField(max_length=255)
