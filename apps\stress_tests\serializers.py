from rest_framework import serializers
from .models import StressTestModel
import json


class StressTestSerializer(serializers.Serializer):
    verification = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    project_number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    name = serializers.CharField(max_length=255, required=False)
    project_number_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    page = serializers.IntegerField(required=False, default=1)
    pagesize = serializers.IntegerField(required=False, default=10)
    status = serializers.IntegerField(default=None, required=False, allow_null=True)

class StressTestUpdateSerializer(serializers.ModelSerializer):
    verification = serializers.Char<PERSON>ield(required=False, allow_blank=True, allow_null=True)
    project_number = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)
    name = serializers.CharField(max_length=255, required=False)
    status = serializers.IntegerField(default=None, required=False, allow_null=True)
    id = serializers.IntegerField(required=False)  # 添加 id 字段

    class Meta:
        model = StressTestModel
        fields = ['verification', 'status', 'name', 'project_number', 'id']

