import json

from django.db import models
from rest_framework.serializers import ModelSerializer
from rest_framework import serializers

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone


class ProcessMonitor(models.Model):
    run_id = models.Char<PERSON>ield(max_length=255)
    code = models.Char<PERSON>ield(max_length=255)
    name = models.Char<PERSON>ield(max_length=255)
    psn = models.CharField(max_length=255)
    status = models.BooleanField()
    value = models.CharField(max_length=255)
    extra_info = models.TextField(blank=True, null=True)
    cur_time = models.DateTimeField()
    project_name = models.Char<PERSON>ield(max_length=255)
    project_number = models.Char<PERSON><PERSON>(max_length=255)
    test_plan_name = models.CharField(max_length=255)
    test_plan_id = models.IntegerField()
    tester_name = models.CharField(max_length=255)
    tester_email = models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)
    create_time = models.DateT<PERSON><PERSON>ield(auto_now_add=True)

    class Meta:
        db_table = 'process_monitor'
        managed = False
        app_label = 'process_monitor'


class ProcessMonitorSerializer(ModelSerializer):
    cur_time = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S")

    class Meta:
        model = ProcessMonitor
        fields = "__all__"


class ProcessMonitorModel:
    def create(self, **kwargs):
        ProcessMonitor.objects.create(
            run_id=kwargs.get("run_id"),
            code=kwargs.get("code"),
            name=kwargs.get("name"),
            psn=kwargs.get("psn"),
            status=kwargs.get("status"),
            value=kwargs.get("value"),
            extra_info=json.dumps(kwargs.get("extra_info")),
            cur_time=kwargs.get("cur_time"),
            project_name=kwargs.get("project_name"),
            project_number=kwargs.get("project_number"),
            test_plan_name=kwargs.get("test_plan_name"),
            test_plan_id=kwargs.get("test_plan_id"),
            tester_name=kwargs.get("tester_name"),
            tester_email=kwargs.get("tester_email")
        )

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        objs = ProcessMonitor.objects.all()

        run_id = kwargs.get("run_id")
        if run_id:
            objs = objs.filter(run_id__contains=run_id)
        code = kwargs.get("code")
        if code:
            objs = objs.filter(code__contains=code)
        name = kwargs.get("name")
        if name:
            objs = objs.filter(name__contains=name)
        psn = kwargs.get("psn")
        if psn:
            objs = objs.filter(psn__contains=psn)
        value = kwargs.get("value")
        if value:
            objs = objs.filter(value__contains=value)
        project_number = kwargs.get("project_number")
        if project_number:
            objs = objs.filter(project_number=project_number)

        count = objs.count()

        objs = objs.order_by("-id")
        objs = objs[(page - 1) * pagesize: page * pagesize]

        results = ProcessMonitorSerializer(objs, many=True).data

        return {"count": count, "results": results}

    def retrieve(self, pk):
        result = ProcessMonitorSerializer(ProcessMonitor.objects.get(pk=pk)).data
        return result
