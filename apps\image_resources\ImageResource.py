# -*-coding:utf-8 -*-
"""
# Author     : jid
# Time       : 2024/11/18 17:56
# Description: 图片资源库表
"""
import operator

from PIL import Image
from django.db import models
from django.db.models import Char<PERSON>ield, DateTimeField, IntegerField, TextField


class ImageResource(models.Model):
    TYPE_CHOICE = (
        (0, u"特殊图片"),
        (1, u"灰阶图片")
    )

    name = CharField(max_length=255, verbose_name=u"图片名称")
    type = IntegerField(default=0, choices=TYPE_CHOICE, verbose_name=u'图片类型')
    image = CharField(max_length=255, blank=True, verbose_name=u'图片原始图')
    thumbnail = CharField(max_length=255, blank=True, verbose_name=u'图片缩略图')
    index = IntegerField(default=0, verbose_name=u'图片索引')
    rgb = CharField(max_length=255, verbose_name=u'图片RGB色值')
    desc = TextField(max_length=255, verbose_name=u'图片描述')
    create_time = DateTimeField(auto_now_add=True, verbose_name=u'创建时间')
    update_time = DateTimeField(auto_now=True, verbose_name=u'更新时间')

    class Meta:
        app_label = 'image_resources'
        db_table = 'image_resources'
        managed = False

    def to_dict(self):
        from image_resources.views import image_resource_fs
        # 这里返回给前端的缩略图路径thumbnail应该是全路径的
        if self.image is None or operator.eq("", self.image):
            image = ""
        else:
            image = image_resource_fs.url(self.image)

        if self.thumbnail is None or operator.eq("", self.thumbnail):
            thumbnail = ""
        else:
            thumbnail = image_resource_fs.url(self.thumbnail)

        return {
            "name": self.name,
            "type": self.type,
            "image": image,
            "thumbnail": thumbnail,
            "index": self.index,
            "rgb": self.rgb,
            "desc": self.desc,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S"),
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S")
        }

    def completion_image_path(self):
        from image_resources.views import image_resource_fs
        if self.image is None or operator.eq("", self.image):
            image = ""
        else:
            image = image_resource_fs.url(self.image)
        return image

    def completion_thumbnail_path(self):
        from image_resources.views import image_resource_fs
        if self.thumbnail is None or operator.eq("", self.thumbnail):
            thumbnail = ""
        else:
            thumbnail = image_resource_fs.url(self.thumbnail)
        return thumbnail

    @staticmethod
    def generate_thumbnail(input_image_path, output_image_path, thumbnail_size=(128, 128)):
        """
        生成图片的缩略图
        :param input_image_path: 原始图片的路径
        :param output_image_path: 生成的缩略图保存的路径
        :param thumbnail_size: 缩略图的大小，格式为(width, height)
        """
        print(input_image_path, output_image_path)
        # 打开原始图片
        with Image.open(input_image_path) as img:
            img.thumbnail(thumbnail_size)
            # 保存缩略图
            img.save(output_image_path)
