"""
URL configuration for atpms project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),

    # 用户
    path("users", include("users.urls")),
    # 机台管理
    path("machines", include("machines.urls")),
    # 自动化测试
    path("auto_test", include("auto_test.urls")),
    # 飞书事件和回调
    path("fs", include("feishu.urls")),
    # 项目管理
    path("projects", include("projects.urls")),
    # 测试问题管理
    path("issues", include("issues.urls")),
    # 测试管理
    path("test_m", include("test_m.urls")),
    # 功能模块
    path("functions", include("functions.urls")),
    # 测试用例
    path("test_cases", include("test_cases.urls")),
    # 企业标准测试用例
    path("es_test_cases", include("es_test_cases.urls")),
    # 测试计划
    path("test_plans", include("test_plans.urls")),
    # 测试计划 v2
    path("v2/test_plans", include("test_plans_v2.urls")),
    # 测试记录
    path("test_records", include("test_records.urls")),
    # 测试记录 v2
    path("v2/test_records", include("test_records_v2.urls")),
    # 测试报告
    path("test_reports", include("test_reports.urls")),
    # 测试报告 v2
    path("v2/test_reports", include("test_reports_v2.urls")),
    # 程序管理
    path("programs", include("programs.urls")),
    # 产品版本
    path("product_versions", include("product_versions.urls")),
    # 差分升级
    path("diff_tool", include("diff_tool.urls")),
    # 用例标签
    path("test_case_tags", include("test_case_tags.urls")),
    # 自定义指令
    path("custom_cmds", include("custom_cmds.urls")),
    # 测试人员管理
    path("testers", include("tester_management.urls")),
    # 产品类型
    path("product_types", include("product_types.urls")),
    # 图片资源
    path("image_resources", include("image_resources.urls")),
    # 样件管理
    path("test_prototypes", include("test_prototypes.urls")),
    # 样件管理
    path("testqueue", include("testqueue.urls")),
    # grpc通信服务
    path("softtrack/", include("softtrack.urls")),
    #  软件提测 
    path('auto_jenkins/', include('auto_jenkins.urls')),
    path("dev", include("dev.urls")),
    path("workbench", include("workbench.urls")),
    path("pack_tool", include("pack_tool.urls")),
    path("sd_versions", include("sd_versions.urls")),
    path("sd_requirements", include("sd_requirements.urls")),
    path("requirements", include("requirements.urls")),
    path("bugs", include("bugs.urls")),
    path("process_monitor", include("process_monitor.urls")),
    path("stress_tests", include("stress_tests.urls")),
    path("code_management", include("code_management.urls")),
    path("inspection", include("inspection.urls")),
    path("test_products", include("test_products.urls")),
    path("can_dbc", include("can_dbc.urls")),
    path("inspecs", include("inspecs.urls")),
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
