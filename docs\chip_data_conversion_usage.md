# 芯片数据自动转换功能使用说明

## 概述

当从数据库获取的数据字段不一致时，系统会自动将数据转换为字典格式输出。这个功能通过 `queryset_to_dict` 通用函数和 `get_model_data` 方法实现。

## 核心功能

### 1. `queryset_to_dict(queryset_or_instance)` 函数

这个通用函数可以将任何 Django QuerySet 或模型实例转换为字典格式，自动处理各种字段类型：

- **ArrayField**: 自动转换为 Python 列表
- **JSONField**: 保持原有的字典或列表格式
- **日期时间字段**: 转换为 ISO 格式字符串
- **布尔字段**: 转换为 Python 布尔值
- **数值字段**: 转换为对应的数值类型
- **其他字段**: 转换为字符串格式

### 2. `get_model_data(model_class, database="hwcp", filters=None, **kwargs)` 方法

通用方法，用于获取任意模型的数据并自动转换为字典格式：

**参数说明:**
- `model_class`: Django 模型类
- `database`: 数据库别名，默认为 "hwcp"
- `filters`: 过滤条件字典
- `**kwargs`: 其他查询参数

## 使用示例

### 示例 1: 获取芯片颜色数据

```python
# 原来的方式
chip_colors = ChipColor.objects.using("hwcp").all()
serializer = ChipColorSerializer(chip_colors, many=True)
data = serializer.data

# 新的方式 - 自动转换
data = self.get_model_data(ChipColor)
```

### 示例 2: 获取特定芯片的配置数据

```python
# 原来的方式
table = Chip.objects.using("hwcp").filter(chip=chip_name)
data = []
for item in table:
    data.append({
        "pin_id": item.pin_id,
        "pin_name": item.pin_name,
        "chip": item.chip,
        "model": item.model,
        "module": list(item.module),
        "alt_value": item.alt_value,
        "status": item.status
    })

# 新的方式 - 自动转换
data = self.get_model_data(Chip, filters={"chip": chip_name})
```

### 示例 3: 获取任意芯片模型数据

```python
# 支持的模型类型
model_mapping = {
    "chip": Chip,
    "chip_color": ChipColor,
    "chip_gpio": ChipGPIO,
    "chip_adc": ChipADC,
    "chip_pwm": ChipPWM,
    "chip_iic": ChipIIC,
    "chip_spi": ChipSPI,
    "chip_uart": ChipUART,
    "chip_exti": ChipEXTI,
    "chip_map": ChipMap,
}

# 动态获取数据
model_class = model_mapping.get("chip_gpio")
data = self.get_model_data(model_class, filters={"chip": "KF32A158"})
```

## API 接口示例

### 获取芯片配置数据

**请求:**
```
GET /api/chip/?project_code=xxx&project_gitlab=xxx&project_branch=xxx
```

**响应:**
```json
{
    "status": 1,
    "message": "数据获取成功",
    "data": {
        "chip": "KF32A158",
        "color": [
            {
                "id": 1,
                "module": "GPIO",
                "color": "#A5D6A7"
            }
        ],
        "io": ["GPIO", "PWM", "UART"],
        "table": [
            {
                "id": 1,
                "pin_id": 5,
                "pin_name": "PH_8",
                "chip": "KF32A158",
                "model": "PWM",
                "module": ["PWM", "GPIO"],
                "alt_value": {"type": "", "work_model": ""},
                "status": false
            }
        ]
    }
}
```

### 获取任意芯片数据

**请求:**
```
GET /api/chip/any/?model=chip_gpio&chip=KF32A158
```

**响应:**
```json
{
    "status": 1,
    "message": "成功获取 chip_gpio 数据",
    "data": {
        "model": "chip_gpio",
        "count": 1,
        "items": [
            {
                "id": 1,
                "type": ["GPIO"],
                "work_model": ["输入", "输出", "复用", "模拟"],
                "speed": ["高速", "中速", "低速"],
                "output_type": ["开漏", "推挽"],
                "output_level": ["低", "高"]
            }
        ]
    }
}
```

## 优势

1. **自动化处理**: 无需手动编写字段转换代码
2. **类型安全**: 自动处理各种 Django 字段类型
3. **统一格式**: 所有数据都转换为一致的字典格式
4. **易于扩展**: 支持任意 Django 模型
5. **错误处理**: 内置异常处理和日志记录

## 注意事项

1. 确保数据库连接配置正确
2. 模型类需要正确导入
3. 过滤条件要与模型字段匹配
4. 大量数据查询时注意性能优化
