
from django.db import models
from rest_framework import serializers
from django.contrib.postgres.fields import ArrayField





class LuminancecCure(models.Model):
    id = models.AutoField(primary_key=True)
    group_name = models.CharField(max_length=50)
    group_type = models.Char<PERSON>ield(max_length=30)
    desc = models.CharField(max_length=255)
    coordinate_type = models.CharField(max_length=40)
    default = models.JSONField()
    min = models.CharField(max_length=50)
    max = models.JSONField(max_length=50)
    type = models.CharField(max_length=20)
    name = models.Char<PERSON>ield(max_length=50)
    params_key = models.Char<PERSON>ield(max_length=50)
    var_name = models.Char<PERSON>ield(max_length=255)
    node_level = models.CharField(max_length=255)

    class Meta:
        db_table = 'luminance_cure'
        app_label = 'luminance_cure'
        managed = False



class LuminancecCureSerializer(serializers.ModelSerializer):
    class Meta:
        model = LuminancecCure
        fields = '__all__'




class LuminanceDefindValue(models.Model):
    id = models.AutoField(primary_key=True)
    project_code = models.CharField(max_length=100)
    project_name = models.CharField(max_length=200)
    project_gitlab = models.CharField(max_length=255)
    project_branch = models.CharField(max_length=100)
    node_level = models.CharField(max_length=100)
    var_name = models.CharField(max_length=100)
    tempre_map = ArrayField(models.JSONField(null=True), default=list)
    temparray_line = ArrayField(models.JSONField(null=True), default=list)

    class Meta:
        db_table = 'luminance_defind_value'
        app_label = 'luminance_defind_value'  # 可根据实际应用名调整
        managed = False

class LuminanceDefindValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = LuminanceDefindValue
        fields = '__all__'
