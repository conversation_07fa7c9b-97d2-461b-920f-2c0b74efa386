from rest_framework import serializers


class ImageResourceListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)
    image_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    thumbnail_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    index_re = serializers.IntegerField(default=0)


class ImageResourceSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255)
    type = serializers.IntegerField(default=0)
    image = serializers.ImageField()
    index = serializers.IntegerField(default=0)
    rgb = serializers.CharField(max_length=255, required=False)
    desc = serializers.Char<PERSON>ield(required=False, allow_null=True, allow_blank=True)


class ImageResourceUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    desc = serializers.Char<PERSON>ield(required=False, allow_null=True, allow_blank=True)
