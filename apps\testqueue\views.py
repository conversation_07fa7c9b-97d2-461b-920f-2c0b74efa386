import os
#import git
import re
import json
import time
from django.conf import settings
import datetime
import grpc
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from utils.proto import logdir_pb2_grpc
from utils.proto import logdir_pb2
import logging
from utils.NextcloudHelper import upload,cloud,create_path
import tempfile
import shutil
import threading
logger = logging.getLogger("testqueue")



CURRENT_PATH = settings.MEDIA_ROOT  
GITLAB_SERVER = "*********"
JENKINS_TOKEN = "**************************"  # jenkins token
GITLAB_URL = "http://*********"  # gitlab地址
AUTOTEST_ROOT = "software_design"
NEXTCLOUD_UID = 'D6A25366-9A66-4BA4-8AB9-AB918E763DC7'


class AllProjectView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user
            logger.info(f"{user.username} 进入软件开发平台CICD")
            channel = grpc.insecure_channel('localhost:50051')
            gitlab_stub = logdir_pb2_grpc.GitlabServiceStub(channel)
            gitlab_response = gitlab_stub.ListAllProjects(logdir_pb2.Empty())
            repos = list()
            is_success = gitlab_response.success
            if not is_success:
                return Response({"message": "Error"}, status=500)
            project_list = gitlab_response.projects
            for project in project_list:   # proto对象需要转换成JSON
                project_dict = {
                    'id': project.project_id,
                    'name': project.project_name,
                    'url': project.project_url,
                    'description': project.project_description,
                    'last_activity': project.project_last_activity
                }
                repos.append(project_dict)
            return Response({"message": "All project view", "data": repos})
        except grpc.RpcError as e:
            error_message  = f"grpc 连接错误{str(e)}"
            logger.error(f"grpc错误: {e}")
            return Response({"message": error_message}, status=503)
        except Exception as e:
            error_message = f"错误{str(e)}"
            logger.error(error_message)
            return Response({"message": error_message}, status=500)
        finally:
            if channel:
                channel.close()



class GitProjectView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            logger.info(f"{request.user.username} 进行克隆操作")
            project_name = request.query_params.get('project_name')
            if not project_name:
                return Response({"message": "Missing project_name"}, status=400)
            channel = grpc.insecure_channel('localhost:50051')
            gitlab_stub = logdir_pb2_grpc.GitlabServiceStub(channel)
            gitlab_response = gitlab_stub.SyncProjects(logdir_pb2.Empty())
            if not gitlab_response.success:
                return Response({"message": "Error","result": False}, status=500)
            return Response(data={"message": f"{project_name}克隆成功","result": True}, status=200)
        except Exception as e:
            print(e)
            return Response({"message": "Error"}, status=500)
        finally:
            if channel:
                channel.close()

class ProjectScannerView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        try:
            project_name = request.query_params.get('project_name')
            if not project_name:
                return Response({"message": "Missing project_name", "code": 1001}, status=400)
            logger.info(f"{request.user.username} 进行项目扫描操作")
            channel = grpc.insecure_channel('localhost:50051')
            scanner_stub = logdir_pb2_grpc.ProjectScannerServiceStub(channel)
            scan_request = scanner_stub.ScanProject(logdir_pb2.ProjectScanRequest(project_name=project_name))
            if scan_request.success:
                message = scan_request.message
                code = scan_request.code
                return Response({"message": message,"code":code,"result": True}, status=200)
            if  not scanner_stub.success  and scanner_stub.code == 1003:
                return Response({"message": f"{project_name}中没有.c文件","result": False,"code":1003}, status=200)
            else:
                return Response({"message": "Error","code":1005}, status=500)
        except Exception as e :
            print(e)
            return Response({"message": "Error","code":1005}, status=500)
        finally:
            if channel:
                channel.close()


class CodeAnalyzerView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]


    def save_file_to_nextcloud(self, analysis_result, parent_dir, temp_dir):
        """
        @param analysis_result: 代码分析结果
        @param project_name: 项目名称
        将这些json文件保存到nextcloud中,并返回父目录的分享链接
        """
        try:
            file_name = analysis_result["文件名"]
            now_time_str = time.strftime("%Y%m%d%H%M%S", time.localtime())
            file_path = os.path.join(temp_dir, f"{file_name}_{now_time_str}.json")
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=4)
            remote_path = f'{parent_dir}/{os.path.basename(file_path)}'
            success, _ = upload(file_path, remote_path)
            print(f"{success=}")
            if not success:
                logger.error(f"上传文件 {file_name} 到Nextcloud失败")
            return success
        except Exception as e:
            logger.error(f"上传文件到Nextcloud失败: {e}")
            return False
    
    def upload_files_thread(self, file_dict, parent_dir, temp_dir):
        """
        后台线程函数，用于上传所有文件
        """
        try:
            for result_dict in file_dict:
                success = self.save_file_to_nextcloud(result_dict, parent_dir, temp_dir)
                if not success:
                    logger.error(f"上传文件 {result_dict.get('文件名', '未知')} 失败")
            
            logger.info(f"所有文件上传完成，临时目录: {temp_dir}")
        except Exception as e:
            logger.error(f"文件上传线程发生异常: {str(e)}")
        finally:
            if temp_dir and os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    logger.info(f"清理临时目录: {temp_dir}")
                except:
                    logger.error(f"清理临时目录失败: {temp_dir}")
            
    def get(self, request):
        try: 
            temp_dir = None
            project_name = request.query_params.get('project_name')
            if not project_name:
                return Response({"message": "Missing project_name", "code": 1001}, status=400)
                
            logger.info(f"{request.user.username} 进行代码分析操作")
            channel = grpc.insecure_channel('localhost:50051')
            scanner_stub = logdir_pb2_grpc.ProjectScannerServiceStub(channel)
            scanner_response = scanner_stub.AnalyzeCode(logdir_pb2.CodeAnalyzeRequest(project_name=project_name))
            file_dict = list()
            
            if scanner_response.success:
                message = scanner_response.message
                temp_dir = tempfile.mkdtemp()
                today = time.strftime("%Y%m%d", time.localtime())
                request_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
                parent_dir = f'{AUTOTEST_ROOT}/{project_name}/json_reports/{today}/{request_time}'
                

                create_path(NEXTCLOUD_UID, parent_dir)
                
                for analysis_result in scanner_response.analysis_results:
                    result_dict = dict()
                    result_dict["文件名"] = analysis_result.file_name
                    result_dict["文件路径"] = analysis_result.file_path
                    result_dict["头文件"] = analysis_result.headr_file
                    result_dict["函数列表"] = []
                    
                    for i, func in enumerate(analysis_result.functions):
                        func_dict = {
                            "id": i + 1,  # 从1开始的ID
                            "函数名": func.function_name,
                            "功能描述": func.description if hasattr(func, "description") else "",
                            "限制条件": func.constraints if hasattr(func, "constraints") else "",
                            "调用示例": func.example_usage,
                            "参数": [
                                {
                                    "输入/输出": param.io_type,
                                    "参数名称": param.name,
                                    "参数类型": param.type,
                                    "描述": param.description
                                } for param in func.parameters
                            ],
                            
                            "返回值": [
                                {
                                    "输入/输出": ret.io_type,
                                    "参数名称": ret.name,
                                    "参数类型": ret.type,
                                    "描述": ret.description
                                } for ret in func.return_values
                            ],
                            
                            "全局变量": [
                                {
                                    "输入/输出": var.io_type,
                                    "参数名称": var.name,
                                    "参数类型": var.type,
                                    "描述": var.description
                                } for var in func.global_vars
                            ],
                            
                            "静态变量": [
                                {
                                    "输入/输出": var.io_type,
                                    "参数名称": var.name,
                                    "参数类型": var.type,
                                    "描述": var.description
                                } for var in func.static_vars
                            ],
                            
                            "验证准则": func.validation_criteria if hasattr(func, "validation_criteria") else ""
                        }
                        result_dict["函数列表"].append(func_dict)
                    
                    file_dict.append(result_dict)
                
                share_response = cloud.create_share(parent_dir, 3)
                parent_url = ""
                if share_response.is_ok:
                    parent_url = share_response.data['url']
                    logger.info(f"创建分享链接成功: {parent_url}")
                else:
                    logger.warning("创建分享链接失败")
        
                upload_thread = threading.Thread(                                                                             # 用线程上传文件,先返回父目录
                    target=self.upload_files_thread,    
                    args=(file_dict, parent_dir, temp_dir),
                    daemon=True
                )
                upload_thread.start()
                logger.info(f"文件上传已在后台开始，共 {len(file_dict)} 个文件")
                return Response(data={"message": f"{message} (文件正在后台上传中...)",
                                     "analysis_result": file_dict,
                                     "result": True,
                                     "parent_url": parent_url}, 
                              status=200)
            else:
                return Response({"message": "Error", "result": False, "analysis_result": "分析失败", "parent_url": ""}, 
                              status=500)
                
        except Exception as e:
            logger.error(f"代码分析视图发生异常: {str(e)}")
            if temp_dir and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            if channel:
                channel.close()
            return Response({"message": "Error", "result": False, "analysis_result": str(e), "parent_url": ""}, 
                          status=500)
            