import datetime

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone


class IssueModel:
    def __init__(self):
        self.table_name = "public.issues"

    def create(self, **kwargs):
        now = datetime.datetime.now()
        params = {
            "name": kwargs.get("name"),
            "project_number": kwargs.get("project_number"),
            "occur_time": kwargs.get("occur_time"),
            "tester": kwargs.get("tester"),
            "machine_number": kwargs.get("machine_number"),
            "images": kwargs.get("image_paths"),
            "video": kwargs.get("video_path"),
            "raw": kwargs.get("raw"),
            "create_time": now,
            "update_time": now,
        }

        sql = """
            INSERT INTO {table_name} ("name", project_number, occur_time, tester, machine_number, images, video, raw, 
                create_time, update_time) 
            VALUES (%(name)s, %(project_number)s, %(occur_time)s, %(tester)s, %(machine_number)s, 
                %(images)s, %(video)s,
                %(raw)s, %(create_time)s, %(update_time)s);
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):

        params = {
        }

        sql_where_list = []

        name = kwargs.get("name")
        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        machine_number = kwargs.get("machine_number")
        if machine_number is not None and machine_number != '':
            sql_where_list.append("machine_number = %(machine_number)s")
            params["machine_number"] = machine_number

        tester = kwargs.get("tester")
        if tester is not None and tester != '':
            sql_where_list.append("tester = %(tester)s")
            params["tester"] = tester

        status = kwargs.get("status")
        if status is not None and status != '':
            sql_where_list.append("status = %(status)s")
            params["status"] = status

        start_time = kwargs.get("start_time")
        if start_time is not None and start_time != '':
            sql_where_list.append("occur_time >= %(start_time)s")
            params["start_time"] = start_time

        end_time = kwargs.get("end_time")
        if end_time is not None and end_time != '':
            sql_where_list.append("occur_time <= %(end_time)s")
            params["end_time"] = end_time

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name}
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY occur_time desc"

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} 
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, "name", project_number, occur_time, tester, machine_number, images, video, status, raw,
                create_time, update_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", project_number, occur_time, tester, machine_number, status, images, video, raw,
                create_time, update_time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        project_number = kwargs.get("project_number")
        if project_number is not None:
            sql_set_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        occur_time = kwargs.get("occur_time")
        if occur_time is not None:
            sql_set_list.append("occur_time = %(occur_time)s")
            params["occur_time"] = occur_time

        tester = kwargs.get("tester")
        if tester is not None:
            sql_set_list.append("tester = %(tester)s")
            params["tester"] = tester

        machine_number = kwargs.get("machine_number")
        if machine_number is not None:
            sql_set_list.append("machine_number = %(machine_number)s")
            params["machine_number"] = machine_number

        status = kwargs.get("status")
        if status is not None:
            sql_set_list.append("status = %(status)s")
            params["status"] = status

        raw = kwargs.get("raw")
        if raw is not None:
            sql_set_list.append("raw = %(raw)s")
            params["raw"] = raw

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result

    def cancel(self, pk):
        params = {
            "id": pk,
            "update_time": datetime.datetime.now()
        }
        sql = """
            UPDATE {table_name}     
                SET 
                status = 2,
                images = NULL,
                video = NULL,
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
        )

        result = sql_execute(sql, params)

        return result
