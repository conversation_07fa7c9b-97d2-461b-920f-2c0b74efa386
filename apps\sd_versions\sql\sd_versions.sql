CREATE TABLE IF NOT EXISTS public.sd_version_plan(
    id serial primary key, --id
    "title" varchar(255) NOT NULL,  --名称,
    "desc" text NOT NULL, --描述
    "begin_date" date, --开始日期
    "end_date" date, --结束日期
    "parent" INTEGER DEFAULT 0,
    "status" varchar(255) default 'pending', --状态 pending 未开始, processing 进行中, completed 已完成, closed 已关闭
    "project_name" varchar(255), --项目名称
    "project_number" varchar(255), --项目编号
    "deleted" boolean default false, --是否删除
    creator_name varchar(255),   --创建人姓名
    creator_email varchar(255),   --创建人邮箱
    create_time timestamp, --创建时间
    updater_name varchar(255),   --更新人姓名
    updater_email varchar(255),   --更新人邮箱
    update_time timestamp --更新时间
);
ALTER TABLE public.sd_version_plan OWNER TO atpms;