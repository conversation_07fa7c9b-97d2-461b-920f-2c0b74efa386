from django.db import connection
from psycopg2.extras import execute_values


def sql_fetchone(sql, params=None):
    with connection.cursor() as cursor:
        cursor.execute(sql, params=params)
        result = cursor.fetchone()
    return result


def sql_fetchone_dict(sql, params=None):
    with connection.cursor() as cursor:
        cursor.execute(sql, params=params)
        result = cursor.fetchone()
        if result:
            col_names = [i.name for i in cursor.description]
            result = dict(zip(col_names, result))
    return result


def sql_fetchall_dict(sql, params=None):
    with connection.cursor() as cursor:
        cursor.execute(sql, params=params)
        results = cursor.fetchall()
        col_names = [i.name for i in cursor.description]
        results = [dict(zip(col_names, i)) for i in results]
    return results


def sql_fetchall(sql, params=None):
    with connection.cursor() as cursor:
        cursor.execute(sql, params=params)
        results = cursor.fetchall()
    return results


def sql_insert_many(sql, argslist):
    with connection.cursor() as cursor:
        execute_values(cursor, sql, argslist)
        connection.commit()


def sql_execute_return(sql, params):
    with connection.cursor() as cursor:
        cursor.execute(sql, params=params)
        results = cursor.fetchall()
        col_names = [i.name for i in cursor.description]
        results = [dict(zip(col_names, i)) for i in results]
    return results


def sql_execute(sql, params=None):
    with connection.cursor() as cursor:
        cursor.execute(sql, params=params)

