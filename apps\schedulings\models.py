import datetime
import logging

from utils.sql_helper import (
    sql_execute, sql_fetchone_dict, sql_fetchall_dict, sql_fetchone, sql_insert_many
)

logger = logging.getLogger("machines")


def parse_schedulings(schedulings, parent_id=None, parent_number=""):
    results = []
    tmp = [i for i in schedulings]

    for f in tmp:
        if f["parent_id"] == parent_id or (parent_id is None and f["parent_id"] is None):
            if parent_number:
                f["number2"] = parent_number + "_" + f["number"]
            else:
                f["number2"] = f["number"]
            children = parse_schedulings(schedulings, parent_id=f["id"], parent_number=f["number2"])
            if children:
                f["children"] = children
            results.append(f)

    return results


class SchedulingModel:
    def __init__(self):
        self.table_name = "public.scheduling"

    def create(self, **kwargs):
        parent_id = kwargs.get("parent_id")

        level = 1
        if parent_id:
            r = self.retrieve(parent_id)
            if r:
                pl = r.get("level")
                level = pl + 1

        params = {
            "shift_personnel": kwargs.get("shift_personnel"),
            "inspectors": kwargs.get("inspectors"),
            "exception_description": kwargs.get("exception_description"),
            "time": datetime.datetime.now(),
        }

        sql = """
            INSERT INTO {table_name} ("shift_personnel",  "inspectors", "exception_description", parent_id, "level", time) 
            VALUES (%(shift_personnel)s, %(inspectors)s, %(exception_description)s, %(parent_id)s, %(level)s, %(time)s);
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):
        name = kwargs.get("name")

        params = {
        }

        sql_where_list = []

        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name}
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql = """
            SELECT id, "shift_personnel", "inspectors", "exception_description", parent_id, "level", time,  deprecated
                FROM public.scheduling
                {sql_where}
                {sql_order_by}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
        )

        results = sql_fetchall_dict(sql, params)

        content = {
            "results": parse_schedulings(results)
        }

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "shift_personnel", "inspectors", "exception_description", parent_id, "level", time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }

        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        shift_personnel = kwargs.get("shift_personnel")
        if shift_personnel is not None:
            sql_set_list.append("shift_personnel = %(shift_personnel)s")
            params["shift_personnel"] = shift_personnel

        inspectors = kwargs.get("inspectors")
        if inspectors is not None:
            sql_set_list.append("inspectors = %(inspectors)s")
            params["inspectors"] = inspectors

        exception_description = kwargs.get("exception_description")
        if exception_description is not None:
            sql_set_list.append("\"exception_description\" = %(exception_description)s")
            params["exception_description"] = exception_description

        time = kwargs.get("time")
        if time is not None:
            sql_set_list.append("time = %(time)s")
            params["time"] = time

        deprecated = kwargs.get("deprecated")
        if deprecated is not None:
            sql_set_list.append("deprecated = %(deprecated)s")
            params["deprecated"] = deprecated

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                time = %(time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result

    def bulk_create(self, data):
        col_names = ["shift_personnel", "inspectors", "exception_description"]
        argslist = []

        now = datetime.datetime.now()

        for i in data:
            temp_list = []
            for j in col_names:
                temp_list.append(i.get(j))
            temp_list.append(now)
            temp_list.append(now)
            argslist.append(temp_list)

        sql = """
            INSERT INTO {table_name} ("shift_personnel", "inspectors", "exception_description", time) 
            VALUES %s 
            ON CONFLICT ("shift_personnel") DO NOTHING
            ;
        """.format(table_name=self.table_name)

        sql_insert_many(sql, argslist=argslist)

    # def test_case_stat(self, project_number=""):
    #     if project_number:
    #         pass
    #
    #     params = {}
    #
    #     sql_where_list = ["not(is_deleted)"]
    #
    #     if project_number:
    #         sql_where_list.append("project_number = %(project_number)s")
    #         params["project_number"] = project_number
    #
    #     if sql_where_list:
    #         sql_where = " WHERE " + " AND ".join(sql_where_list)
    #     else:
    #         sql_where = ""
    #
    #     sql = """
    #         SELECT "module", module_2level, module_3level,
    #             count("module") as count1, count(module_2level) as count2, count(module_3level) as count3
    #             FROM public.scheduling
    #             {sql_where}
    #             GROUP BY "module", module_2level, module_3level
    #         ;
    #     """.format(sql_where=sql_where)
    #
    #     result = sql_fetchall_dict(sql, params)
    #
    #     data = {}
    #     for i in result:
    #         module = i.get("module")
    #         module_2level = i.get("module_2level")
    #         module_3level = i.get("module_3level")
    #
    #         if data.get(module) is None:
    #             data[module] = 0
    #         data[module] += i.get("count1")
    #
    #         if module_2level:
    #             k = f"{module}_{module_2level}"
    #             if data.get(k) is None:
    #                 data[k] = 0
    #             data[k] += i.get("count2")
    #
    #         if module_3level:
    #             k = f"{module}_{module_2level}_{module_3level}"
    #             if data.get(k) is None:
    #                 data[k] = 0
    #             data[k] += i.get("count3")
    #
    #     return data