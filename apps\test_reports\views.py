import os
import traceback
import logging
from io import BytesIO
import urllib.parse
import pandas as pd
from copy import deepcopy

from django.http import HttpResponse
from openpyxl import load_workbook
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from .serializers import TestReportsListSerializer
from .models import TestReportModel, plan_stats
from functions.models import FunctionModel

logger = logging.getLogger("machine")


class TestReportsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestReportsListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestReportModel()
            content = model.list(**serializer.validated_data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)

        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestReportDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = TestReportModel()
            content = model.retrieve(pk)

            if content:
                plan_stats(content)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)

        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestReportDownloadView(APIView):
    def get(self, request, pk=None):
        try:
            model = TestReportModel()
            plan = model.retrieve(pk)

            if plan:
                plan_stats(plan)
            else:
                return Response({"err_code": 1, "msg": ""}, status.HTTP_400_BAD_REQUEST)

            base_dir = settings.BASE_DIR
            file_path = os.path.join(base_dir, "config", "测试报告模板.xlsx")
            wb = load_workbook(file_path)

            ws = wb.get_sheet_by_name("封面")

            ws.cell(row=7, column=4, value=plan.get("project_number"))

            ws = wb.get_sheet_by_name("总结")
            stats = plan.get("stats")

            tester = set()
            for i in stats.get("sp_stats"):
                tester.add(stats.get("sp_stats")[i].get("tester_name"))
            tester.add(plan.get("creator_name"))
            tester = [i for i in tester if i and i != plan.get("creator_name")]
            tester.insert(0, plan.get("creator_name"))
            tester = " ".join(tester)

            ws.cell(row=3, column=2, value=plan.get("software_version"))
            ws.cell(row=4, column=2, value="通过" if stats.get("result") else "不通过")
            ws.cell(row=5, column=2, value=tester)
            ws.cell(row=6, column=2, value=stats.get("start_time"))
            ws.cell(row=7, column=2, value=stats.get("end_time"))

            # for pv in plan.get("product_version"):
            #     ws.insert_rows(4)
            #     cell = ws.cell(row=4, column=1, value=pv.get("type_name"))
            #     cell._style = deepcopy(ws.cell(row=3, column=1)._style)
            #     cell = ws.cell(row=4, column=2, value=pv.get("name"))
            #     cell._style = deepcopy(ws.cell(row=3, column=2)._style)

            row_index = 0
            for index, row in enumerate(ws.iter_rows()):
                if row[0].value == "测试用例汇总分析":
                    row_index = index + 1
                    break
            row_index += 2

            data = FunctionModel().list().get("results")
            module_map = {}
            for i in data:
                module_map[i["number"]] = i["name"]
                if i.get("children"):
                    for j in i["children"]:
                        module_map["{}-{}".format(i["number"], j["number"])] = j["name"]

                        if j.get("children"):
                            for k in j["children"]:
                                module_map["{}-{}-{}".format(i["number"], j["number"], k["number"])] = k["name"]

            count = 0
            for i in stats.get("m_stats"):
                ws.cell(row=row_index + count, column=1, value=module_map.get(i.get("module")))
                ws.cell(row=row_index + count, column=2, value=i.get("total"))
                ws.cell(row=row_index + count, column=3, value=i.get("non_exec"))
                ws.cell(row=row_index + count, column=4, value=i.get("pass"))
                ws.cell(row=row_index + count, column=5, value=i.get("ng"))
                ws.cell(row=row_index + count, column=6, value=i.get("pass_rate"))
                ws.cell(row=row_index + count, column=11, value="通过" if i.get("result") else "不通过")

                count += 1

            output = BytesIO()
            wb.save(output)
            output.seek(0)

            with pd.ExcelWriter(output, engine='openpyxl', mode="a") as writer:
                pass

            output.seek(0)
            response = HttpResponse(
                output,
                content_type="application/octet-stream"
            )
            filename = "测试报告.xlsx"
            filename = urllib.parse.quote(filename)
            response['Content-Disposition'] = f'attachment; filename={filename}'

            return response

        except Exception:
            logger.error(traceback.format_exc())
            return HttpResponse("Invalid request", status=400)
