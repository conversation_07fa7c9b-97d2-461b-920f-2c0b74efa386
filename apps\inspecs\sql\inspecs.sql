CREATE TABLE IF NOT EXISTS public.inspec_items(
    id serial primary key,
    "name" VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL UNIQUE,
    "desc" TEXT DEFAULT '',
    is_active BOOLEAN DEFAULT TRUE,
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.inspec_items OWNER TO atpms;

CREATE TABLE IF NOT EXISTS public.inspec_persons(
    id serial primary key,
    user_name VARCHAR(255) NOT NULL,
    user_email VARCHAR(255) NOT NULL,
    "order" INTEGER NOT NULL DEFAULT 0,
    is_available BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    on_shift BOOLEAN DEFAULT FALSE,
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.inspec_persons OWNER TO atpms;

CREATE TABLE IF NOT EXISTS public.inspec_times(
    id serial primary key,
    "date" DATE NOT NULL UNIQUE,
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.inspec_times OWNER TO atpms;

CREATE TABLE IF NOT EXISTS public.inspec_records(
    id serial primary key,
    item_id INTEGER NOT NULL,
    person_id INTEGER NOT NULL,
    time_id INTEGER NOT NULL,
    "is_pass" BOOLEAN,
    "desc" TEXT,
    create_time timestamp, --创建时间
    update_time timestamp, --更新时间
    UNIQUE(item_id, time_id) -- 确保每个检查项和时间的记录唯一
);
ALTER TABLE public.inspec_records OWNER TO atpms;