import datetime
import json

from django.db import models
from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone, sql_insert_many
from django.db import transaction

from .signals import (test_case_create_signal, test_case_delete_signal, test_case_update_signal)


class TestCaseRequirementMap(models.Model):
    test_case_id = models.IntegerField(primary_key=True)
    test_case_version = models.CharField(max_length=255, primary_key=True)
    requirement_id = models.CharField(max_length=255, primary_key=True)
    requirement_version = models.CharField(max_length=255, primary_key=True)
    requirement_number = models.Char<PERSON>ield(max_length=255)
    create_time = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'test_case_requirement_map'
        managed = False
        app_label = 'test_cases'
        unique_together = ('test_case_id', 'test_case_version', 'requirement_id', 'requirement_version')  # 设置联合主键

    def to_dict(self):
        return {
            "test_case_id": self.test_case_id,
            "test_case_version": self.test_case_version,
            "requirement_id": self.requirement_id,
            "requirement_version": self.requirement_version,
            "requirement_number": self.requirement_number,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S"),
        }


class TestCaseModel:
    def __init__(self):
        self.table_name = "public.test_cases2"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            module = kwargs.get("module")
            module_list = module.split(", ")
            for m in module_list:
                if m.strip() == "":
                    raise Exception("module can not be empty")
            module = module_list[0]
            module_2level = None
            module_3level = None
            if len(module_list) >= 2:
                module_2level = module_list[1]
            if len(module_list) >= 3:
                module_3level = module_list[2]

            action_type = kwargs.get("action_type")
            project_number = kwargs.get("project_number")

            sql_where = []
            params = {}
            sql_where.append("project_number = %(project_number)s")
            params["project_number"] = project_number
            sql_where.append("action_type = %(action_type)s")
            params["action_type"] = action_type
            sql_where.append("module = %(module)s")
            params["module"] = module
            if module_2level is None:
                sql_where.append("module_2level is NULL")
            else:
                sql_where.append("module_2level = %(module_2level)s")
                params["module_2level"] = module_2level
            if module_3level is None:
                sql_where.append("module_3level is NULL")
            else:
                sql_where.append("module_3level = %(module_3level)s")
                params["module_3level"] = module_3level
            sql_where = "where " + " and ".join(sql_where)

            sql = f"""
                select count(*) from public.test_cases2 
                {sql_where} 
                ; 
            """
            count = sql_fetchone(sql, params)[0]

            tl = [action_type, module, module_2level, module_3level]
            tl = [i for i in tl if i is not None]
            tl.append(str(count + 1).rjust(5, '0'))
            number = "-".join(tl)

            params = {
                "name": kwargs.get("name"),
                "number": number,
                "module": module,
                "module_2level": module_2level,
                "module_3level": module_3level,
                "type": kwargs.get("type"),
                "action_type": action_type,
                "version": "1",
                "status": "PENDING",
                "priority": kwargs.get("priority"),
                "related_requirements": kwargs.get("related_requirements"),
                "source": kwargs.get("source"),
                "generation_method": kwargs.get("generation_method"),
                "test_method": ", ".join(kwargs.get("test_method")),
                "execute_mode": kwargs.get("execute_mode"),
                "function_safe_attrib": kwargs.get("function_safe_attrib"),
                "preconditions": kwargs.get("preconditions"),
                "steps": kwargs.get("steps"),
                "expected": kwargs.get("expected"),
                "remark": kwargs.get("remark"),
                "cycle": kwargs.get("cycle"),
                "vision_revert": kwargs.get("vision_revert"),
                "vision_algorithm": kwargs.get("vision_algorithm"),
                "creator_name": kwargs.get("creator_name"),
                "creator_employee_number": kwargs.get("creator_employee_number"),
                "creator_email": kwargs.get("creator_email"),
                "tag": " | ".join(kwargs.get("tag") if kwargs.get("tag") else []),
                "project_name": kwargs.get("project_name"),
                "project_number": project_number,
                "project_id": kwargs.get("project_id"),
                "es_source": kwargs.get("es_source"),
                "es_id": kwargs.get("es_id"),
                "product_type_id": kwargs.get("product_type_id"),
                "public_test_case_id": kwargs.get("public_test_case_id"),
                "create_time": now,
                "update_time": now,
            }

            sql = """
                INSERT INTO {table_name} ("name", "number", "module", module_2level, module_3level, "type", action_type, 
                    "version", status, priority, related_requirements,
                    source, generation_method, test_method, execute_mode, function_safe_attrib, preconditions, steps, expected, remark, 
                    "cycle", vision_revert, vision_algorithm, creator_name, creator_employee_number, creator_email,
                    tag, project_name, project_number, project_id, es_source, es_id, product_type_id, public_test_case_id,
                     create_time, update_time) 
                VALUES (%(name)s, %(number)s, %(module)s, %(module_2level)s, %(module_3level)s, %(type)s, %(action_type)s,
                    %(version)s, %(status)s, %(priority)s, %(related_requirements)s,
                    %(source)s, %(generation_method)s, %(test_method)s, %(execute_mode)s, %(function_safe_attrib)s, 
                    %(preconditions)s, %(steps)s, %(expected)s, %(remark)s, %(cycle)s, %(vision_revert)s,
                     %(vision_algorithm)s, %(creator_name)s, %(creator_employee_number)s, %(creator_email)s,
                    %(tag)s, %(project_name)s, %(project_number)s, %(project_id)s, %(es_source)s, %(es_id)s,
                     %(product_type_id)s, %(public_test_case_id)s, %(create_time)s, %(update_time)s)
                RETURNING id
                ;
            """.format(table_name=self.table_name)

            ts_id = sql_fetchone(sql, params)[0]

            sql = """
                INSERT INTO public.test_steps (test_case_id, "order", "type", params, "desc", expectation,
                 create_time, update_time)
                VALUES (%(test_case_id)s, %(order)s, %(type)s, %(params)s, %(desc)s, %(expectation)s,
                 %(create_time)s, %(update_time)s)
                ;
            """

            test_steps = kwargs.get("test_steps", [])
            for (index, step) in enumerate(test_steps):
                params = {
                    "test_case_id": ts_id,
                    "order": index + 1,
                    "type": step.get("type"),
                    "params": step.get("params"),
                    "desc": step.get("desc"),
                    "expectation": step.get("expectation"),
                    "create_time": now,
                    "update_time": now,
                }
                sql_execute(sql, params)

            if kwargs.get("requirement_id") and kwargs.get("requirement_version"):
                TestCaseRequirementMap.objects.create(
                    test_case_id=ts_id,
                    test_case_version="1",
                    requirement_id=kwargs.get("requirement_id"),
                    requirement_version=kwargs.get("requirement_version"),
                    requirement_number=kwargs.get("requirement_number"),
                )

            test_case_create_signal.send(
                self.__class__,
                project_number=project_number,
                action_type=action_type,
                module=module,
                type=kwargs.get("type"),
                execute_mode=kwargs.get("execute_mode"),
                test_case_id=ts_id,
            )

            return ts_id

    @staticmethod
    def _build_sql_where(ignore_is_deleted=True, **kwargs):
        params = {}
        sql_where_list = []

        if ignore_is_deleted:
            sql_where_list.append("not(is_deleted)")

        id = kwargs.get("id")
        if id is not None and id != '':
            sql_where_list.append("id = %(id)s")
            params["id"] = id

        search_str = kwargs.get("search_str")
        if search_str is not None and search_str != '':
            sql_where_list.append(
                "(name ~* %(search_str)s OR project_name ~* %(search_str)s OR project_number ~* %(search_str)s "
                "OR creator_name ~* %(search_str)s)"
            )
            params["search_str"] = search_str

        name = kwargs.get("name")
        if name is not None and name != '':
            sql_where_list.append("name ~* %(name)s")
            params["name"] = name

        creator_name = kwargs.get("creator_name")
        if creator_name is not None and creator_name != '':
            sql_where_list.append("creator_name = %(creator_name)s")
            params["creator_name"] = creator_name

        creator_re = kwargs.get("creator_re")
        if creator_re is not None and creator_re != '':
            sql_where_list.append("creator_name ~* %(creator_re)s")
            params["creator_re"] = creator_re

        number = kwargs.get("number")
        if number is not None and number != '':
            sql_where_list.append("number ~* %(number)s")
            params["number"] = number

        version = kwargs.get("version")
        if version is not None and version != '':
            sql_where_list.append("version = %(version)s")
            params["version"] = version

        number_c = kwargs.get("number_c")
        if number_c is not None and number_c != '':
            sql_where_list.append("number = %(number_c)s")
            params["number_c"] = number_c

        status = kwargs.get("status", "")
        if status is not None and status != '':
            sql_where_list.append("status = %(status)s")
            params["status"] = status

        status_list = kwargs.get("status_list", "")
        if status_list is not None and len(status_list) > 0:
            sql_where_list.append("status in %(status_list)s")
            params["status_list"] = tuple(status_list)

        product_type_id_list = kwargs.get("product_type_id_list", "")
        if product_type_id_list is not None and len(product_type_id_list) > 0:
            sql_where_list.append("product_type_id in %(product_type_id_list)s")
            params["product_type_id_list"] = tuple(product_type_id_list)

        priority_list = kwargs.get("priority_list", "")
        if priority_list is not None and priority_list != '':
            sql_where_list.append("priority in %(priority_list)s")
            params["priority_list"] = tuple(priority_list)

        execute_mode_list = kwargs.get("execute_mode_list", "")
        if execute_mode_list is not None and execute_mode_list != '':
            sql_where_list.append("execute_mode in %(execute_mode_list)s")
            params["execute_mode_list"] = tuple(execute_mode_list)

        creator_employee_number = kwargs.get("creator_employee_number")
        if creator_employee_number is not None and creator_employee_number != '':
            sql_where_list.append("creator_employee_number = %(creator_employee_number)s")
            params["creator_employee_number"] = creator_employee_number

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        action_type = kwargs.get("action_type")
        if action_type is not None and action_type != '':
            sql_where_list.append("action_type = %(action_type)s")
            params["action_type"] = action_type

        type = kwargs.get("type")
        if type is not None and type != '':
            sql_where_list.append("type = %(type)s")
            params["type"] = tuple(type)

        is_durable = kwargs.get("is_durable")
        if is_durable is not None:
            if is_durable:
                sql_where_list.append("type = 'DURABLE_TEST'")
            else:
                sql_where_list.append("type != 'DURABLE_TEST'")

        module = kwargs.get("module")
        if module is not None and module != '':
            sql_where_list.append("module = %(module)s")
            params["module"] = module

        module_2level = kwargs.get("module_2level")
        if module_2level is not None and module_2level != '':
            sql_where_list.append("module_2level = %(module_2level)s")
            params["module_2level"] = module_2level

        module_3level = kwargs.get("module_3level")
        if module_3level is not None and module_3level != '':
            sql_where_list.append("module_3level = %(module_3level)s")
            params["module_3level"] = module_3level

        sql_where_list2 = []

        module_list = kwargs.get("module_list")
        if module_list:
            sql_where_list2.append("module in %(module_list)s")
            params["module_list"] = tuple(module_list)

        module_2level_list = kwargs.get("module_2level_list")
        if module_2level_list:
            for i, v in enumerate(module_2level_list):
                m = v.split(",")
                if len(m) != 2:
                    continue
                sql_where_list2.append(f"(module = %(module_2level_l{i})s and module_2level = %(module_2level_r{i})s)")
                params[f"module_2level_l{i}"] = m[0]
                params[f"module_2level_r{i}"] = m[1]

        module_3level_list = kwargs.get("module_3level_list")
        if module_3level_list:
            for i, v in enumerate(module_3level_list):
                m = v.split(",")
                if len(m) != 3:
                    continue
                sql_where_list2.append(
                    f"(module = %(module_3level_l{i})s and module_2level = %(module_3level_z{i})s) and module_3level = %(module_3level_r{i})s")
                params[f"module_3level_l{i}"] = m[0]
                params[f"module_3level_z{i}"] = m[1]
                params[f"module_3level_r{i}"] = m[2]

        if sql_where_list2:
            sql_where_list.append("(" + " OR ".join(sql_where_list2) + ")")

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)
        order = kwargs.get("order", [])

        sql_where, params = self._build_sql_where(**kwargs)

        if not order:
            sql_order_by = " ORDER BY id desc "
        else:
            for i in range(len(order)):
                if order[i].startswith("-"):
                    order[i] = order[i][1:] + " DESC"
            sql_order_by = " ORDER BY " + ", ".join(order)

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
        SELECT id, "name", "number", "module", module_2level, module_3level, "type", action_type, "version", status, priority, source, 
            generation_method, test_method, execute_mode, function_safe_attrib, preconditions, steps, expected, "cycle", 
            vision_revert, vision_algorithm, remark, creator_name, creator_employee_number, creator_email,
            tag, project_name, project_number, project_id, es_source, es_id, product_type_id, public_test_case_id,
             create_time, update_time
         FROM {table_name}
        {sql_where}
        {sql_order_by}
        {sql_limit}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        for i in content["results"]:
            i["tag"] = i["tag"].split(" | ") if i["tag"] else []
            i["create_time"] = i["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def list2(self, **kwargs):
        order = kwargs.get("order", [])

        sql_where, params = self._build_sql_where(**kwargs)

        if not order:
            sql_order_by = " ORDER BY id "
        else:
            for i in range(len(order)):
                if order[i].startswith("-"):
                    order[i] = order[i][1:] + " DESC"
            sql_order_by = " ORDER BY " + ", ".join(order)

        # sql = """
        #     WITH ts AS (
        #         SELECT id, "name", "number", "module", module_2level, module_3level, "type", action_type, "version", status, priority, source,
        #             generation_method, test_method, execute_mode, function_safe_attrib, preconditions, steps, expected, "cycle",
        #             vision_revert, vision_algorithm, remark, creator_name, creator_employee_number, creator_email,
        #             tag, project_name, project_number, project_id, es_source, es_id, product_type_id, public_test_case_id,
        #              create_time, update_time
        #          FROM {table_name}
        #         {sql_where}
        #         ),
        #         ts2 AS (
        #          SELECT ts.id, STRING_AGG(tc.desc, E'\n') as step_desc, STRING_AGG(tc.expectation, E'\n') as step_expectation
        #             FROM ts LEFT JOIN public.test_steps tc ON ts.id = tc.test_case_id
        #             GROUP BY ts.id
        #         )
        #
        #     SELECT ts.*, ts2.step_desc, ts2.step_expectation
        #     FROM ts LEFT JOIN ts2 ON ts.id = ts2.id
        #     {sql_order_by}
        #     ;
        # """.format(
        #     table_name=self.table_name,
        #     sql_where=sql_where,
        #     sql_order_by=sql_order_by,
        # )

        sql = """    
            SELECT id, "name", "number", "module", module_2level, module_3level, "type", action_type, "version", status, priority, source, 
                generation_method, test_method, execute_mode, function_safe_attrib, preconditions, steps, expected, "cycle", 
                vision_revert, vision_algorithm, remark, creator_name, creator_employee_number, creator_email,
                tag, project_name, project_number, project_id, es_source, es_id, product_type_id, public_test_case_id,
                 create_time, update_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
        )

        content = sql_fetchall_dict(sql, params)

        for i in content:
            i["tag"] = i["tag"].split(" | ") if i["tag"] else []
            i["create_time"] = i["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            i["test_method"] = i["test_method"].split(", ")

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, "name", "number", "module", module_2level, module_3level, "type", action_type, "version", status, 
            cached_status, priority, source, related_requirements,
            generation_method, test_method, execute_mode, function_safe_attrib, preconditions, steps, expected, "cycle", 
            vision_revert, vision_algorithm, remark, creator_name, creator_employee_number, creator_email,
            tag, project_name, project_number, project_id, es_source, es_id, product_type_id, public_test_case_id,
             create_time, update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)
        result = sql_fetchone_dict(sql, params)
        if result:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["tag"] = result["tag"].split(" | ") if result["tag"] else []

        sql = """
            select "name", "number"
            from public.test_case_tags
            where "number" in %(numbers)s;
        """

        if result and result["tag"]:
            r = sql_fetchall_dict(sql, {"numbers": tuple(result["tag"])})
            tag_map = dict()
            for i in r:
                tag_map[i["number"]] = i["name"]
                result["tag_name"] = [tag_map.get(j) for j in result["tag"]]

        sql = """
            SELECT id, test_case_id, "order", "type", params, "desc", expectation, create_time, update_time
            FROM public.test_steps
            WHERE test_case_id = %(test_case_id)s
            ORDER BY "order"
        """

        if result:
            result["test_steps"] = sql_fetchall_dict(sql, {"test_case_id": result.get("id")})
            for i in result["test_steps"]:
                i["create_time"] = i["create_time"].strftime("%Y-%m-%d %H:%M:%S")
                i["update_time"] = i["update_time"].strftime("%Y-%m-%d %H:%M:%S")
                i["params"] = json.loads(i["params"])

            result["test_method"] = result["test_method"].split(", ")

            requirements = TestCaseRequirementMap.objects.filter(
                test_case_id=result.get("id"), test_case_version=result.get("version"),
            )
            result["requirements"] = [i.to_dict() for i in requirements]

        return result

    def retrieve_by_number(self, number):
        params = {
            "number": number,
        }
        sql = """
           SELECT id, "name", "number", "module", module_2level, module_3level, "type", action_type, "version", status, 
               cached_status, priority, source, related_requirements,
               generation_method, test_method, execute_mode, function_safe_attrib, preconditions, steps, expected, "cycle", 
               vision_revert, vision_algorithm, remark, creator_name, creator_employee_number, creator_email,
               tag, project_name, project_number, project_id, es_source, es_id, product_type_id, public_test_case_id,
                create_time, update_time
           FROM {table_name}
           WHERE "number" = %(number)s
               LIMIT 1;
       """.format(table_name=self.table_name)
        result = sql_fetchone_dict(sql, params)
        if result:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["tag"] = result["tag"].split(" | ") if result["tag"] else []

        sql = """
               select "name", "number"
               from public.test_case_tags
               where "number" in %(numbers)s;
           """

        if result and result["tag"]:
            r = sql_fetchall_dict(sql, {"numbers": tuple(result["tag"])})
            tag_map = dict()
            for i in r:
                tag_map[i["number"]] = i["name"]
                result["tag_name"] = [tag_map.get(j) for j in result["tag"]]

        sql = """
               SELECT id, test_case_id, "order", "type", params, "desc", expectation, create_time, update_time
               FROM public.test_steps
               WHERE test_case_id = %(test_case_id)s
               ORDER BY "order"
           """

        if result:
            result["test_steps"] = sql_fetchall_dict(sql, {"test_case_id": result.get("id")})
            for i in result["test_steps"]:
                i["create_time"] = i["create_time"].strftime("%Y-%m-%d %H:%M:%S")
                i["update_time"] = i["update_time"].strftime("%Y-%m-%d %H:%M:%S")
                i["params"] = json.loads(i["params"])

            result["test_method"] = result["test_method"].split(", ")

            requirements = TestCaseRequirementMap.objects.filter(
                test_case_id=result.get("id"), test_case_version=result.get("version"),
            )
            result["requirements"] = [i.to_dict() for i in requirements]

        return result

    def delete(self, pk):
        tc = self.retrieve(pk)
        if not tc:
            return

        params = {
            "id": pk,
            "update_time": datetime.datetime.now()
        }
        sql = """
            UPDATE {table_name}     
                SET 
                is_deleted = true,
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
        )
        sql_execute(sql, params)

        test_case_delete_signal.send(
            self.__class__,
            project_number=tc.get("project_number"),
            action_type=tc.get("action_type"),
            module=tc.get("module"),
            type=tc.get("type"),
            execute_mode=tc.get("execute_mode"),
            test_case_id=pk
        )

    def update(self, **kwargs):
        with transaction.atomic():
            tc = self.retrieve(kwargs.get("pk"))
            if not tc:
                return

            now = datetime.datetime.now()
            params = {
                "id": kwargs.get("pk"),
                "update_time": now,
            }

            sql_set_list = []

            name = kwargs.get("name")
            if name is not None:
                sql_set_list.append("name = %(name)s")
                params["name"] = name

            type = kwargs.get("type")
            if type is not None:
                sql_set_list.append("type = %(type)s")
                params["type"] = type

            version = kwargs.get("version")
            if version is not None:
                sql_set_list.append("version = %(version)s")
                params["version"] = version

            status = kwargs.get("status")
            if status is not None:
                sql_set_list.append("status = %(status)s")
                params["status"] = status

            cached_status = kwargs.get("cached_status")
            if cached_status is not None:
                sql_set_list.append("cached_status = %(cached_status)s")
                params["cached_status"] = cached_status

            priority = kwargs.get("priority")
            if priority is not None:
                sql_set_list.append("priority = %(priority)s")
                params["priority"] = priority

            related_requirements = kwargs.get("related_requirements")
            if related_requirements is not None:
                sql_set_list.append("related_requirements = %(related_requirements)s")
                params["related_requirements"] = related_requirements

            source = kwargs.get("source")
            if source is not None:
                sql_set_list.append("source = %(source)s")
                params["source"] = source

            generation_method = kwargs.get("generation_method")
            if generation_method is not None:
                sql_set_list.append("generation_method = %(generation_method)s")
                params["generation_method"] = generation_method

            test_method = kwargs.get("test_method")
            if test_method is not None:
                sql_set_list.append("test_method = %(test_method)s")
                params["test_method"] = ", ".join(test_method)

            execute_mode = kwargs.get("execute_mode")
            if execute_mode is not None:
                sql_set_list.append("execute_mode = %(execute_mode)s")
                params["execute_mode"] = execute_mode

            function_safe_attrib = kwargs.get("function_safe_attrib")
            if function_safe_attrib is not None:
                sql_set_list.append("function_safe_attrib = %(function_safe_attrib)s")
                params["function_safe_attrib"] = function_safe_attrib

            preconditions = kwargs.get("preconditions")
            if preconditions is not None:
                sql_set_list.append("preconditions = %(preconditions)s")
                params["preconditions"] = preconditions

            steps = kwargs.get("steps")
            if steps is not None:
                sql_set_list.append("steps = %(steps)s")
                params["steps"] = steps

            expected = kwargs.get("expected")
            if expected is not None:
                sql_set_list.append("expected = %(expected)s")
                params["expected"] = expected

            remark = kwargs.get("remark")
            if remark is not None:
                sql_set_list.append("remark = %(remark)s")
                params["remark"] = remark

            cycle = kwargs.get("cycle")
            if cycle is not None:
                sql_set_list.append("cycle = %(cycle)s")
                params["cycle"] = cycle

            vision_revert = kwargs.get("vision_revert")
            if vision_revert is not None:
                sql_set_list.append("vision_revert = %(vision_revert)s")
                params["vision_revert"] = vision_revert

            vision_algorithm = kwargs.get("vision_algorithm")
            if vision_algorithm is not None:
                sql_set_list.append("vision_algorithm = %(vision_algorithm)s")
                params["vision_algorithm"] = vision_algorithm

            tag = kwargs.get("tag")
            if tag is not None:
                sql_set_list.append("tag = %(tag)s")
                params["tag"] = " | ".join(tag)

            es_source = kwargs.get("es_source")
            if es_source is not None:
                sql_set_list.append("es_source = %(es_source)s")
                params["es_source"] = es_source

            es_id = kwargs.get("es_id")
            if es_id is not None:
                sql_set_list.append("es_id = %(es_id)s")
                params["es_id"] = es_id

            product_type_id = kwargs.get("product_type_id")
            if product_type_id is not None:
                sql_set_list.append("product_type_id = %(product_type_id)s")
                params["product_type_id"] = product_type_id

            if not sql_set_list:
                return

            sql_set = ", ".join(sql_set_list)

            sql = """
                UPDATE {table_name}     
                    SET 
                    {sql_set},
                    update_time = %(update_time)s
                    WHERE id = %(id)s;
            """.format(
                table_name=self.table_name,
                sql_set=sql_set,
            )
            sql_execute(sql, params)

            test_steps = kwargs.get("test_steps")
            if test_steps is not None:
                sql = """
                    delete from public.test_steps 
                    where test_case_id = %(test_case_id)s
                    ;
                """
                sql_execute(sql, {"test_case_id": kwargs.get("pk")})

                sql = """
                    INSERT INTO public.test_steps (test_case_id, "order", "type", params, "desc", expectation,
                     create_time, update_time)
                    VALUES (%(test_case_id)s, %(order)s, %(type)s, %(params)s, %(desc)s, %(expectation)s
                    , %(create_time)s, %(update_time)s)
                    ;
                """
                for (index, step) in enumerate(test_steps):
                    params = {
                        "test_case_id": kwargs.get("pk"),
                        "order": index + 1,
                        "type": step.get("type"),
                        "params": step.get("params"),
                        "desc": step.get("desc"),
                        "expectation": step.get("expectation"),
                        "create_time": now,
                        "update_time": now,
                    }
                    sql_execute(sql, params)

            test_case_update_signal.send(
                self.__class__,
                project_number=tc.get("project_number"),
                action_type=tc.get("action_type"),
                module=tc.get("module"),
                type=tc.get("type"),
                execute_mode=tc.get("execute_mode"),
                test_case_id=kwargs.get("pk")
            )

    def update_steps(self, **kwargs):
        with transaction.atomic():
            tc = self.retrieve(kwargs.get("pk"))
            if not tc:
                return

            now = datetime.datetime.now()
            params = {
                "id": kwargs.get("pk"),
                "update_time": now,
            }

            sql_set_list = []
            if sql_set_list:
                sql_set = ", ".join(sql_set_list)
                sql = """
                    UPDATE {table_name}     
                        SET 
                        {sql_set},
                        update_time = %(update_time)s
                        WHERE id = %(id)s;
                """.format(
                    table_name=self.table_name,
                    sql_set=sql_set,
                )
                sql_execute(sql, params)

            test_steps = kwargs.get("test_steps")
            if test_steps is not None:
                sql = """
                    delete from public.test_steps 
                    where test_case_id = %(test_case_id)s
                    ;
                """
                sql_execute(sql, {"test_case_id": kwargs.get("pk")})

                sql = """
                    INSERT INTO public.test_steps (test_case_id, "order", "type", params, "desc", expectation,
                     create_time, update_time)
                    VALUES (%(test_case_id)s, %(order)s, %(type)s, %(params)s, %(desc)s, %(expectation)s
                    , %(create_time)s, %(update_time)s)
                    ;
                """
                for (index, step) in enumerate(test_steps):
                    params = {
                        "test_case_id": kwargs.get("pk"),
                        "order": index + 1,
                        "type": step.get("type"),
                        "params": step.get("params"),
                        "desc": step.get("desc"),
                        "expectation": step.get("expectation"),
                        "create_time": now,
                        "update_time": now,
                    }
                    sql_execute(sql, params)

            test_case_update_signal.send(
                self.__class__,
                project_number=tc.get("project_number"),
                action_type=tc.get("action_type"),
                module=tc.get("module"),
                type=tc.get("type"),
                execute_mode=tc.get("execute_mode"),
                test_case_id=kwargs.get("pk")
            )

    def move(self, pk, **kwargs):
        with transaction.atomic():
            t_module = kwargs.get("module")
            t_action_type = kwargs.get("action_type")

            r = self.retrieve(pk)

            module = [r.get("module"), r.get("module_2level"), r.get("module_3level")]
            module = ", ".join([i for i in module if i])
            action_type = r.get("action_type")

            if t_module == module and t_action_type == action_type:
                return

            test_steps = r.get("test_steps")
            for i in test_steps:
                i["params"] = json.dumps(i.get("params", {}))

            data = {
                "name": r.get("name"),
                "module": t_module,
                "type": r.get("type"),
                "action_type": t_action_type,
                "priority": r.get("priority"),
                "related_requirements": r.get("related_requirements"),
                "source": r.get("source"),
                "generation_method": r.get("generation_method"),
                "test_method": r.get("test_method"),
                "execute_mode": r.get("execute_mode"),
                "function_safe_attrib": r.get("function_safe_attrib"),
                "preconditions": r.get("preconditions"),
                "steps": r.get("steps"),
                "expected": r.get("expected"),
                "remark": r.get("remark"),
                "cycle": r.get("cycle"),
                "vision_revert": r.get("vision_revert"),
                "vision_algorithm": r.get("vision_algorithm"),
                "project_name": r.get("project_name"),
                "project_number": r.get("project_number"),
                "project_id": r.get("project_id"),
                "es_source": r.get("es_source"),
                "es_id": r.get("es_id"),
                "product_type_id": r.get("product_type_id"),
                "public_test_case_id": r.get("public_test_case_id"),
                "creator_name": r.get("creator_name"),
                "creator_employee_number": r.get("creator_employee_number"),
                "creator_email": r.get("creator_email"),
                "test_steps": test_steps,
            }

            self.delete(pk)

            self.create(**data)

    def copy_by_project(self, source_project_number, target_project_name, target_project_number, target_project_id,
                        user):
        with transaction.atomic():
            sql = """
                SELECT id 
                FROM public.test_cases2
                WHERE project_number = %(project_number)s AND not(is_deleted) AND status != 'DEPRECATED'
                ORDER BY id
            """
            source_test_case_ids = sql_fetchall_dict(sql, {"project_number": source_project_number})

            for i in source_test_case_ids:
                id_ = i.get("id")

                r = self.retrieve(id_)

                module = [r.get("module"), r.get("module_2level"), r.get("module_3level")]
                module = ", ".join([j for j in module if j])

                test_steps = r.get("test_steps")
                for j in test_steps:
                    j["params"] = json.dumps(j.get("params", {}))

                data = {
                    "name": r.get("name"),
                    "module": module,
                    "type": r.get("type"),
                    "action_type": r.get("action_type"),
                    "priority": r.get("priority"),
                    "related_requirements": r.get("related_requirements"),
                    "source": r.get("source"),
                    "generation_method": r.get("generation_method"),
                    "test_method": r.get("test_method"),
                    "execute_mode": r.get("execute_mode"),
                    "function_safe_attrib": r.get("function_safe_attrib"),
                    "preconditions": r.get("preconditions"),
                    "steps": r.get("steps"),
                    "expected": r.get("expected"),
                    "remark": r.get("remark"),
                    "cycle": r.get("cycle"),
                    "vision_revert": r.get("vision_revert"),
                    "vision_algorithm": r.get("vision_algorithm"),
                    "project_name": target_project_name,
                    "project_number": target_project_number,
                    "project_id": target_project_id,
                    "es_source": r.get("es_source"),
                    "es_id": r.get("es_id"),
                    "product_type_id": r.get("product_type_id"),
                    "public_test_case_id": r.get("public_test_case_id"),
                    "creator_name": user.username,
                    "creator_employee_number": user.employee_number,
                    "creator_email": user.email,
                    "test_steps": test_steps,
                }

                self.create(**data)

    def update_version(self, test_case_id, status):
        params = {
            "id": test_case_id,
            "update_time": datetime.datetime.now(),
            "status": status,
        }
        sql = """
            UPDATE {table_name}     
                SET 
                status = %(status)s,
                update_time = %(update_time)s
                WHERE id = %(id)s
                ;
        """.format(
            table_name=self.table_name
        )
        sql_execute(sql, params)


class TestStepModel:
    def __init__(self):
        self.table_name = "public.test_steps"

    def create(self, **kwargs):
        params = {
            "name": kwargs.get("name"),
            "number": kwargs.get("number"),
            "desc": kwargs.get("desc"),
            "create_time": datetime.datetime.now(),
            "update_time": datetime.datetime.now(),
        }

        sql = """
        INSERT INTO {table_name} ("name",  "number", "desc", create_time, update_time) 
        VALUES (%(name)s, %(number)s, %(desc)s, %(create_time)s, %(update_time)s);
    """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):
        name = kwargs.get("name")

        params = {
        }

        sql_where_list = []

        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
        SELECT id, "name", "number", "module", "type", "version", state, priority, source, generation_method,
            test_method, execute_mode, function_safe_attrib, "desc", "cycle", vision_revert, vision_algorithm, 
            tag, project_name, project_number, project_id, create_time, update_time
         FROM {table_name}
        {sql_where}
        {sql_order_by}
        {sql_limit}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, "name", "number", "desc", create_time, update_time
            FROM {table_name}
            WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
    """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        number = kwargs.get("number")
        if number is not None:
            sql_set_list.append("number = %(number)s")
            params["number"] = number

        desc = kwargs.get("desc")
        if desc is not None:
            sql_set_list.append("\"desc\" = %(desc)s")
            params["desc"] = desc

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
        UPDATE {table_name}     
            SET 
            {sql_set},
            update_time = %(update_time)s
            WHERE id = %(id)s;
    """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result


class PublicTestCaseModel:
    def __init__(self):
        self.table_name = "public.public_test_cases"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            module = kwargs.get("module")
            module_list = module.split(", ")
            module = module_list[0]
            module_2level = None
            module_3level = None
            if len(module_list) >= 2:
                module_2level = module_list[1]
            if len(module_list) >= 3:
                module_3level = module_list[2]

            params = {
                "name": kwargs.get("name"),
                "number": kwargs.get("number"),
                "module": module,
                "module_2level": module_2level,
                "module_3level": module_3level,
                "type": kwargs.get("type"),
                "action_type": kwargs.get("action_type"),
                "version": kwargs.get("version"),
                "status": kwargs.get("status"),
                "priority": kwargs.get("priority"),
                "related_requirements": kwargs.get("related_requirements"),
                "source": kwargs.get("source"),
                "generation_method": kwargs.get("generation_method"),
                "test_method": ", ".join(kwargs.get("test_method")),
                "execute_mode": kwargs.get("execute_mode"),
                "function_safe_attrib": kwargs.get("function_safe_attrib"),
                "preconditions": kwargs.get("preconditions"),
                "steps": kwargs.get("steps"),
                "expected": kwargs.get("expected"),
                "remark": kwargs.get("remark"),
                "cycle": kwargs.get("cycle"),
                "vision_revert": kwargs.get("vision_revert"),
                "vision_algorithm": kwargs.get("vision_algorithm"),
                "creator_name": kwargs.get("creator_name"),
                "creator_employee_number": kwargs.get("creator_employee_number"),
                "creator_email": kwargs.get("creator_email"),
                "tag": " | ".join(kwargs.get("tag")),
                "project_name": kwargs.get("project_name"),
                "project_number": kwargs.get("project_number"),
                "project_id": kwargs.get("project_id"),
                "es_source": kwargs.get("es_source"),
                "es_id": kwargs.get("es_id"),
                "create_time": now,
                "update_time": now,
            }

            sql = """
                INSERT INTO {table_name} ("name", "number", "module", module_2level, module_3level, "type", action_type, 
                    "version", status, priority,
                    source, generation_method, test_method, execute_mode, function_safe_attrib, preconditions, steps, expected, remark, 
                    "cycle", vision_revert, vision_algorithm, creator_name, creator_employee_number, creator_email,
                    tag, project_name, project_number, project_id, es_source, es_id, create_time, update_time) 
                VALUES (%(name)s, %(number)s, %(module)s, %(module_2level)s, %(module_3level)s, %(type)s, 
                    %(action_type)s, %(version)s, %(status)s, %(priority)s,
                    %(source)s, %(generation_method)s, %(test_method)s, %(execute_mode)s, %(function_safe_attrib)s, 
                    %(preconditions)s, %(steps)s, %(expected)s, %(remark)s, %(cycle)s, %(vision_revert)s,
                     %(vision_algorithm)s, %(creator_name)s, %(creator_employee_number)s, %(creator_email)s,
                    %(tag)s, %(project_name)s, %(project_number)s, %(project_id)s, %(es_source)s, %(es_id)s,
                     %(create_time)s, %(update_time)s)
                RETURNING id
                ;
            """.format(table_name=self.table_name)

            ts_id = sql_fetchone(sql, params)

            sql = """
                INSERT INTO public.public_test_steps (test_case_id, "order", "type", params, "desc", expectation,
                 create_time, 
                    update_time)
                VALUES (%(test_case_id)s, %(order)s, %(type)s, %(params)s, %(desc)s, %(expectation)s
                , %(create_time)s, %(update_time)s)
                ;
            """

            test_steps = kwargs.get("test_steps", [])
            for (index, step) in enumerate(test_steps):
                params = {
                    "test_case_id": ts_id,
                    "order": index + 1,
                    "type": step.get("type"),
                    "params": step.get("params"),
                    "desc": step.get("desc"),
                    "expectation": step.get("expectation"),
                    "create_time": now,
                    "update_time": now,
                }
                sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        sql_where_list.append("status != 'Deprecated'")

        id = kwargs.get("id")
        if id is not None and id != '':
            sql_where_list.append("id = %(id)s")
            params["id"] = id

        name = kwargs.get("name")
        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        name_re = kwargs.get("name_re")
        if name_re is not None and name_re != '':
            sql_where_list.append("name ilike %(name_re)s")
            params["name_re"] = f"%{name_re}%"

        status = kwargs.get("status", "")
        if status is not None and status != '':
            sql_where_list.append("status = %(status)s")
            params["status"] = status

        creator_employee_number = kwargs.get("creator_employee_number")
        if creator_employee_number is not None and creator_employee_number != '':
            sql_where_list.append("creator_employee_number = %(creator_employee_number)s")
            params["creator_employee_number"] = creator_employee_number

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        search_str = kwargs.get("search_str")
        if search_str is not None and search_str != '':
            sql_where_list.append("(name ~* %(search_str)s OR creator_name ~* %(search_str)s)")
            params["search_str"] = search_str

        execute_mode_list = kwargs.get("execute_mode_list", "")
        if execute_mode_list is not None and execute_mode_list != '':
            sql_where_list.append("execute_mode in %(execute_mode_list)s")
            params["execute_mode_list"] = tuple(execute_mode_list)

        action_type = kwargs.get("action_type")
        if action_type is not None and action_type != '':
            sql_where_list.append("action_type = %(action_type)s")
            params["action_type"] = action_type

        priority_list = kwargs.get("priority_list", "")
        if priority_list is not None and priority_list != '':
            sql_where_list.append("priority in %(priority_list)s")
            params["priority_list"] = tuple(priority_list)

        action_type_list = kwargs.get("action_type_list", "")
        if action_type_list is not None and action_type_list:
            sql_where_list.append("action_type in %(action_type_list)s")
            params["action_type_list"] = tuple(action_type_list)

        tags = kwargs.get("tags")
        if tags is not None and tags:
            for index, tag in enumerate(tags):
                sql_where_list.append(f"tag ilike %(tag{index})s")
                params[f"tag{index}"] = f"%{tag}%"

        es_source = kwargs.get("es_source")
        if es_source is not None and es_source != '':
            sql_where_list.append("es_source = %(es_source)s")
            params["es_source"] = es_source

        sql_where_list2 = []

        module_list = kwargs.get("module_list")
        if module_list:
            sql_where_list2.append("module in %(module_list)s")
            params["module_list"] = tuple(module_list)

        module_2level_list = kwargs.get("module_2level_list")
        if module_2level_list:
            for i, v in enumerate(module_2level_list):
                m = v.split(",")
                if len(m) != 2:
                    continue
                sql_where_list2.append(f"(module = %(module_2level_l{i})s and module_2level = %(module_2level_r{i})s)")
                params[f"module_2level_l{i}"] = m[0]
                params[f"module_2level_r{i}"] = m[1]

        module_3level_list = kwargs.get("module_3level_list")
        if module_3level_list:
            for i, v in enumerate(module_3level_list):
                m = v.split(",")
                if len(m) != 3:
                    continue
                sql_where_list2.append(
                    f"(module = %(module_3level_l{i})s and module_2level = %(module_3level_z{i})s) and module_3level = %(module_3level_r{i})s")
                params[f"module_3level_l{i}"] = m[0]
                params[f"module_3level_z{i}"] = m[1]
                params[f"module_3level_r{i}"] = m[2]

        if sql_where_list2:
            sql_where_list.append("(" + " OR ".join(sql_where_list2) + ")")

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY update_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
        SELECT id, "name", "number", "module", module_2level, module_3level, "type", action_type, "version", status, 
            priority, source,
            generation_method, test_method, execute_mode, function_safe_attrib, preconditions, steps, expected, "cycle", 
            vision_revert, vision_algorithm, remark, creator_name, creator_employee_number, creator_email,
            tag, project_name, project_number, project_id, es_source, es_id, create_time, update_time
         FROM {table_name}
        {sql_where}
        {sql_order_by}
        {sql_limit}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        for i in content["results"]:
            i["tag"] = i["tag"].split(" | ") if i["tag"] else []

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, "name", "number", "module", module_2level, module_3level, "type", action_type, "version", status,
            priority, source,
            generation_method, test_method, execute_mode, function_safe_attrib, preconditions, steps, expected, "cycle", 
            vision_revert, vision_algorithm, remark, creator_name, creator_employee_number, creator_email,
            tag, project_name, project_number, project_id, es_source, es_id, create_time, update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        sql = """
            SELECT id, test_case_id, "order", "type", params, "desc", expectation, create_time, update_time
            FROM public.public_test_steps
            WHERE test_case_id = %(test_case_id)s
            ORDER BY "order"
        """

        if result:
            result["test_steps"] = sql_fetchall_dict(sql, {"test_case_id": result.get("id")})

            result["test_method"] = result["test_method"].split(", ")
            result["tag"] = result["tag"].split(" | ") if result["tag"] else []

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
    """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()
            params = {
                "id": kwargs.get("pk"),
                "update_time": now,
            }

            sql_set_list = []

            name = kwargs.get("name")
            if name is not None:
                sql_set_list.append("name = %(name)s")
                params["name"] = name

            module = kwargs.get("module")
            if module is not None:

                module = kwargs.get("module")
                module_list = module.split(", ")
                module = module_list[0]
                module_2level = None
                module_3level = None
                if len(module_list) >= 2:
                    module_2level = module_list[1]
                if len(module_list) >= 3:
                    module_3level = module_list[2]

                sql_set_list.append("module = %(module)s")
                params["module"] = module

                sql_set_list.append("module_2level = %(module_2level)s")
                params["module_2level"] = module_2level

                sql_set_list.append("module_3level = %(module_3level)s")
                params["module_3level"] = module_3level

            type = kwargs.get("type")
            if type is not None:
                sql_set_list.append("type = %(type)s")
                params["type"] = type

            action_type = kwargs.get("action_type")
            if action_type is not None:
                sql_set_list.append("action_type = %(action_type)s")
                params["action_type"] = action_type

            version = kwargs.get("version")
            if version is not None:
                sql_set_list.append("version = %(version)s")
                params["version"] = version

            status = kwargs.get("status")
            if status is not None:
                sql_set_list.append("status = %(status)s")
                params["status"] = status

            priority = kwargs.get("priority")
            if priority is not None:
                sql_set_list.append("priority = %(priority)s")
                params["priority"] = priority

            # related_requirements = kwargs.get("related_requirements")
            # if related_requirements is not None:
            #     sql_set_list.append("related_requirements = %(related_requirements)s")
            #     params["related_requirements"] = related_requirements

            source = kwargs.get("source")
            if source is not None:
                sql_set_list.append("source = %(source)s")
                params["source"] = source

            generation_method = kwargs.get("generation_method")
            if generation_method is not None:
                sql_set_list.append("generation_method = %(generation_method)s")
                params["generation_method"] = generation_method

            test_method = kwargs.get("test_method")
            if test_method is not None:
                sql_set_list.append("test_method = %(test_method)s")
                params["test_method"] = ", ".join(test_method)

            execute_mode = kwargs.get("execute_mode")
            if execute_mode is not None:
                sql_set_list.append("execute_mode = %(execute_mode)s")
                params["execute_mode"] = execute_mode

            function_safe_attrib = kwargs.get("function_safe_attrib")
            if function_safe_attrib is not None:
                sql_set_list.append("function_safe_attrib = %(function_safe_attrib)s")
                params["function_safe_attrib"] = function_safe_attrib

            preconditions = kwargs.get("preconditions")
            if preconditions is not None:
                sql_set_list.append("preconditions = %(preconditions)s")
                params["preconditions"] = preconditions

            steps = kwargs.get("steps")
            if steps is not None:
                sql_set_list.append("steps = %(steps)s")
                params["steps"] = steps

            expected = kwargs.get("expected")
            if expected is not None:
                sql_set_list.append("expected = %(expected)s")
                params["expected"] = expected

            remark = kwargs.get("remark")
            if remark is not None:
                sql_set_list.append("remark = %(remark)s")
                params["remark"] = remark

            cycle = kwargs.get("cycle")
            if cycle is not None:
                sql_set_list.append("cycle = %(cycle)s")
                params["cycle"] = cycle

            vision_revert = kwargs.get("vision_revert")
            if vision_revert is not None:
                sql_set_list.append("vision_revert = %(vision_revert)s")
                params["vision_revert"] = vision_revert

            vision_algorithm = kwargs.get("vision_algorithm")
            if vision_algorithm is not None:
                sql_set_list.append("vision_algorithm = %(vision_algorithm)s")
                params["vision_algorithm"] = vision_algorithm

            tag = kwargs.get("tag")
            if tag is not None:
                sql_set_list.append("tag = %(tag)s")
                params["tag"] = " | ".join(tag)

            es_source = kwargs.get("es_source")
            if es_source is not None:
                sql_set_list.append("es_source = %(es_source)s")
                params["es_source"] = es_source

            es_id = kwargs.get("es_id")
            if es_id is not None:
                sql_set_list.append("es_id = %(es_id)s")
                params["es_id"] = es_id

            if not sql_set_list:
                return

            sql_set = ", ".join(sql_set_list)

            sql = """
                UPDATE {table_name}     
                    SET 
                    {sql_set},
                    update_time = %(update_time)s
                    WHERE id = %(id)s;
            """.format(
                table_name=self.table_name,
                sql_set=sql_set,
            )
            sql_execute(sql, params)

            test_steps = kwargs.get("test_steps")
            if test_steps is not None:
                sql = """
                    delete from public.public_test_steps 
                    where test_case_id = %(test_case_id)s
                    ;
                """
                sql_execute(sql, {"test_case_id": kwargs.get("pk")})

                sql = """
                    INSERT INTO public.public_test_steps (test_case_id, "order", "type", params, "desc", expectation
                    , create_time, update_time)
                    VALUES (%(test_case_id)s, %(order)s, %(type)s, %(params)s, %(desc)s, %(expectation)s
                    , %(create_time)s, %(update_time)s)
                    ;
                """
                for (index, step) in enumerate(test_steps):
                    params = {
                        "test_case_id": kwargs.get("pk"),
                        "order": index + 1,
                        "type": step.get("type"),
                        "params": step.get("params"),
                        "desc": step.get("desc"),
                        "expectation": step.get("expectation"),
                        "create_time": now,
                        "update_time": now,
                    }
                    sql_execute(sql, params)


class TestCaseArchiveModel:
    def __init__(self):
        self.table_name = "public.test_case_archives"

    def create(self, test_case_id):
        test_case = TestCaseModel().retrieve(test_case_id)

        params = {
            "test_case_id": test_case_id,
            "content": json.dumps(test_case),
            "tag": test_case.get("version"),
            "create_time": datetime.datetime.now()
        }

        sql = """
            INSERT INTO {table_name} (test_case_id, content, tag, create_time)
            VALUES (%(test_case_id)s, %(content)s, %(tag)s, %(create_time)s)
            ;
        """.format(table_name=self.table_name)

        sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        id = kwargs.get("id")
        if id is not None and id != '':
            sql_where_list.append("id = %(id)s")
            params["id"] = id

        test_case_id = kwargs.get("test_case_id")
        if test_case_id is not None and test_case_id != '':
            sql_where_list.append("test_case_id = %(test_case_id)s")
            params["test_case_id"] = test_case_id

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10000)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} 
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, test_case_id, tag, create_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)
        for i in content["results"]:
            i["create_time"] = i["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, test_case_id, content, tag, create_time
            FROM {table_name}
            WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["content"] = json.loads(result["content"])
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result


class TestCaseOperationLogModel(models.Model):
    project_number = models.CharField(max_length=255)
    action_type = models.CharField(max_length=255)
    module = models.CharField(max_length=255)
    type = models.CharField(max_length=255)
    execute_mode = models.CharField(max_length=255)
    test_case_id = models.IntegerField()
    operate_method = models.CharField(max_length=255)
    operate_time = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False
        app_label = 'test_case_tags'
        db_table = 'test_case_operation_log'
