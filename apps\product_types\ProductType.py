# -*-coding:utf-8 -*-
"""
# Author     : jid
# Time       : 2024/11/18 17:56
# Description: 产品类型库表
"""
from django.db import models
from django.db.models import Cha<PERSON><PERSON><PERSON>, DateTimeField, TextField


class ProductType(models.Model):
    name = Char<PERSON>ield(max_length=255, verbose_name=u"产品类型名称")
    number = CharField(max_length=255, blank=True, verbose_name=u'产品类型编号')
    desc = TextField(null=True, blank=True, verbose_name=u'描述')
    create_time = DateTimeField(auto_now_add=True, verbose_name=u'创建时间')
    update_time = DateTimeField(auto_now=True, verbose_name=u'更新时间')

    class Meta:
        app_label = 'product_types'
        db_table = 'product_types'
        managed = False

    def to_dict(self):
        return {
            "name": self.name,
            "number": self.number,
            "desc": self.desc,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S"),
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S")
        }
