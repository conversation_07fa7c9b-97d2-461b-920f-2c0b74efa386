import json
import logging
import os
import traceback
import time
import urllib.parse

import requests

logger = logging.getLogger("fs_app")

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
CONF_PATH = os.path.join(BASE_DIR, "config", "config.json")

with open(CONF_PATH, "rb") as f:
    CONF = json.load(f)

FS_CONF = CONF.get("fs_app")
BASE_URL = FS_CONF.get("url")
TIMEOUT = FS_CONF.get("timeout")
APP_ID = FS_CONF.get("app_id")
APP_SECRET = FS_CONF.get("app_secret")
MACHINE_RESERVE_TID = FS_CONF.get("machine_reserve_tid")


class FSApp:
    def __init__(self):
        self._tenant_access_token = None
        self._tenant_access_token_expire_time = None
        self._software_token = None
        self._software_token_expire_time = None

    def get_tenant_access_token(self):
        api = "/open-apis/auth/v3/tenant_access_token/internal"
        url = BASE_URL + api
        data = {
            "app_id": APP_ID,
            "app_secret": APP_SECRET
        }

        try:
            logger.info("获取tenant_access_token url: %s data: %s", url, data)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("获取tenant_access_token response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"获取tenant_access_token。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("获取tenant_access_token！\n%s", traceback.format_exc())
            return False, {"msg": f"获取tenant_access_token。{traceback.format_exc()}"}

        return True, response

    def get_token(self):
        if self._tenant_access_token_expire_time and time.time() < self._tenant_access_token_expire_time:
            return self._tenant_access_token
        else:
            f, data = self.get_tenant_access_token()
            if f:
                self._tenant_access_token = data.get("tenant_access_token")
                self._tenant_access_token_expire_time = time.time() + data.get("expire") - 60 * 60 * 30
                return self._tenant_access_token
            else:
                return None

    def get_software_token(self):
        if self._software_token_expire_time and time.time() < self._software_token_expire_time:
            return self._software_token
        else:
            f, data = self.get_software_tenant_access_token()
            if f:
                self._software_token = data.get("tenant_access_token")
                self._software_token_expire_time = time.time() + data.get("expire") - 60 * 60 * 30
                return self._software_token
            else:
                return None

    def get_software_tenant_access_token(self):
        api = "/open-apis/auth/v3/tenant_access_token/internal"
        url = BASE_URL + api
        # 修改为软件测试平台密钥
        data = {
            "app_id": "cli_a560d402f139d00b",
            "app_secret": "OOdaiB47QtN8Xs8LSRtHiOI04HyxxHDP"
        }

        try:
            logger.info("获取tenant_access_token url: %s data: %s", url, data)
            response = requests.post(url, json=data, timeout=TIMEOUT)
            logger.info("获取tenant_access_token response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"获取tenant_access_token。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("获取tenant_access_token！\n%s", traceback.format_exc())
            return False, {"msg": f"获取tenant_access_token。{traceback.format_exc()}"}

        return True, response

    def send_machine_reserve_msg(self, receive_email, title, machine, start_time,
                                 end_time, project, content, applicant, r_id):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送机台预约消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "type": "template",
                "data": {
                    "template_id": MACHINE_RESERVE_TID,
                    # "template_version_name": "1.0.0",
                    "template_variable": {
                        "title": title,
                        "machine": machine,
                        "time": f"{start_time} - {end_time}",
                        "project": project,
                        "content": content,
                        "applicant": applicant,
                        "r_id": r_id,
                    }
                }
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送机台预约消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送机台预约消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送机台预约消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送机台预约消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送机台预约消息。{traceback.format_exc()}"}

        return True, response

    def send_machine_reservation_time_out_msg(self, receive_email, title, machine, start_time,
                                              end_time, project, content, applicant):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送机台预约消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "type": "template",
                "data": {
                    "template_id": "AAqkYdaqET3Sr",
                    # "template_version_name": "1.0.0",
                    "template_variable": {
                        "title": title,
                        "machine": machine,
                        "time": f"{start_time} - {end_time}",
                        "project": project,
                        "content": content,
                        "applicant": applicant,
                    }
                }
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送机台预约超时释放消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送机台预约超时释放消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送机台预约超时释放消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送机台预约超时释放消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送机台预约超时释放消息。{traceback.format_exc()}"}

        return True, response

    def send_machine_reservation_apply_msg(self, receive_email, title, machine, start_time,
                                           end_time, project, content, applicant, r_id):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送机台预约申请消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "type": "template",
                "data": {
                    "template_id": "AAqkGhPc5XPdx",
                    "template_variable": {
                        "title": title,
                        "machine": machine,
                        "time": f"{start_time} - {end_time}",
                        "project": project,
                        "content": content,
                        "applicant": applicant,
                        "r_id": r_id,
                    }
                }
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送机台预约申请消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送机台预约申请消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送机台预约申请消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送机台预约申请消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送机台预约申请消息。{traceback.format_exc()}"}

        return True, response

    def send_msg(self, receive_email, msg):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "text": msg
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "text",
            "content": content
        }

        try:
            logger.info("发送消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送消息。{traceback.format_exc()}"}

        return True, response

    def send_test_exception_msg(self, receive_email, project, test_plan, tester, test_case_total,
                                test_case_exec, test_case_ng, test_record_item_id):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送测试异常消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        path = f"https://applink.feishu.cn/client/web_app/open?appId=cli_a560d402f139d00b&path={urllib.parse.quote(f'/test_records_v2/items/{test_record_item_id}')}"
        content = json.dumps(
            {
                "type": "template",
                "data": {
                    "template_id": "AAq7CMSi7BpLP",
                    "template_variable": {
                        "project": project,
                        "test_plan": test_plan,
                        "tester": tester,
                        "test_case_total": test_case_total,
                        "test_case_exec": test_case_exec,
                        "test_case_ng": test_case_ng,
                        "url": path
                    }
                }
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送测试异常消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送测试异常消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送测试异常消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送测试异常消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送测试异常消息。{traceback.format_exc()}"}

        return True, response

    def send_monitor_exception_msg(self, receive_email, project, plan, tester, code, name, cur_time, app_name,
                                   station_name):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送测试异常消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "type": "template",
                "data": {
                    "template_id": "AAqRrgqt1Eali",
                    "template_variable": {
                        "project": project,
                        "plan": plan,
                        "tester": tester,
                        "item": f"{name}({code})",
                        "cur_time": cur_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "app_name": app_name,
                        "station_name": station_name,
                    }
                }
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送监控异常消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送监控异常消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送监控异常消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送监控异常消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送监控异常消息。{traceback.format_exc()}"}

        return True, response

    def send_test_completed_msg(self, receive_email, project, test_plan, tester, test_case_total,
                                test_case_exec, test_case_ng):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送测试完成消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "type": "template",
                "data": {
                    "template_id": "AAqSZ2ckJQNvG",
                    "template_variable": {
                        "project": project,
                        "test_plan": test_plan,
                        "tester": tester,
                        "test_case_total": test_case_total,
                        "test_case_exec": test_case_exec,
                        "test_case_ng": test_case_ng,
                    }
                }
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送测试完成消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送测试完成消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送测试完成消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送测试完成消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送测试完成消息。{traceback.format_exc()}"}

        return True, response

    def send_machine_storage_alarm_msg(self, receive_email, project, machine, tester, cwd,
                                       remain_capacity, tip):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送测试异常消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "type": "template",
                "data": {
                    "template_id": "AAq7Cs8qFLK0b",
                    "template_variable": {
                        "project": project,
                        "machine": machine,
                        "tester": tester,
                        "cwd": cwd,
                        "remain_capacity": remain_capacity,
                        "tip": tip,
                    }
                }
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送测试异常消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送测试异常消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送测试异常消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送测试异常消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送测试异常消息。{traceback.format_exc()}"}

        return True, response

    def send_inspection_msg(self, receive_email, person_name, time, tip, link):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送点检消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "type": "template",
                "data": {
                    "template_id": "AAqz9pKXwyi68",
                    "template_variable": {
                        "person_name": person_name,
                        "time": time,
                        "tip": tip,
                        "link": link,
                    }
                }
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送点检消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送点检消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送点检消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送点检消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送点检消息。{traceback.format_exc()}"}

        return True, response

    def send_test_plan_create_msg(self, receive_email, project, plan_type, plan_name, creator, url_):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送测试计划创建消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "type": "template",
                "data": {
                    "template_id": "AAqdz6td6VGv6",
                    "template_variable": {
                        "project": project,
                        "plan_type": plan_type,
                        "plan_name": plan_name,
                        "creator": creator,
                        "url": url_,
                    }
                }
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送测试计划创建消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送测试计划创建消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送测试计划创建消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送测试计划创建消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送测试计划创建消息。{traceback.format_exc()}"}

        return True, response

    def send_msg_batch(self, open_ids, msg, msg_type="text"):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"批量发送消息。获取token失败"}

        api = "/open-apis/message/v4/batch_send/"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }

        data = {
            "open_ids": open_ids,
            "msg_type": msg_type,
        }

        if msg_type == "text":
            data["content"] = {
                "text": msg
            }
        elif msg_type == "post":
            data["content"] = {
                "post": msg
            }
        elif msg_type == "interactive":
            data["card"] = {
                "type": "template",
                "data": msg
            }

        try:
            logger.info("批量发送消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("批量发送消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"批量发送消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("批量发送消息！\n%s", traceback.format_exc())
            return False, {"msg": f"批量发送消息。{traceback.format_exc()}"}

        return True, response

    def get_users_by_department_id(self, department_id="0", page_token=None):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送消息。获取token失败"}

        api = "/open-apis/contact/v3/users/find_by_department"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        params = {
            "department_id": department_id,
            "page_token": page_token
        }

        try:
            logger.info("发送消息 url: %s params: %s", url, params)
            response = requests.get(url, headers=headers, params=params, timeout=TIMEOUT)
            logger.info("发送消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送消息。{traceback.format_exc()}"}

        return True, response

    def get_departments_by_department_id(self, department_id="0", page_token=None):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"发送消息。获取token失败"}

        api = f"/open-apis/contact/v3/departments/{department_id}/children"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }

        params = {
            "page_token": page_token,
            "page_size": 50
        }

        try:
            logger.info("发送消息 url: %s params: %s", url, params)
            response = requests.get(url, headers=headers, params=params, timeout=TIMEOUT)
            logger.info("发送消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送消息。{traceback.format_exc()}"}

        return True, response

    def send_mr_notification_msg(self, receive_email, project_path, source_branch, target_branch, mr_url, user,
                                 changeDescription, leader=False):
        """
        发送MR创建通知消息
        """
        token = self.get_software_token()
        if token is None:
            return False, {"msg": f"发送MR通知消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "data": {
                    "template_id": "AAqd7kuz2O99k",  # 使用MR模板
                    "template_variable": {
                        "project_path": project_path,
                        "source_branch": source_branch,
                        "target_branch": target_branch,
                        "user": user,  # 用户
                        "changeDescription": changeDescription,  # 描述
                        "url": {
                            "url": mr_url
                        }
                    }
                },
                "type": "template"
            }
        )
        if not leader:
            receive_email = "<EMAIL>"  # test 测试发给我
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送MR通知消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送MR通知消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送MR通知消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送MR通知消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送MR通知消息。{traceback.format_exc()}"}

        return True, response

    def send_branch_merge_msg(self, receive_email, project_path, source_branch, target_branch, m_url):
        """
        发送MR创建通知消息
        """
        token = self.get_software_token()
        if token is None:
            return False, {"msg": f"发送MR通知消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "data": {
                    "template_id": "AAqdxHiaLDIR3",  # 使用MR模板
                    "template_variable": {
                        "project_path": project_path,
                        "source_branch": source_branch,
                        "target_branch": target_branch,
                        "url": {
                            "url": m_url
                        }
                    }

                },
                "type": "template"
            }
        )
        # receive_email = "<EMAIL>"
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送MR通知消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送MR通知消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送MR通知消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送MR通知消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送MR通知消息。{traceback.format_exc()}"}

        return True, response

    def send_merge_request_msg(self, receive_email, project_path, source_branch, target_branch, mr_url):
        """
        发送MR创建通知消息
        """
        token = self.get_software_token()
        if token is None:
            return False, {"msg": f"发送MR通知消息。获取token失败"}

        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "data": {
                    "template_id": "AAqdzwqOR1z9K",  # 使用MR模板
                    "template_variable": {
                        "project_path": project_path,
                        "source_branch": source_branch,
                        "target_branch": target_branch,
                        "url": {
                            "url": mr_url
                        }
                    }
                },
                "type": "template"
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }

        try:
            logger.info("发送MR通知消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送MR通知消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送MR通知消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送MR通知消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送MR通知消息。{traceback.format_exc()}"}

        return True, response
    
    def send_jenkins_task_start_msg(self, receive_email, project_path, source_branch, result, user):
        """
        发送自动化打包开始的状态
        """
        token = self.get_software_token()
        if token is None:
            return False, {"msg": f"发送Jenkins任务状态消息。获取token失败"}
        
        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "data": {
                    "template_id": "AAqzzygj1vQfg",  # 使用MR模板
                    "template_variable": {
                        "project_path": project_path,
                        "source_branch": source_branch,
                        "result": result,
                        "user": user,  # 用户
                    }
                },
                "type": "template"
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }
        try:
            logger.info("发送Jenkins任务状态消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送Jenkins任务状态消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送Jenkins任务状态消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送Jenkins任务状态消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送Jenkins任务状态消息。{traceback.format_exc()}"}
        return True, response
    
    def send_jenkins_task_result_msg(self, receive_email, project_path, source_branch, result, user,total_time,upload_url):
        """
        发送自动打包的结果
        """
        token = self.get_software_token()
        if token is None:
            return False, {"msg": f"发送Jenkins任务状态消息。获取token失败"}
        
        api = "/open-apis/im/v1/messages?receive_id_type=email"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}"
        }
        content = json.dumps(
            {
                "data": {
                    "template_id": "AAqzhKnwwr4Fi",  # 使用MR模板
                    "template_variable": {
                        "project_path": project_path,
                        "source_branch": source_branch,
                        "result": result,
                        "user": user,  # 用户
                        "total_time": total_time,  # 描述
                        "url": {
                            "url": upload_url
                        }
                    }
                },
                "type": "template"
            }
        )
        data = {
            "receive_id": receive_email,
            "msg_type": "interactive",
            "content": content
        }
        try:
            logger.info("发送Jenkins任务状态消息 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("发送Jenkins任务状态消息 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"发送Jenkins任务状态消息。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("发送Jenkins任务状态消息！\n%s", traceback.format_exc())
            return False, {"msg": f"发送Jenkins任务状态消息。{traceback.format_exc()}"}
        return True, response




    def upload_file(self, file_name, file_type, content):
        token = self.get_software_token()
        if token is None:
            return False, {"msg": f"发送MR通知消息。获取token失败"}

        api = "/approval/openapi/v2/file/upload"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}",
        }

        data = {
            "name": file_name,
            "type": file_type,
        }
        files = {
            "content": (file_name, content),
        }

        try:
            logger.info("上传文件 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, data=data, files=files, timeout=TIMEOUT)
            logger.info("上传文件 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"上传文件。请求的返回状态码：{response.status_code}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("上传文件！\n%s", traceback.format_exc())
            return False, {"msg": f"上传文件。{traceback.format_exc()}"}

        return True, response

    def create_software_release_approval(self, open_id, **kwargs):
        token = self.get_token()
        if token is None:
            return False, {"msg": f"创建软件发布评审。获取token失败"}

        api = "/open-apis/approval/v4/instances"
        url = BASE_URL + api
        headers = {
            "Authorization": f"Bearer {token}",
        }

        approval_code = "21D9C46A-D9F2-48EF-9108-10D237C443EF"

        project_name = kwargs.get("project_name")
        product_name = kwargs.get("product_name")
        customer_name = kwargs.get("customer_name")
        if not customer_name:
            customer_name = None

        ver_list = []
        for ver in kwargs.get("versions", []):
            soft_type = ver.get("soft_type")
            soft_version = ver.get("soft_version")
            soft_version_info = ver.get("soft_version_info")
            is_update = ver.get("is_update")
            soft_path = ver.get("soft_path")
            soft_revision = ver.get("soft_revision")
            soft_risk = ver.get("soft_risk")

            ver_list.append(
                [
                    {
                        "id": "soft_type",
                        "type": "radioV2",
                        "value": soft_type,
                    },
                    {
                        "id": "soft_version",
                        "type": "input",
                        "value": soft_version,
                    },
                    {
                        "id": "soft_version_info",
                        "type": "textarea",
                        "value": soft_version_info,
                    },
                    {
                        "id": "is_update",
                        "type": "radioV2",
                        "value": is_update,
                    },
                    {
                        "id": "soft_path",
                        "type": "input",
                        "value": soft_path,
                    },
                    {
                        "id": "soft_revision",
                        "type": "textarea",
                        "value": soft_revision,
                    },
                    {
                        "id": "soft_risk",
                        "type": "textarea",
                        "value": soft_risk,
                    },
                ]
            )

        ota_validate = kwargs.get("ota_validate")
        release_reason = kwargs.get("release_reason")
        soft_report = kwargs.get("soft_report")
        soft_review_minutes = kwargs.get("soft_review_minutes")
        test_tool_update = kwargs.get("test_tool_update")
        tester = kwargs.get("tester", [])
        test_end_time = kwargs.get("test_end_time")
        test_note = kwargs.get("test_note")
        if not test_note:
            test_note = None
        hardware_developer = kwargs.get("hardware_developer", [])
        software_developer = kwargs.get("software_developer", [])
        pm = kwargs.get("pm", [])
        npi = kwargs.get("npi", [])

        form = [
            {
                "id": "project_name",
                "type": "radioV2",
                "value": project_name,
            },
            {
                "id": "product_name",
                "type": "input",
                "value": product_name,
            },
            {
                "id": "custom_name",
                "type": "radioV2",
                "value": customer_name,
            },
            {
                "id": "version_list",
                "type": "fieldList",
                "value": ver_list,
            },
            {
                "id": "ota_validate",
                "type": "checkboxV2",
                "value": ota_validate,
            },
            {
                "id": "release_reason",
                "type": "checkboxV2",
                "value": release_reason,
            },
            {
                "id": "soft_report",
                "type": "attachmentV2",
                "value": soft_report,
            },
            {
                "id": "soft_review_minutes",
                "type": "attachmentV2",
                "value": soft_review_minutes,
            },
            {
                "id": "test_tool_update",
                "type": "radioV2",
                "value": test_tool_update,
            },
            {
                "id": "tester",
                "type": "contact",
                "open_ids": tester,
            },
            {
                "id": "test_end_time",
                "type": "date",
                "value": test_end_time,
            },
            {
                "id": "test_note",
                "type": "textarea",
                "value": test_note,
            },
            {
                "id": "dardware_developer",
                "type": "contact",
                "open_ids": hardware_developer,
            },
            {
                "id": "software_developer",
                "type": "contact",
                "open_ids": software_developer,
            },
            {
                "id": "pm",
                "type": "contact",
                "open_ids": pm,
            },
            {
                "id": "npi",
                "type": "contact",
                "open_ids": npi,
            },

        ]
        form = json.dumps(form)

        data = {
            "approval_code": approval_code,
            "open_id": open_id,
            "form": form,
        }

        try:
            logger.info("创建软件发布评审 url: %s data: %s", url, data)
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
            logger.info("创建软件发布评审 response: %s", response.text)
            if response.status_code != 200:
                return False, {"msg": f"创建软件发布评审。请求的返回状态码：{response.status_code}。{response.text}"}
            response = response.json()
            if response.get("code") != 0:
                return False, response
        except Exception:
            logger.error("创建软件发布评审！\n%s", traceback.format_exc())
            return False, {"msg": f"创建软件发布评审。{traceback.format_exc()}"}

        return True, response


fs_app = FSApp()
