from django.urls import path

from .views import (
    TesterRoleView, TesterRoleDetailView,
    TesterSkillView, TesterSkillDetailView,
    TesterView, TesterDetailView,
    TesterGradesView, TesterRoleSkillsView, TesterSkillTypeView, TesterSkillTypeDetailView,
)

urlpatterns = [
    path("", TesterView.as_view()),
    path("/<int:pk>", TesterDetailView.as_view()),
    path("/<int:pk>/grades", TesterGradesView.as_view()),

    path("/roles", TesterRoleView.as_view()),
    path("/roles/<int:pk>", TesterRoleDetailView.as_view()),
    path("/roles/<int:pk>/skills", TesterRoleSkillsView.as_view()),

    path("/skills", TesterSkillView.as_view()),
    path("/skills/<int:pk>", TesterSkillDetailView.as_view()),

    path("/skill_types", TesterSkillTypeView.as_view()),
    path("/skill_types/<int:pk>", TesterSkillTypeDetailView.as_view()),
]
