--测试计划表
CREATE TABLE IF NOT EXISTS public.test_plans(
    id serial primary key, --id
    "name" varchar(255),  --名称,
    "desc" text, --描述
    durability bool,  --是否是耐久
    plan_type varchar(255),  --计划类型,
    sub_plans text, --子计划集合
    project_name varchar(255),   --项目名称
    project_number varchar(255),   --项目编号
    project_id varchar(255),   --项目id
    software_version varchar(255),   --产品软件版本
    product_version text ,   --产品版本
    creator_name varchar(255),   --创建人姓名
    creator_employee_number varchar(255),   --创建人工号
    creator_email varchar(255),   --创建人邮箱
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.test_plans OWNER TO atpms;