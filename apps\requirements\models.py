from django.db import models

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone


class RequirementModel:
    def get_test_cases_by_requirement(self, requirement_id, requirement_version):
        sql = """
            SELECT tc.id, tc."name", tc."number", tc."module", tc.module_2level, tc.module_3level, tc."type", tc.action_type, 
                tc."version", tc.status, tc.priority, tc.source, tc.generation_method, tc.test_method, tc.execute_mode, 
                tc.function_safe_attrib, tc.preconditions, tc.steps, tc.expected, tc."cycle", 
                tc.vision_revert, tc.vision_algorithm, tc.remark, tc.creator_name, tc.creator_employee_number, tc.creator_email,
                tc.tag, tc.project_name, tc.project_number, tc.project_id, tc.es_source, tc.es_id, tc.product_type_id, tc.public_test_case_id,
                tc.create_time, tc.update_time
            FROM public.test_case_requirement_map as m left join public.test_cases2 as tc 
            on m.test_case_id = tc.id and m.test_case_version = tc.version
            WHERE m.requirement_id = %s and m.requirement_version = %s
            ORDER BY m.create_time DESC
            
        """
        params = (requirement_id, requirement_version)
        result = sql_fetchall_dict(sql, params)
        if not result:
            return []
        else:
            return result
