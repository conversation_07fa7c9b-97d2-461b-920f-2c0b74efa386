import datetime

from django.db import models, transaction
from rest_framework import serializers
from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone

class PackToolUsageRecord(models.Model):
    project_name = models.CharField(max_length=255)
    project_number = models.CharField(max_length=255)
    component_code = models.CharField(max_length=255)
    software_version = models.Char<PERSON>ield(max_length=255)
    screen_touchpad_version = models.CharField(max_length=255, blank=True, null=True)
    pmic_flag = models.IntegerField()
    encrypt = models.IntegerField()
    encrypt_file = models.CharField(max_length=255, blank=True, null=True)
    output_type = models.Char<PERSON><PERSON>(max_length=255)
    srecord_type = models.CharField(max_length=255, blank=True, null=True)
    input_files = models.JSONField()
    packer_version = models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)
    output_file_name = models.Cha<PERSON><PERSON><PERSON>(max_length=255)
    output_file_md5 = models.Char<PERSON>ield(max_length=255)
    output_file_url = models.CharField(max_length=255)
    operator_name = models.Char<PERSON>ield(max_length=255)
    operator_email = models.CharField(max_length=255)
    create_time = models.DateTimeField(auto_now_add=True)
    package_status = models.IntegerField(default=0)  # 默认值为0，表示未验证
    output_file_url = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'pack_tool_usage_records'
        managed = False
        app_label = 'pack_tool'


class PackToolUsageRecordSerializer(serializers.ModelSerializer):
    create_time = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)

    class Meta:
        model = PackToolUsageRecord
        fields = '__all__'


class PackToolUsageRecordModel:
    def list(self, **kwargs):
        page = kwargs.get('page', 1)
        pagesize = kwargs.get('pagesize', 10)
        software_version_re = kwargs.get("software_version_re")
        screen_touchpad_version_re = kwargs.get("screen_touchpad_version_re")
        output_file_name_re = kwargs.get("output_file_name_re")
        output_file_md5_re = kwargs.get("output_file_md5_re")
        project_number = kwargs.get("project_number")

        records = PackToolUsageRecord.objects.all()

        records = records.order_by('-id')

        if software_version_re:
            records = records.filter(software_version__contains=software_version_re)

        if screen_touchpad_version_re:
            records = records.filter(screen_touchpad_version__contains=screen_touchpad_version_re)

        if output_file_name_re:
            records = records.filter(output_file_name__contains=output_file_name_re)

        if output_file_md5_re:
            records = records.filter(output_file_md5__contains=output_file_md5_re)

        if project_number:
            records = records.filter(project_number=project_number)

        # 分页
        start = (page - 1) * pagesize
        end = page * pagesize
        records = records[start:end]

        content = {
            "count": records.count(),
            "results": PackToolUsageRecordSerializer(records, many=True).data
        }

        return content

    def retrieve(self, pk):
        record = PackToolUsageRecord.objects.get(pk=pk)
        return PackToolUsageRecordSerializer(record).data

class PackageGenerationRecordsModel:
    def __init__(self):
        self.table_name = "public.pack_tool_usage_records"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            params = {
                "project_name": kwargs.get("project_name"),
                "project_number": kwargs.get("project_number"),
                "pack_input_file_type": kwargs.get("pack_input_file_type"),
                "pack_old_package_name": kwargs.get("pack_old_package_name"),
                "pack_old_package_version": kwargs.get("pack_old_package_version"),
                "pack_old_package_md5": kwargs.get("pack_old_package_md5"),
                "pack_new_package_name": kwargs.get("pack_new_package_name"),
                "pack_new_package_version": kwargs.get("pack_new_package_version"),
                "pack_new_package_md5": kwargs.get("pack_new_package_md5"),
                "software_version": kwargs.get("software_version"),
                "screen_touchpad_version": kwargs.get("screen_touchpad_version"),
                "output_file_name": kwargs.get("output_file_name"),
                "output_file_md5": kwargs.get("output_file_md5"),
                "output_file_url": kwargs.get("output_file_url"),
                "packer_version": kwargs.get("packer_version"),
                "operator_name": kwargs.get("operator_name"),
                "operator_email": kwargs.get("operator_email"),
                "create_time": now,
            }

            sql = """
                INSERT INTO {table_name} (project_name, project_number, pack_input_file_type, pack_old_package_name, pack_old_package_version,
                    pack_old_package_md5, pack_new_package_name, pack_new_package_version, pack_new_package_md5,software_version, screen_touchpad_version, output_file_name, output_file_md5, 
                    output_file_url, packer_version, operator_name, operator_email, create_time) 
                VALUES (%(project_name)s, %(project_number)s, %(pack_input_file_type)s, %(pack_old_package_name)s, %(pack_old_package_version)s,
                    %(pack_old_package_md5)s, %(pack_new_package_name)s, %(pack_new_package_version)s, %(pack_new_package_md5)s, %(software_version)s, %(screen_touchpad_version)s, %(output_file_name)s,
                    %(output_file_md5)s, %(output_file_url)s, %(packer_version)s, %(operator_name)s, 
                    %(operator_email)s, %(create_time)s)
                ;
            """.format(table_name=self.table_name)

            sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        output_file_md5 = kwargs.get("output_file_md5")
        if output_file_md5 is not None and output_file_md5 != '':
            sql_where_list.append("output_file_md5 = %(output_file_md5)s")
            params["output_file_md5"] = output_file_md5

        software_version_re = kwargs.get("software_version_re")
        if software_version_re is not None and software_version_re != '':
            sql_where_list.append("software_version ~* %(software_version_re)s")
            params["software_version_re"] = software_version_re

        screen_touchpad_version_re = kwargs.get("screen_touchpad_version_re")
        if screen_touchpad_version_re is not None and screen_touchpad_version_re != '':
            sql_where_list.append("screen_touchpad_version ~* %(screen_touchpad_version_re)s")
            params["screen_touchpad_version_re"] = screen_touchpad_version_re

        output_file_name_re = kwargs.get("output_file_name_re")
        if output_file_name_re is not None and output_file_name_re != '':
            sql_where_list.append("output_file_name ~* %(output_file_name_re)s")
            params["output_file_name_re"] = output_file_name_re

        packer_version_re = kwargs.get("packer_version_re")
        if packer_version_re is not None and packer_version_re != '':
            sql_where_list.append("packer_version ~* %(packer_version_re)s")
            params["packer_version_re"] = packer_version_re

        output_file_md5_re = kwargs.get("output_file_md5_re")
        if output_file_md5_re is not None and output_file_md5_re != '':
            sql_where_list.append("output_file_md5 ~* %(output_file_md5_re)s")
            params["output_file_md5_re"] = output_file_md5_re

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY create_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, project_name, project_number, pack_input_file_type, pack_old_package_name, pack_old_package_version, pack_old_package_md5,
                pack_new_package_name, pack_new_package_version, pack_new_package_md5, software_version, screen_touchpad_version, output_file_name, output_file_md5, output_file_url,
                packer_version, package_status, operator_name, operator_email, create_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        for result in content["results"]:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, project_name, project_number, pack_input_file_type, pack_old_package_name, pack_old_package_version, pack_old_package_md5,
                pack_new_package_name, pack_new_package_version, pack_new_package_md5, software_version, screen_touchpad_version, output_file_name, output_file_md5, output_file_url,
                packer_version, package_status, operator_name, operator_email, create_time
            FROM {table_name}
            WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def update_verify_status(self, id, package_status):
        params = {
            "id": id,
            "package_status": package_status,
        }

        sql = """
            UPDATE {table_name}     
                SET 
                package_status = %(package_status)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
        )

        sql_execute(sql, params)

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

class PackageShareUrlsModel:
    def __init__(self):
        self.table_name = "public.package_share_urls"

    def create(self, **kwargs):
        now = datetime.datetime.now()

        params = {
            "pack_record_id": kwargs.get("pack_record_id"),
            "token": kwargs.get("token"),
            "expiration_time": kwargs.get("expiration_time"),
            "creator_name": kwargs.get("creator_name"),
            "creator_email": kwargs.get("creator_email"),
            "create_time": now,
        }

        sql = """
            INSERT INTO {table_name} (pack_record_id, token, expiration_time, creator_name, creator_email,
                create_time) 
            VALUES (%(pack_record_id)s, %(token)s, %(expiration_time)s, %(creator_name)s, %(creator_email)s,
                %(create_time)s)
            ;
        """.format(table_name=self.table_name)

        sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        output_file_md5 = kwargs.get("output_file_md5")
        if output_file_md5 is not None and output_file_md5 != '':
            sql_where_list.append("output_file_md5 = %(output_file_md5)s")
            params["output_file_md5"] = output_file_md5

        software_version_re = kwargs.get("software_version_re")
        if software_version_re is not None and software_version_re != '':
            sql_where_list.append("software_version ~* %(software_version_re)s")
            params["software_version_re"] = software_version_re

        screen_touchpad_version_re = kwargs.get("screen_touchpad_version_re")
        if screen_touchpad_version_re is not None and screen_touchpad_version_re != '':
            sql_where_list.append("screen_touchpad_version ~* %(screen_touchpad_version_re)s")
            params["screen_touchpad_version_re"] = screen_touchpad_version_re

        output_file_name_re = kwargs.get("output_file_name_re")
        if output_file_name_re is not None and output_file_name_re != '':
            sql_where_list.append("output_file_name ~* %(output_file_name_re)s")
            params["output_file_name_re"] = output_file_name_re

        packer_version_re = kwargs.get("packer_version_re")
        if packer_version_re is not None and packer_version_re != '':
            sql_where_list.append("packer_version ~* %(packer_version_re)s")
            params["packer_version_re"] = packer_version_re

        output_file_md5_re = kwargs.get("output_file_md5_re")
        if output_file_md5_re is not None and output_file_md5_re != '':
            sql_where_list.append("output_file_md5 ~* %(output_file_md5_re)s")
            params["output_file_md5_re"] = output_file_md5_re

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY create_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, project_name, project_number, pack_input_file_type, pack_old_package_name, pack_old_package_version, pack_old_package_md5,
                pack_, pack_new_package_version, pack_new_package_md5, software_version, screen_touchpad_version, output_file_name, output_file_md5, output_file_url,
                packer_version, package_status, operator_name, operator_email, create_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        for result in content["results"]:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, token):
        params = {
            "token": token,
        }
        sql = """
            SELECT url.id, url.expiration_time, url.create_time, url.creator_name, url.creator_email,
                ur.project_name, ur.project_number, ur.pack_old_package_version, ur.pack_new_package_version, ur.operator_name,
                ur.package_status, ur.packer_version,
                ur.output_file_url, ur.software_version, ur.screen_touchpad_version, ur.output_file_name, ur.output_file_md5
            FROM {table_name} as url left join public.pack_tool_usage_records as ur
            on url.pack_record_id = ur.id
            WHERE url.token = %(token)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result["create_time"] + datetime.timedelta(seconds=result["expiration_time"]) < datetime.datetime.now():
            result = None

        if result:
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def update_verify_status(self, id, package_status):
        params = {
            "id": id,
            "package_status": package_status,
        }

        sql = """
            UPDATE {table_name}     
                SET 
                package_status = %(package_status)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
        )

        sql_execute(sql, params)

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result