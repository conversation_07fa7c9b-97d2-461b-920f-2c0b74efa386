"""
Webhook相关工具函数
"""
import grpc
import logging
import time
import random
from utils.proto import logdir_pb2_grpc
from utils.proto import logdir_pb2

logger = logging.getLogger("auto_jenkins")

def create_webhook(git_url):
    """
    创建GitLab Webhook
    Args:
        git_url (str): Git仓库URL
        branch (str): 分支名称
    Returns:
        bool: 是否成功
    """
    try:
        # 创建gRPC连接
        channel = grpc.insecure_channel(f'localhost:50051')
        stub = logdir_pb2_grpc.GitlabServiceStub(channel)
        
        # 调用gRPC方法
        request = logdir_pb2.CreateWebhookRequest()
        request.git_url = git_url
        
        response = stub.CreateWebhook(request)
        
        if response.success:
            logger.info(f"创建webhook成功: {git_url}")
            return True
        else:
            logger.error(f"创建webhook失败: {response.message}")
            return False
            
    except Exception as e:
        logger.error(f"创建webhook异常: {str(e)}")
        return False

def generate_build_id(job_name):
      """生成唯一构建ID"""
      timestamp = int(time.time())
      random_num = random.randint(1000, 9999)
      build_id = f"{job_name}_{timestamp}_{random_num}"
      return build_id


def software_hash():
    return {
        "显示屏MCU": "DISPLAY_MCU",
        "电机MCU": "MOTOR_MCU",
        "图形显示MCU": "OSD_MCU",
        "上位机": "HMI",
        "VDS系统OS": "VDS/OS",
        "VDS系统MCU": "VDS/MCU",
        "VDS系统APK": "VDS/APK",
        "SIMBOX": "SIMBOX",
        "主机": "HOST"
    }