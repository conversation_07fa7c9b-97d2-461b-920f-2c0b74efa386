CREATE TABLE public.chip_iic (
    id SERIAL PRIMARY KEY,
    type VARCHAR(10)[] NOT NULL,
    func_work_model VARCHAR(20)[] NOT NULL,
    func_alt_value VARCHAR(50),
    iic_type VARCHAR(20)[] NOT NULL,
    channel VARCHAR(50),
    communication_speed VARCHAR(20),
    device_address JSONB DEFAULT '{}'::jsonb,
    work_mode VARCHAR(10)[] NOT NULL,
    sub_addr_type VARCHAR(50)[] NOT NULL,
    sub_addr_byte JSONB DEFAULT '{}'::jsonb,
    enable_config VARCHAR(10)[] NOT NULL
);
ALTER TABLE public.chip_iic ALTER COLUMN type DROP NOT NULL;
ALTER TABLE public.chip_iic ALTER COLUMN func_work_model DROP NOT NULL;
ALTER TABLE public.chip_iic ALTER COLUMN iic_type DROP NOT NULL;
ALTER TABLE public.chip_iic ALTER COLUMN work_mode DROP NOT NULL;
ALTER TABLE public.chip_iic ALTER COLUMN sub_addr_type DROP NOT NULL;
ALTER TABLE public.chip_iic ALTER COLUMN enable_config DROP NOT NULL;


ALTER TABLE public.chip_iic
ADD COLUMN name VARCHAR(100),  -- 名称
ADD COLUMN des TEXT;           -- 描述