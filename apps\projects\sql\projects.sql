--项目表
CREATE TABLE IF NOT EXISTS public.projects(
    id serial primary key, --id
    "name" varchar(255),
    "number" varchar(255) unique ,
    related_people text,
    msg_effective_time_start time , --每天消息推送生效开始时间
    msg_effective_time_end time, --每天消息推送生效结束时间
    configs jsonb, --项目配置
    create_time timestamp, --创建时间
    update_time timestamp --更新时间
);
ALTER TABLE public.projects OWNER TO atpms;


CREATE TABLE IF NOT EXISTS public.project_module_map(
    id serial primary key, --id
    project_number varchar(255), --项目编号
    module_id int REFERENCES public.functions2(id) on delete cascade, --模块id
    unique(project_number, module_id)
);
ALTER TABLE public.project_module_map OWNER TO atpms;

CREATE TABLE IF NOT EXISTS public.project_inspection_item_map(
    id serial primary key, --id
    project_number varchar(255), --项目编号
    item_id int REFERENCES public.inspection_items(id) on delete cascade, --模块id
    unique(project_number, item_id)
);
ALTER TABLE public.project_inspection_item_map OWNER TO atpms;