from django.urls import path

from .views import (
    TestPlansView, TestPlanDetailView, TestPlanDetailV2View, TestPlanSyncView, TestPlanReviewRView,
    TestPlanUpdateStatusView, TestCaseExecView, TestCaseResultSyncView, TestPlanTestCasesView,
    TestPlanTestCasesDeleteView, TestPlanTestCasesAddView, TestPlanTestCasesUpdateView,
    TestPlanCopyView, AtFilesView, RelatedVersionsPushView,
)

urlpatterns = [
    path("", TestPlansView.as_view()),

    path("/at_files", AtFilesView.as_view()),

    path("/sync", TestPlanSyncView.as_view()),

    path("/review_r", TestPlanReviewRView.as_view()),

    path("/update_status", TestPlanUpdateStatusView.as_view()),

    path("/test_case_exec", TestCaseExecView.as_view()),

    path("/sync_test_result", TestCaseResultSyncView.as_view()),

    path("/push_related_versions", RelatedVersionsPushView.as_view()),

    path("/<int:pk>", TestPlanDetailView.as_view()),

    path("/<int:pk>/copy", TestPlanCopyView.as_view()),

    path("/<int:pk>/test_cases", TestPlanTestCasesView.as_view()),

    path("/<int:pk>/test_cases/delete", TestPlanTestCasesDeleteView.as_view()),

    path("/<int:pk>/test_cases/add", TestPlanTestCasesAddView.as_view()),

    path("/<int:pk>/test_cases/update", TestPlanTestCasesUpdateView.as_view()),

    path("/<int:pk>/hc", TestPlanDetailV2View.as_view()),
]
