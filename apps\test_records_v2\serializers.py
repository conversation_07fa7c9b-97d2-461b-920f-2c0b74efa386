from rest_framework import serializers


class TestRecordListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    project_number = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    test_plan_name = serializers.Char<PERSON>ield(required=False, allow_blank=True, allow_null=True)
    tester_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    machine_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    test_plan_id = serializers.IntegerField(required=False)


class TestRecordItemListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    project_number = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)
    test_record_id = serializers.Char<PERSON><PERSON>(max_length=255, required=False, allow_null=True, allow_blank=True)
    test_case_id = serializers.Integer<PERSON>ield(required=False, allow_null=True)
    test_plan_name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    test_case_name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    result_list = serializers.ListField(child=serializers.ChoiceField(choices=['1', '0', '2']), min_length=0,
                                        required=False)
    status_list = serializers.ListField(child=serializers.IntegerField(), min_length=0, required=False)
    order = serializers.ListField(child=serializers.CharField(max_length=255), min_length=0, required=False)


class TestRecordItemResultDecideSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    result = serializers.BooleanField()


class TestRecordItemFalseAlarmSerializer(serializers.Serializer):
    id = serializers.IntegerField()


class OccurVersionSerializer(serializers.Serializer):
    num = serializers.CharField(max_length=255)
    type = serializers.CharField(max_length=255)


class IssuePushSerializer(serializers.Serializer):
    id = serializers.IntegerField()

    title = serializers.CharField(max_length=120)
    projectCode = serializers.CharField(max_length=255)
    module = serializers.CharField(max_length=255)
    probability = serializers.CharField(max_length=255)
    testerOpenIds = serializers.ListField(child=serializers.EmailField(), min_length=1)
    occurVersionList = serializers.ListField(child=OccurVersionSerializer(), min_length=1)
    frontCondition = serializers.CharField(max_length=255)
    testStep = serializers.CharField(max_length=255)
    expectResult = serializers.CharField(max_length=255)
    actualResult = serializers.CharField(max_length=255)
    remark = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    handlerIdList = serializers.ListField(child=serializers.EmailField(), min_length=1)
    starIdList = serializers.ListField(child=serializers.EmailField(), min_length=0, allow_null=None)
    beginTime = serializers.DateTimeField()
    endTime = serializers.DateTimeField()
    level = serializers.CharField(max_length=255)
    occurProjectProgress = serializers.CharField(max_length=255)
    test_case_number = serializers.CharField(max_length=255)
    test_case_version = serializers.CharField(max_length=255)


class TestRecordSerializer(serializers.Serializer):
    test_record_id = serializers.CharField(max_length=255)
    order = serializers.IntegerField()
    project_number = serializers.CharField(max_length=255)
    project_name = serializers.CharField(max_length=255)
    test_plan_name = serializers.CharField(max_length=255)
    test_plan_id = serializers.IntegerField()
    test_case_number = serializers.CharField(max_length=255)
    test_case_name = serializers.CharField(max_length=255)
    test_case_id = serializers.IntegerField()
    test_case_version = serializers.CharField(max_length=255)
    start_time = serializers.DateTimeField()
    end_time = serializers.DateTimeField()
    machine_number = serializers.CharField(max_length=255)
    result = serializers.BooleanField(required=False, allow_null=True, default=None)


class ResourceSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    path = serializers.CharField(max_length=255)


class TestRecordItemSourceListSerializer(serializers.Serializer):
    test_record_item_id = serializers.IntegerField()


class TestRecordItemSourcesSerializer(serializers.Serializer):
    test_record_item_id = serializers.IntegerField()
    resource = serializers.ListField(child=ResourceSerializer(), min_length=1)


class TestRecordItemCollectSerializer(serializers.Serializer):
    collected = serializers.BooleanField()
    test_record_item_id = serializers.IntegerField()
