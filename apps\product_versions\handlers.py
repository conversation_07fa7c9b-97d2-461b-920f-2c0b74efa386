import logging

from django.dispatch import receiver

from test_plans_v2.signals import plan_create, plan_create2
from .signals import product_version_update
from .models import ProductVersionModel
from test_products.models import TestProductModel
from utils.fs_service import FSService

fs_service = FSService()

logger = logging.getLogger("product_versions")


@receiver(plan_create)
def plan_create_handler(sender, signal, **kwargs):
    logger.info("sender: %s, signal: %s, kwargs: %s", sender, signal, kwargs)

    project_number = kwargs.get("project_number")
    version_name = kwargs.get("version_name")

    model = ProductVersionModel()

    content = model.list(project_number=project_number, version_name=version_name)
    logger.info("content: %s", content)
    if not content["results"]:
        return

    r = content["results"][0]

    if r.get("status") != "SUBMITTED":
        return

    model.update_status(r.get("id"), "TESTING")
    logger.info("update status to TESTING")


def generate_version_name(test_product, version_names):
    hv = test_product.get("hardware_versions", [])
    sv = test_product.get("software_versions", [])
    for i in hv:
        version_names.append(i.get("name"))
    for i in sv:
        version_names.append(i.get("name"))
    components = test_product.get("components", [])

    for i in components:
        generate_version_name(i, version_names)


@receiver(plan_create2)
def plan_create2_handler(sender, signal, **kwargs):
    logger.info("sender: %s, signal: %s, kwargs: %s", sender, signal, kwargs)

    project_number = kwargs.get("project_number")
    test_product_id = kwargs.get("test_product_id")

    if not test_product_id:
        return
    test_product = TestProductModel().retrieve(test_product_id)
    if not test_product:
        return

    version_names = []
    generate_version_name(test_product, version_names)

    if not version_names:
        return

    model = ProductVersionModel()

    content = model.list(project_number=project_number, version_name_list=version_names)
    logger.info("content: %s", content)
    if not content["results"]:
        return

    for r in content["results"]:

        if r.get("status") != "SUBMITTED":
            continue

        model.update_status(r.get("id"), "TESTING")

    logger.info("update status to TESTING")


@receiver(product_version_update)
def product_version_update_handler(sender, signal, **kwargs):
    logger.info("sender: %s, signal: %s, kwargs: %s", sender, signal, kwargs)

    project_number = kwargs.get("project_number")
    version_name = kwargs.get("version_name")
    status = kwargs.get("status")
    old_status = kwargs.get("old_status")

    if status == "SUBMITTED":
        s = "HAVE_TEST"
    elif status == "TESTING":
        s = "TESTING"
    elif status == "FAILED":
        s = "NG"
    elif status == "SMOKE_TEST_PASSED":
        s = "SMOKE_OK"
    elif status == "PASSED":
        s = "ALL_OK"
    elif status == "SPECIAL_VERSION_RELEASED":
        s = "SMOKE_PUBLISH"
    elif status == "RELEASED":
        s = "PUBLISH"
    elif status == "PRODUCTION":
        s = "MP"
    elif status == "DEPRECATED":
        s = "ABANDON"
    else:
        return

    f, data = fs_service.update_product_version_status(project_number, version_name, s)
