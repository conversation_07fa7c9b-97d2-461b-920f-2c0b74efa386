from rest_framework import serializers


class PackToolUsageRecordListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    project_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    software_version_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    screen_touchpad_version_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    output_file_name_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    output_file_md5_re = serializers.Char<PERSON>ield(max_length=255, required=False, allow_blank=True, allow_null=True)
    packer_version_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class PackToolUsageRecordSerializer(serializers.Serializer):
    project_name = serializers.Char<PERSON>ield(max_length=255)
    project_number = serializers.CharField(max_length=255)
    component_code = serializers.CharField(max_length=255)
    software_version = serializers.ListField(
        child=serializers.IntegerField(min_value=0, max_value=255), min_length=2, max_length=2)
    screen_touchpad_version = serializers.ListField(
        child=serializers.IntegerField(min_value=0, max_value=255), min_length=2, max_length=2)
    input_files = serializers.ListField(child=serializers.FileField(), min_length=1)
    input_file_identifs = serializers.ListField(child=serializers.CharField(), min_length=1)
    input_file_crc_vailds = serializers.ListField(child=serializers.CharField(), min_length=1)
    output_type = serializers.ChoiceField(choices=["bin", "hex", "s19"])
    srecord_type = serializers.ChoiceField(choices=[1, 2, 3])
    output_path = serializers.CharField(max_length=255)
    pmic_flag = serializers.IntegerField()
    encrypt = serializers.IntegerField()
    encrypt_file = serializers.FileField(required=False)
    package_status = serializers.ChoiceField(choices=[1, 2], required=False, allow_blank=True, allow_null=True)
    output_file_url = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    pack_input_file_type = serializers.ChoiceField(choices=["bin", "hex", "zip"], required=False, allow_blank=True, allow_null=True)
    pack_old_package = serializers.FileField(required=False)
    pack_old_package_version = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    pack_new_package = serializers.FileField(required=False)
    pack_new_package_version = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    output_file_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    packer_version = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class PackToolUsageRecordVerifySerializer(serializers.Serializer):
    id = serializers.IntegerField()
    package_status = serializers.ChoiceField(choices=[1, 2], required=False, allow_blank=True, allow_null=True)
    output_file_url = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class PackageShareUrlSerializer(serializers.Serializer):
    pack_record_id = serializers.IntegerField()
    expiration_time = serializers.IntegerField(default=60 * 60 * 6)