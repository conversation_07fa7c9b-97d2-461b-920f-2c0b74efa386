import traceback
import logging

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from .serializers import (
    GetProductVersionsSerializer,
    GetProductVersionsSerializer_init
)
from .models import ProductVersionModel
from tester_management.models import Tester

logger = logging.getLogger("machine")


class ProductVersionsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # 增加project_number空字符过滤
            if 'project_number' in request.query_params.keys() and request.query_params['project_number'] == '':
                serializer = GetProductVersionsSerializer_init(data=request.query_params)

            elif 'project_number' not in request.query_params.keys():
                serializer = GetProductVersionsSerializer_init(data=request.query_params)

            else:
                serializer = GetProductVersionsSerializer(data=request.query_params)

            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            is_team = serializer.validated_data.get("is_team")




            user = request.user

            model = ProductVersionModel()

            if is_team:
                tester = Tester.objects.filter(email=user.email).first()
                if tester is None:
                    return Response({"err_code": 1, "msg": "测试人员未注册。"}, status.HTTP_400_BAD_REQUEST)
                team_members = Tester.objects.filter(group=tester.group)
                person_email_list = [member.email for member in team_members]
                content = model.list(**serializer.validated_data, person_email_list=person_email_list)
                content["team_members"] = [{"name": member.name, "email": member.email} for member in team_members]
            else:
                content = model.list(**serializer.validated_data, person_email=user.email)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
