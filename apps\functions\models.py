import datetime
import logging

from utils.sql_helper import (
    sql_execute, sql_fetchone_dict, sql_fetchall_dict, sql_fetchone, sql_insert_many
)

logger = logging.getLogger("machines")


def parse_functions(functions, parent_id=None, parent_number=""):
    results = []
    tmp = [i for i in functions]

    for f in tmp:
        if f["parent_id"] == parent_id or (parent_id is None and f["parent_id"] is None):
            if parent_number:
                f["number2"] = parent_number + "," + f["number"]
            else:
                f["number2"] = f["number"]
            children = parse_functions(functions, parent_id=f["id"], parent_number=f["number2"])
            if children:
                f["children"] = children
            results.append(f)

    return results


class FunctionModel:
    def __init__(self):
        self.table_name = "public.functions2"

    def create(self, **kwargs):
        parent_id = kwargs.get("parent_id")

        level = 1
        if parent_id:
            r = self.retrieve(parent_id)
            if r:
                pl = r.get("level")
                level = pl + 1

        params = {
            "name": kwargs.get("name"),
            "number": kwargs.get("number"),
            "desc": kwargs.get("desc"),
            "parent_id": parent_id,
            "level": level,
            "create_time": datetime.datetime.now(),
            "update_time": datetime.datetime.now(),
        }

        sql = """
            INSERT INTO {table_name} ("name",  "number", "desc", parent_id, "level", create_time, update_time) 
            VALUES (%(name)s, %(number)s, %(desc)s, %(parent_id)s, %(level)s, %(create_time)s, %(update_time)s);
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):
        name = kwargs.get("name")

        params = {
        }

        sql_where_list = []

        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name}
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql = """
            SELECT id, "name", "number", "desc", parent_id, "level", create_time, update_time, deprecated
                FROM public.functions2
                {sql_where}
                {sql_order_by}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
        )

        results = sql_fetchall_dict(sql, params)

        content = {
            "results": parse_functions(results)
        }

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", "number", "desc", parent_id, "level", create_time, update_time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }

        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        number = kwargs.get("number")
        if number is not None:
            sql_set_list.append("number = %(number)s")
            params["number"] = number

        desc = kwargs.get("desc")
        if desc is not None:
            sql_set_list.append("\"desc\" = %(desc)s")
            params["desc"] = desc
        
        deprecated = kwargs.get("deprecated")
        if deprecated is not None:
            sql_set_list.append("deprecated = %(deprecated)s")
            params["deprecated"] = deprecated

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result

    def bulk_create(self, data):
        col_names = ["name", "number", "desc"]
        argslist = []

        now = datetime.datetime.now()

        for i in data:
            temp_list = []
            for j in col_names:
                temp_list.append(i.get(j))
            temp_list.append(now)
            temp_list.append(now)
            argslist.append(temp_list)

        sql = """
            INSERT INTO {table_name} ("name", "number", "desc", create_time, update_time) 
            VALUES %s 
            ON CONFLICT ("name") DO NOTHING
            ;
        """.format(table_name=self.table_name)

        sql_insert_many(sql, argslist=argslist)

    def test_case_stat(self, project_number="", action_type=""):

        params = {}

        sql_where_list = ["not(is_deleted)"]

        if project_number:
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        if action_type:
            sql_where_list.append("action_type = %(action_type)s")
            params["action_type"] = action_type

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        sql = """
            SELECT "module", module_2level, module_3level, 
                count("module") as count1, count(module_2level) as count2, count(module_3level) as count3
                FROM public.test_cases2
                {sql_where}
                GROUP BY "module", module_2level, module_3level 
            ;
        """.format(sql_where=sql_where)

        result = sql_fetchall_dict(sql, params)

        data = {}
        for i in result:
            module = i.get("module")
            module_2level = i.get("module_2level")
            module_3level = i.get("module_3level")

            if data.get(module) is None:
                data[module] = 0
            data[module] += i.get("count1")

            if module_2level:
                k = f"{module},{module_2level}"
                if data.get(k) is None:
                    data[k] = 0
                data[k] += i.get("count2")

            if module_3level:
                k = f"{module},{module_2level},{module_3level}"
                if data.get(k) is None:
                    data[k] = 0
                data[k] += i.get("count3")

        return data