syntax = "proto3";

package logdir;

service LogDirService {
    rpc GenerateDatesToKeep (Empty) returns (LogDirResponse);
    rpc DeleteOldDirs (LogDirRequest) returns (OperationStatus);
}

service GitlabService {
    rpc ListAllProjects (Empty) returns (ListAllProjectsResponse);
    rpc SyncProjects (Project) returns (ProjectSyncRequest);
    rpc CreateOrGetMergeRequest (CreateOrGetMergeRequestRequest) returns (CreateOrGetMergeRequestResponse);  
    rpc QueryMRStatus(QueryMRStatusRequest)  returns (QueryMRStatusResponse);
    rpc MergeMR(MergeMRRequest) returns (MergeMRResponse);
    rpc GitCommit(GitCommitRequest) returns (GitCommitResponse);
    rpc GitPush(GitPushRequest) returns (GitPushResponse);   
    rpc GetProjectBranches(GetProjectBranchesRequest) returns (GetProjectBranchesResponse);    // 获取项目的分支
    rpc CloneRepository(CloneRepositoryRequest) returns (CloneRepositoryResponse);      // 克隆项目到仓库 
    rpc GetSubgroupPaths(GetSubgroupPathsRequest) returns (GetSubgroupPathsResponse);   //  拿到固定的组 比如muc-team下面的子组
    rpc CreateRepository(CreateRepositoryRequest)  returns (CreateRepositoryResponse);   // 创建仓库
    rpc CreateBranch(CreateBranchRequest) returns (CreateBranchResponse);
    rpc GetProjectTags(GetProjectTagsRequest) returns (GetProjectTagsResponse);
    rpc CreateWebhook(CreateWebhookRequest) returns (CreateWebhookResponse);
}

message CreateBranchRequest {         // 200创建成功 409是已经存在该分支
    string project_path = 1;        // 项目路径（必填）如："mcu-team/my-project"
    string branch_name = 2;         // 新分支名称（必填）
    string ref = 3;                 // 基于哪个分支/commit创建（非必填）
}


message CreateBranchResponse {
    bool success = 1;               // 是否成功
    string message = 2;             // 响应消息
    int32 code = 3;                 // 状态码
    string branch_name = 4;         // 创建的分支名称
    string project_path = 5;        // 项目路径
}


message CreateRepositoryRequest {    //返回值 200是创建成功409是已经存在这个仓库 
    string full_project_name = 1;  //仓库的名字     
    string  group_path = 2;
    optional string description = 3;
}

message CreateRepositoryResponse {
    int32 project_id = 1;       //项目的ID
    string project_path = 2;    // Git项目的Path 
    string project_url = 3;     // 项目URL
    string default_branch = 4;  //默认分支
    string message = 5;        // 
    int32 code = 6;
}

message GetSubgroupPathsRequest {
    string group_path = 1;  // 项目组name "mcu-team"
    string full_project_name = 2;  //项目的完整路径

}

message GetSubgroupPathsResponse {
    string parent_group_path = 1;      // 父组路径
    string parent_group_name = 2;      // 父组名称
    repeated string subgroup_paths = 3; // 所有子组路径数组
    int32 total_count = 4;             // 子组总数
    string message = 5;
    int32 code = 6;
}

service ProjectScannerService {
    rpc ScanProject (ProjectScanRequest) returns (ProjectScanResponse);
    rpc AnalyzeCode  (CodeAnalyzeRequest) returns (CodeAnalyzeResponse);
}
 
message QueryMRStatusRequest {    
  string project_path = 1;
  int32 mr_iid = 2;
}

message QueryMRStatusResponse {
  int32 mr_iid = 1;
  string merge_status = 2;
  string state = 3;
  string message = 4;
  int32 code = 5;
}

message MergeMRRequest {  
  string project_path = 1;
  int32 mr_iid = 2;
}

message MergeMRResponse {
  int32 mr_iid = 1;
  string state = 2;
  string message = 3;
  int32 code = 4;
  string commit_id = 5; 
}

message GitCommitRequest {
  string project_path = 1;
  string branch = 2;  // 传空 
  string commit_message = 3; 
  repeated string file_paths = 4;  //传空
}

message GitCommitResponse {
  bool success = 1;
  string message = 2;
  string commit_id = 3;
  int32 code = 4;
}

message GitPushRequest {
  string project_path = 1;
  string branch = 2;  // 要推送的分支，如果为空则使用当前分支
}

message GitPushResponse {
  bool success = 1;
  string message = 2;
  string commit_id = 3;
  int32 code = 4;
}

message CreateOrGetMergeRequestRequest{
        string engineering_path = 1;   //项目的名称
        string source_branch = 2;      //资源的分支
        string target_branch = 3;
} 

message CreateOrGetMergeRequestResponse {  
        string message = 1;
        int32 mr_iid = 2 ;
        string project_path = 3;  
        string merge_status = 4;  
        int32 grpc_status_code = 5;
        string  mr_url = 6;
}


message Empty {}

message CodeAnalyzeRequest {
    string project_name = 1;
}

message ParameterInfo {
    string io_type = 1;       // 输入/输出
    string name = 2;          // 参数名称
    string type = 3;          // 参数类型
    string description = 4;   // 描述
}

message FunctionInfo {
    int32 id = 1;
    string function_name = 2;       // 函数名
    string description = 3;         // 功能描述
    string constraints = 4;         // 限制条件
    string example_usage = 5;       // 调用示例
    string validation_criteria = 6; // 验证准则
    repeated ParameterInfo parameters = 7;    // 参数列表
    repeated ParameterInfo return_values = 8; // 返回值列表
    repeated ParameterInfo global_vars = 9;   // 全局变量列表
    repeated ParameterInfo static_vars = 10;  // 静态变量列表
}

message CodeAnalysisResult { 
    string file_name =  1;
    string file_path = 2;
    string headr_file = 3;
    repeated FunctionInfo  functions = 4;
}

message CodeAnalyzeResponse {
    string message = 1;
    bool success = 2;
    repeated CodeAnalysisResult analysis_results = 3;
}

message ProjectScanRequest {
    string project_name = 1;
}

message ProjectScanResponse {
    string message = 1;
    bool success = 2;
    int32 code = 3;
    int32 file_count = 4;
}

message Project {
    string project_name = 1;
}
message ProjectSyncRequest{
    string message = 1;
    bool success = 2;
}

message ProjectInfo { 
    int32   project_id = 1;
    string  project_name = 2;
    string  project_url = 3;
    string  project_description = 4;
    string  project_last_activity = 5;
}

message ListAllProjectsResponse { 
    string message = 1;
    repeated ProjectInfo projects = 2;
    bool success = 3;
}

message GetProjectBranchesRequest {
    string project_name = 1;
}

message BranchInfo {
    string name = 1;
    string commit_id = 2;
    bool protected = 3;
}

message GetProjectBranchesResponse {
    bool success = 1;
    string message = 2;
    repeated string branches = 3;  // 返回分支名称字符串数组
}

message CloneRepositoryRequest {
    string repo_url = 1;
    string project_path = 2;
    string branch = 3;  // 要克隆的分支，为空则使用默认分支;
    string tag = 4;    // 要克隆的标签  如果设置tag 则tag的优先级最高
}

message CloneRepositoryResponse {
    bool success = 1;
    string message = 2;
    string local_path = 3;
}

message LogDirResponse {
    repeated string dates = 1;   //repeated 是列表
}

message LogDirRequest {
    repeated string dates = 1;
}

message OperationStatus {
    bool success = 1;               //是否成功
    int32 deleted_count = 2;         // 删除的目录数量
    repeated string deleted_dirs = 3; // 已删除的目录
    repeated string errors = 4;       // 错误信息
}

// 获取项目标签请求
message GetProjectTagsRequest {
    string project_path = 1;  // 项目路径，如 "mcu-team/my-project"
    int32 limit = 2;          // 限制返回的标签数量，0表示不限制
    bool sort = 3;            // 是否按创建时间排序，true为降序（最新的在前）
}

// 标签信息
message TagInfo {
    string name = 1;           // 标签名称
    string message = 2;        // 标签消息
    string commit_id = 3;      // 关联的提交ID
    string commit_message = 4; // 提交消息
    string created_at = 5;     // 创建时间
    string creator_name = 6;   // 创建者名称
    bool protected = 7;        // 是否受保护
}

// 获取项目标签响应
message GetProjectTagsResponse {
    bool success = 1;          // 是否成功
    string message = 2;        // 响应消息
    int32 code = 3;            // 状态码
    repeated TagInfo tags = 4; // 标签列表
    int32 total_count = 5;     // 标签总数
}

// 创建Webhook请求
message CreateWebhookRequest {
    string git_url = 1;        // Git仓库URL，如 "git@10.1.1.99:mcu-team/my-project.git"
    string branch = 2;         // 分支名称，如 "master"
}

// 创建Webhook响应
message CreateWebhookResponse {
    bool success = 1;          // 是否成功
    string message = 2;        // 响应消息
    int32 code = 3;            // 状态码
    int32 webhook_id = 4;      // Webhook ID
    string webhook_url = 5;    // Webhook URL
}
