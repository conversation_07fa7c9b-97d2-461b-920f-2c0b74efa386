--功能表2
CREATE TABLE IF NOT EXISTS public.functions2(
    id serial primary key, --id
    "name" varchar(255), --功能名称
    "number" varchar(255),  --功能编号
    "level" int default 1, --级别
    parent_id int, --父id
    "desc" varchar(255),  --功能描述
    deprecated bool default false ,
    create_time timestamp, --创建时间
    update_time timestamp, --更新时间
    unique(parent_id, number)
);
ALTER TABLE public.functions2 OWNER TO atpms;