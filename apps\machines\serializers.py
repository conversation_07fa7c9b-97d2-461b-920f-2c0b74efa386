from rest_framework import serializers

from .models import MachineReservationModel


class DeviceTypeListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class DeviceTypeSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255)
    number = serializers.Char<PERSON>ield(max_length=255)
    desc = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)


class DeviceTypeUpdateSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)
    number = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class DeviceListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    d_number_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class DeviceSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    type = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    model = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    maintainer = serializers.CharField(max_length=255)
    status = serializers.IntegerField(default=0)


class DeviceUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    type = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    model = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    maintainer = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    status = serializers.IntegerField(default=0, required=False, allow_null=True)


class FunctionListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class FunctionSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class FunctionUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class MachineListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class MachineDetailByMNumberSerializer(serializers.Serializer):
    number = serializers.CharField()


class MachineSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    type = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    position = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    maintainer = serializers.CharField(max_length=255)
    status = serializers.IntegerField(default=0)
    is_share = serializers.BooleanField(default=False)

    devices = serializers.ListSerializer(child=serializers.IntegerField(), required=False,
                                         allow_empty=True, allow_null=True)
    functions = serializers.ListSerializer(child=serializers.IntegerField(), required=False,
                                           allow_empty=True, allow_null=True)


class MachineUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    type = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    m_number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    position = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    maintainer = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    status = serializers.IntegerField(required=False, allow_null=True)
    is_share = serializers.BooleanField(required=False, allow_null=True)

    devices = serializers.ListField(child=serializers.IntegerField(), required=False,
                                    allow_empty=True, allow_null=True)
    functions = serializers.ListField(child=serializers.IntegerField(), required=False,
                                      allow_empty=True, allow_null=True)


class MachineReservationListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    machine_name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    machine_id = serializers.IntegerField(required=False)
    start_time = serializers.DateTimeField(required=False)
    end_time = serializers.DateTimeField(required=False)
    status_list = serializers.ListField(child=serializers.IntegerField(), required=False)
    machine_name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    username_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    project_number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class MachineReservationSerializer(serializers.Serializer):
    machine_id = serializers.IntegerField()
    start_time = serializers.DateTimeField()
    end_time = serializers.DateTimeField()
    project = serializers.CharField(max_length=255)
    content = serializers.CharField(max_length=255)
    comment = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    is_share = serializers.BooleanField(default=False)
    t_mode = serializers.CharField(max_length=255)
    executive_personnel = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)

    def validate(self, data):
        if data["start_time"] >= data["end_time"]:
            raise serializers.ValidationError("结束时间必需在开始时间之后。")
        r = MachineReservationModel().exists(
            machine_id=data["machine_id"], start_time=data["start_time"], end_time=data["end_time"], status_list=[1, 3]
        )
        if r:
            raise serializers.ValidationError("机台预约时间段已有预约，时间有冲突。")
        return data


class MachineReservationCheckinSerializer(serializers.Serializer):
    id = serializers.IntegerField()


class MachineReservationUpdateSerializer(serializers.Serializer):
    start_time = serializers.DateTimeField(required=False, allow_null=True)
    end_time = serializers.DateTimeField(required=False, allow_null=True)
    project = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    content = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    comment = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)

    # is_share = serializers.BooleanField(required=False)

    def validate(self, data):
        if data.get("start_time") and data.get("end_time"):
            if data["start_time"] >= data["end_time"]:
                raise serializers.ValidationError("结束时间必需在开始时间之后。")
        c1 = data.get("start_time") is None or data.get("start_time") == ""
        c2 = data.get("end_time") is None or data.get("end_time") == ""
        if (c1 and (not c2)) or ((not c1) and c2):
            raise serializers.ValidationError("start_time end_time需同时为空或同时有值。")
        return data


class MachineUsageRecordListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    machine_name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class MachineReservationTimelineSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    machine_name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    machine_name_number_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    machine_name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    machine_number_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    start_time = serializers.DateTimeField(required=False, allow_null=True)
    end_time = serializers.DateTimeField(required=False, allow_null=True)