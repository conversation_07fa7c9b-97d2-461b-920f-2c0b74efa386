from pandas import DataFrame

from test_plans_v2.models import TestPlanModel
from utils.sql_helper import sql_fetchall_dict


class TestReportModel:
    def __init__(self):
        pass

    def list(self, **kwargs):
        content = TestPlanModel().list(**kwargs, status="COMPLETED")
        return content

    def retrieve(self, pk):
        plan = TestPlanModel().retrieve(pk)

        if plan:
            sql = """
                select code, max(name) as name, count(*)
                from public.process_monitor
                where test_plan_id = %(test_plan_id)s
                group by code
            """

            r = sql_fetchall_dict(sql, {"test_plan_id": plan.get("id")})

            plan["process_monitor_exp"] = r

        return plan


def plan_stats(plan):
    if plan:
        plan["stats"] = {}

        data = []
        for i in plan.get("test_cases"):
            data.append({
                "module": i.get("module"),
                "test_case_id": i.get("id"),
                "result": i.get("result", {}).get("result_two"),
            })

        tc_df = DataFrame(data)

        tc_df['is_exec'] = tc_df['result'].apply(lambda x: 1 if x is not None else 0)
        tc_df['is_pass'] = tc_df['result'].apply(lambda x: 1 if x == 1 else 0)

        m_df = tc_df.groupby(["module"]).agg(
            **{
                "total": ("test_case_id", "count"),
                "exec": ("is_exec", "sum"),
                "pass": ("is_pass", "sum")
            }
        )

        m_df.loc['合计'] = m_df.sum()

        m_df["non_exec"] = m_df["total"] - m_df["exec"]
        m_df["ng"] = m_df["total"] - m_df["pass"]
        m_df["pass_rate"] = m_df["pass"] / m_df["total"]
        m_df["pass_rate"].fillna(0, inplace=True)
        m_df["result"] = m_df["total"] == m_df["pass"]

        plan["stats"]["result"] = (m_df.loc["合计", "total"] == m_df.loc["合计", "pass"])

        m_df.reset_index(inplace=True)

        plan["stats"]["m_stats"] = m_df.to_dict(orient='records')

    return plan
