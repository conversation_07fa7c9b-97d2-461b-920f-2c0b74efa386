
import logging
import traceback
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db import transaction
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.cache import cache
from django.core.serializers import serialize
import json
import os
import shutil
import time
from pathlib import Path

logger = logging.getLogger("code_management")
from apps.code_management.grpc.host_machine_grpc import host_machine_client
from apps.code_management.grpc.git_grpc import git_client
from apps.code_management.models.project_models import ProjectInfoSerializer, CodeProjectInfo, CodeBranchInfo, BranchInfoSerializer
from apps.code_management.models.config_models import NodeTree, NodeTreeSerializer
from apps.code_management.models.code_models import CodePath
from users.models import UserFSInfo
from utils.fs_service import FSService
from utils.fs_app import fs_app


local_path = "/home/<USER>/hwcp"
config_path = "/home/<USER>/hwcp/gRPC_Server/HwcpTest"
config_url = "http://*********/python-team/HwcpTest.git"
config_branch = "main"
sdk_git_path = "http://*********/"







class CodeFileView(APIView):

    def select_data(self, workspace_path, node_level, node_name):
        """
            从CodeBranchInfo查询芯片，再从CodePath查询对应芯片、节点层级的文件路径信息
            :param workspace_path: 工作空间路径（关联CodeBranchInfo的branch_space）
            :param node_level: 节点层级（如1.1、2.1）
            :param node_name: 节点名称（对应CodePath的var_name）
            :return: CodePath查询集 或 空列表
        """
        try:
            # 1. 查询当前工作空间下的所有芯片（去重，避免重复数据）
            chip_queryset = CodeBranchInfo.objects.filter(
                branch_space=workspace_path
            ).values_list('chip', flat=True).distinct()  # values_list获取单个字段，distinct去重
            chip_list = list(chip_queryset)
            logger.info(f"工作空间[{workspace_path}]下查询到的芯片列表: {chip_list}")

            if not chip_list:
                logger.warning(f"工作空间[{workspace_path}]未查询到关联的芯片数据")
                return []

            # 2. 查询这些芯片下，对应节点层级、节点名称的文件路径信息
            # 注意：原代码字段名错误（vat_name → var_name），已修正
            code_path_queryset = CodePath.objects.using("hwcp").filter(
                chip__in=chip_list,  # 匹配所有查询到的芯片
                node_level=node_level,
                var_name=node_name  # 修正原代码的vat_name拼写错误
            ).values('chip', 'full_path', 'file_name')  # 只获取需要的字段，减少数据传输

            logger.info(f"查询到的CodePath数据: {list(code_path_queryset)}")
            return list(code_path_queryset)  # 转换为列表，便于后续处理

        except CodeBranchInfo.DoesNotExist:
            logger.warning(f"工作空间[{workspace_path}]未找到对应的分支信息（CodeBranchInfo.DoesNotExist）")
            return []
        except Exception as e:
            logger.error(
                f"查询芯片/文件路径失败：workspace_path={workspace_path}, node_level={node_level}, node_name={node_name}, 错误信息: {str(e)}",
                exc_info=True  # 打印详细堆栈信息，便于调试
            )
            return []




    def read_content(self, base_path, file_name, workspace_path, node_level, node_name):
        """
            拼接完整文件路径并读取文件内容（支持动态拼接项目根路径）
            :param base_path: CodePath中的full_path（相对路径，如Code//MidSdk/Display/Brightness）
            :param file_name: CodePath中的file_name（文件名，如Config_Brightness.h）
            :param workspace_path: 工作空间路径（项目根路径，用于拼接完整路径）
            :return: 包含文件内容、路径、名称的字典 或 空字典
        """
        try:
            # 1. 拼接完整文件路径：工作空间路径 + CodePath的full_path + 文件名
            # 处理路径分隔符（兼容Windows\和Linux/）
            relative_path = base_path.replace('//', os.sep)  # 将//替换为系统默认分隔符
            # 本地测试
            relative_path = base_path
            # workspace_path = r"D:\note\代码\2025-07-14\sdk2.0\hiwaysdk_2.0\Application"

            full_file_path = os.path.join(workspace_path, relative_path, file_name)
            logger.info("full_file_path:%s", full_file_path)
            # 2. 验证文件是否存在
            if not os.path.exists(full_file_path):
                logger.info(f"文件不存在：{full_file_path}")
                return {}

            # 3. 读取文件内容（支持utf-8，兼容中文）
            with open(full_file_path, "r", encoding="utf-8") as f:
                content = f.read()
            # 拼接路径
            file_path = os.path.join(".", relative_path)
            # # 本地测试
            # file_path = "." + "/" + relative_path
            # 4. 组织返回数据
            return {
                "content": content,
                "file_path": file_path,  # 完整文件路径
                "file_name": file_name,
                "chip": None  # 预留芯片字段，后续赋值
            }
        except UnicodeDecodeError:
            logger.error(f"文件编码错误（非utf-8）：{full_file_path}，请检查文件编码格式")
            return {}
        except PermissionError:
            logger.error(f"无权限读取文件：{full_file_path}")
            return {}
        except Exception as e:
            logger.error(f"读取文件失败：{full_file_path}，错误信息: {str(e)}", exc_info=True)
            return {}



    def post(self, request):
        """
        支持的模块：Brightness、Memory、Temperature、System、Touch、Power（按之前筛选的数据）
        """
        try:
            # 1. 解析请求参数
            params = request.data.get('params', {})
            logger.info("receive info: %s", params)
            workspace_path = params.get('workspace_path')
            node_level = params.get("node_level")
            node_name = params.get("nodeName")

           # 校验
            missing_params = []
            if not workspace_path:
                missing_params.append("workspace_path")
            if not node_level:
                missing_params.append("node_level")
            if not node_name:
                missing_params.append("nodeName")
            if missing_params:
                logger.warning(f"缺少必传参数：{missing_params}")
                return Response({"status": 0, "message": f"缺少必传参数：{','.join(missing_params)}"})

            logger.info(f"接收请求参数：workspace_path={workspace_path}, node_level={node_level}, node_name={node_name}")

            # 2. 查询芯片和文件路径信息
            code_path_data = self.select_data(workspace_path, node_level, node_name)
            if not code_path_data:
                return Response({"status": 0, "message": "未查询到对应芯片或文件路径信息"})

            # 3. 读取每个文件的内容
            file_list = []
            for item in code_path_data:
                chip = item.get('chip')
                full_path = item.get('full_path')
                file_name = item.get('file_name')

                # 跳过空文件名（如部分模块无文件的情况，按数据定义处理）
                if not file_name or file_name.strip() == "":
                    logger.info(f"芯片[{chip}]的节点[{node_name}]无关联文件名，跳过读取")
                    continue

                # 读取文件内容
                file_info = self.read_content(full_path, file_name, workspace_path, node_level, node_name)
                if file_info:
                    file_info["chip"] = chip  # 补充芯片信息，便于前端区分
                    file_list.append(file_info)
            logger.info("读取到的files: %s", file_list)

            # 4. 返回
            if not file_list:
                return Response({"status": 0, "message": "未读取到有效文件内容（文件不存在或无权限）"})

            return Response({
                "status": 1,
                "message": "获取文件成功",
                "files": file_list,  # 动态返回所有查询到的文件（支持多芯片、多文件）
                "chip_count": len(set(item["chip"] for item in file_list))  # 补充芯片数量，便于前端展示
            })

        except Exception as e:
            logger.error(f"接口异常：{str(e)}", exc_info=True)
            return Response({"status": 0, "message": f"服务器内部错误：{str(e)}"})