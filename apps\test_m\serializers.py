from rest_framework import serializers


class TestCaseListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    project_number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    submitter = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TestCaseSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255)
    tc_file = serializers.FileField()
    version = serializers.IntegerField()


class TestCaseFileDownLoadSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)


class TestCaseTypeListSerializer(serializers.Serializer):
    page = serializers.Integer<PERSON>ield(default=1)
    pagesize = serializers.Integer<PERSON>ield(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TestCaseTypeSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TestCaseTypeUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TestCaseStatusListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TestCaseStatusSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class TestCaseStatusUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
