from rest_framework import serializers


class ProductTypeListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    number_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class ProductTypeSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    number = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class ProductTypeUpdateSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class ProductTypeByMNumberSerializer(serializers.Serializer):
    number = serializers.Char<PERSON>ield()