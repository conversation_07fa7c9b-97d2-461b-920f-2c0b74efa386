import datetime
import json

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone
from django.db import transaction, models


class TestProduct(models.Model):
    name = models.Char<PERSON>ield(max_length=255)
    type = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    m_number = models.CharField(max_length=255, blank=True, null=True)
    hardware_versions = models.TextField()
    software_versions = models.TextField()
    project_name = models.CharField()
    project_number = models.CharField()
    project_id = models.IntegerField()
    creator_name = models.Char<PERSON>ield(max_length=255)
    creator_email = models.EmailField(max_length=255)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'test_products'
        managed = False
        app_label = 'test_products'

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type,
            "m_number": self.m_number,
            "hardware_versions": json.loads(self.hardware_versions) if self.hardware_versions else [],
            "software_versions": json.loads(self.software_versions) if self.software_versions else [],
            "project_name": self.project_name,
            "project_number": self.project_number,
            "project_id": self.project_id,
            "creator_name": self.creator_name,
            "creator_email": self.creator_email,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S"),
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S"),
        }


class TestProductComponent(models.Model):
    parent_id = models.IntegerField()
    child_id = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'test_product_components'
        managed = False
        app_label = 'test_products'


class TestProductModel:
    def __init__(self):
        self.table_name = "public.test_products"

    def create(self, user, **kwargs):
        with transaction.atomic():
            tp = TestProduct.objects.create(
                name=kwargs.get("name"),
                type=kwargs.get("type"),
                m_number=kwargs.get("m_number"),
                project_name=kwargs.get("project_name"),
                project_number=kwargs.get("project_number"),
                project_id=kwargs.get("project_id"),
                creator_name=user.username,
                creator_email=user.email,
                hardware_versions=kwargs.get("hardware_versions", []),
                software_versions=kwargs.get("software_versions", []),
            )
            id_ = tp.id

            components = kwargs.get("components", [])
            for component_id in components:
                TestProductComponent.objects.create(
                    parent_id=id_,
                    child_id=component_id,
                )

            return id_

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        query = TestProduct.objects.all()

        name = kwargs.get("name")
        if name:
            query = query.filter(name__icontains=name)
        m_number = kwargs.get("m_number")
        if m_number:
            query = query.filter(m_number__icontains=m_number)
        project_number = kwargs.get("project_number")
        if project_number:
            query = query.filter(project_number=project_number)
        type_ = kwargs.get("type")
        if type_:
            query = query.filter(type=type_)

        count = query.count()

        query = query.order_by("-update_time")
        query = query[(page - 1) * pagesize: page * pagesize]

        results = query.values(
            "id", "name", "type", "m_number",
            "project_name", "project_number", "project_id", "creator_name", "creator_email",
            "hardware_versions", "software_versions",
            "create_time", "update_time"
        )

        for i in results:
            i["hardware_versions"] = json.loads(i["hardware_versions"]) if i["hardware_versions"] else []
            i["software_versions"] = json.loads(i["software_versions"]) if i["software_versions"] else []
            i["create_time"] = i["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            i["update_time"] = i["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        content = {
            "count": count,
            "results": results,
        }

        return content

    def get_descendants(self, parent):
        if parent["depth"] > 10:
            raise Exception("产品组件层级过深，可能存在循环引用。")

        parent["components"] = TestProductComponent.objects.filter(
            parent_id=parent["id"]).values_list('child_id', flat=True)
        if not parent["components"]:
            return
        else:
            parent["components"] = [TestProduct.objects.get(pk=i).to_dict() for i in parent["components"]]
            for i in parent["components"]:
                i["depth"] = parent["depth"] + 1
                self.get_descendants(i)

    def retrieve(self, pk):
        tp = TestProduct.objects.filter(id=pk).first()
        if not tp:
            return None
        tp = tp.to_dict()
        tp["depth"] = 1

        self.get_descendants(tp)

        return tp

    def delete(self, pk):
        TestProduct.objects.get(id=pk).delete()

    def update(self, pk, **kwargs):
        with transaction.atomic():
            tp = TestProduct.objects.get(pk=pk)
            tp.name = kwargs.get("name")
            tp.type = kwargs.get("type")
            tp.m_number = kwargs.get("m_number")
            tp.hardware_versions = kwargs.get("hardware_versions")
            tp.software_versions = kwargs.get("software_versions")
            tp.save()

            components = TestProductComponent.objects.filter(
                parent_id=pk).values_list('child_id', flat=True)

            if kwargs.get("components") != components:
                TestProductComponent.objects.filter(parent_id=pk).delete()
                for component_id in kwargs.get("components", []):
                    TestProductComponent.objects.create(
                        parent_id=pk,
                        child_id=component_id,
                    )
