from rest_framework import serializers

from .models import NIOTestCaseModel, VoyahTestCaseModel


class NIOTestCaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = NIOTestCaseModel
        fields = '__all__'


class VoyahTestCaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = VoyahTestCaseModel
        fields = '__all__'


class NIOTestCaseListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    id_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class VoyahTestCaseListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.Char<PERSON><PERSON>(max_length=255, required=False, allow_null=True, allow_blank=True)
    id_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
