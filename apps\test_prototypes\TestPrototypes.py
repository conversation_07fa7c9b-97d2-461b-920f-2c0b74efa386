# -*-coding:utf-8 -*-
"""
# Author     : jid
# Time       : 2024/11/18 17:56
# Description: 产品类型库表
"""
from django.db import models
from django.db.models import Char<PERSON>ield, DateTimeField, IntegerField, TextField


class TestPrototypes(models.Model):
    TYPE_CHOICE = (
        (0, u"总成"),
        (1, u"VDS"),
        (2, u"主机"),
        (3, u"PCBA"),
        (4, u"屏模组")
    )

    STATUS_CHOICE = (
        (0, u"正常"),
        (1, u"故障"),
        (2, u"拆解"),
        (3, u"报废")
    )

    STAGES_CHOICE = (
        (0, u"DV"),
        (1, u"PV")
    )

    name = CharField(max_length=255, verbose_name=u"样件名称")
    type = IntegerField(default=0, choices=TYPE_CHOICE, verbose_name=u'样件类型')
    status = IntegerField(default=0, choices=STATUS_CHOICE, verbose_name=u'样件状态')
    number = CharField(max_length=255, verbose_name=u'样件编号')
    project = CharField(max_length=255, verbose_name=u'关联项目')
    project_name = CharField(max_length=255, verbose_name=u'关联项目')
    user_name = CharField(max_length=255, verbose_name=u'用户名')
    user_email = CharField(max_length=255, verbose_name=u'用户邮箱')
    desc = TextField(max_length=255, verbose_name=u'描述')
    create_time = DateTimeField(auto_now_add=True, verbose_name=u'创建时间')
    update_time = DateTimeField(auto_now=True, verbose_name=u'更新时间')
    prototype_stage = IntegerField(default=0, choices=STAGES_CHOICE, verbose_name=u'样件阶段')

    class Meta:
        app_label = 'test_prototypes'
        db_table = 'test_prototypes'
        managed = False

    def to_dict(self):
        return {
            "name": self.name,
            "type": self.type,
            "status": self.status,
            "number": self.number,
            "project": self.project,
            "project_name": self.project_name,
            "user_name": self.user_name,
            "user_email": self.user_email,
            "desc": self.desc,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S"),
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S"),
            "prototype_stage": self.prototype_stage
        }
