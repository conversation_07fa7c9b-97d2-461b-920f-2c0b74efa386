import json
import traceback
import logging

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from .serializers import (
    TestPlanSerializer, TestPlanListSerializer, TestPlanUpdateSerializer, TestPlanReviewRSerializer,
    TestPlanUpdateStatusSerializer, TestCaseExecSerializer, TestCaseResultSyncSerializer, TestPlanTestCasesSerializer,
    TestPlanTestCasesDeleteSerializer, TestPlanTestCasesAddSerializer, TestPlanTestCasesUpdateSerializer,
    TestPlanCopySerializer, RelatedVersionsPushSerializer,
)
from .models import TestPlanModel, TestCaseResultModel, AtFile
from product_versions.models import ProductVersionModel
from users.models import UserFSInfo
from utils.fs_service import FSService
from utils.fs_app import fs_app

logger = logging.getLogger("machine")

fs_service = FSService()


class TestPlansView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = TestPlanListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestPlanModel()

            data = {
                **serializer.validated_data
            }
            if data.get("action_type") == "MY_CREATED":
                data["creator_email"] = request.user.email
                data["action_type"] = None

            content = model.list(**data)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = TestPlanSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user

            project_number = serializer.validated_data.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            m_version = serializer.validated_data.get("m_version")
            pv = ProductVersionModel().retrieve_by_version_name(version_name=m_version)
            if not pv:
                return Response({"err_code": 1, "msg": "对应产品版本未提测！"}, status.HTTP_400_BAD_REQUEST)

            model = TestPlanModel()
            tp_id = model.create(
                **serializer.validated_data,
                creator_email=user.email, creator_name=user.username, creator_employee_number=user.employee_number
            )

            plan_type = serializer.validated_data.get("plan_type")
            project_name = serializer.validated_data.get("project_name")
            project_number = serializer.validated_data.get("project_number")
            plan_name = serializer.validated_data.get("name")
            if plan_type == 1:
                fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
                f, data = fs_service.get_user_info(token=fs_info.access_token, open_id=user.open_id)
                if f:
                    departs = data.get("data").get("departmentList", [])
                    dp = departs[0] if departs else None
                    if dp:
                        ld = dp.get("leaderId")
                        if ld:
                            fs_app.send_test_plan_create_msg(
                                ld,
                                project=f"{project_name}({project_number})",
                                plan_type="内部验证",
                                plan_name=plan_name,
                                creator=user.username,
                                url_=f"http://www.hwauto.com.cn:59999/test_plans_v2/list?id={tp_id}",
                            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = TestPlanModel()
            result = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk=None):
        try:
            serializer = TestPlanUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            model = TestPlanModel()

            r = model.retrieve(pk=pk)

            if not r:
                return Response({"err_code": 1, "msg": "对应id测试用例不存在！"}, status.HTTP_400_BAD_REQUEST)

            # if r.get("creator_employee_number") != user.employee_number:
            #     return Response({"err_code": 1, "msg": "创建人不符合！"}, status.HTTP_400_BAD_REQUEST)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            data = {}
            if r.get("status") in ["DEBUGGING"]:
                data = serializer.validated_data
            elif r.get("status") in ["REVIEWING", "APPROVED", "RUNNING"]:
                data = {
                    "abnormal_stop": serializer.validated_data.get("abnormal_stop"),
                    "test_case_extra_args": serializer.validated_data.get("test_case_extra_args"),
                }
            elif r.get("status") in ["REVIEWING", "APPROVED", "RUNNING"]:
                data = {
                    "finish_notice": serializer.validated_data.get("finish_notice"),
                    "test_case_extra_args": serializer.validated_data.get("test_case_extra_args"),
                }
            elif r.get("status") in ["REJECTED"]:
                data = {
                    **serializer.validated_data,
                    "status": "DEBUGGING",
                }
            elif r.get("status") in ["COMPLETED"]:
                return Response({"err_code": 1, "msg": "测试计划已结束，不可再修改！"}, status.HTTP_400_BAD_REQUEST)

            model.update(pk=pk, **data)

            return Response({"err_code": 0, "data": {"id": pk}, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:

            user = request.user
            model = TestPlanModel()

            r = model.retrieve(pk=pk)

            # if r.get("creator_employee_number") != user.employee_number:
            #     return Response({"err_code": 1, "msg": "创建人不符合！"}, status.HTTP_400_BAD_REQUEST)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            if r.get("status") == "REVIEWING":
                return Response({"err_code": 1, "msg": "评审中的计划不能删除"}, status.HTTP_400_BAD_REQUEST)

            if r.get("status") == "APPROVED":
                return Response({"err_code": 1, "msg": "评审通过的计划不能删除"}, status.HTTP_400_BAD_REQUEST)

            model.delete(pk=pk)
            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanDetailV2View(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            machine_number = request.query_params.get("machine_number")

            model = TestPlanModel()

            result = model.retrieve2(pk=pk, machine_number=machine_number)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanSyncView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            t_id = request.data.get("id")
            if t_id is None:
                return Response({"err_code": 1, "msg": "id不能为空！"}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            model = TestPlanModel()
            t = model.retrieve(pk=t_id)

            if not t:
                return Response({"err_code": 1, "msg": "id无效，对应计划不存在！"}, status.HTTP_400_BAD_REQUEST)

            if t.get("plan_type") != 0:
                return Response({"err_code": 1, "msg": "只有对外发布计划才能同步！"}, status.HTTP_400_BAD_REQUEST)

            project_number = t.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            f, data = fs_service.sync_test_plan(token, user.email, t)

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            model.update(pk=t_id, status="REVIEWING")

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanReviewRView(APIView):
    def post(self, request):
        try:
            logger.info("request.data %s", request.data)

            serializer = TestPlanReviewRSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            logger.info("serializer.validated_data %s", serializer.validated_data)

            model = TestPlanModel()

            project_number = serializer.validated_data.get("project_number")
            name = serializer.validated_data.get("name")
            status_ = serializer.validated_data.get("status")

            r = model.list(project_number=project_number, name=name, pagesize=1)
            if r.get("results"):
                tp = r.get("results")[0]
                tp_id = tp.get("id")

                if status_ == "ACCEPT":
                    model.update(pk=tp_id, status="APPROVED")
                elif status_ == "CANCEL" or status_ == "REJECT":
                    model.update(pk=tp_id, status="REJECTED")

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanUpdateStatusView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = TestPlanUpdateStatusSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestPlanModel()

            user = request.user

            id_ = serializer.validated_data.get("id")
            status_ = serializer.validated_data.get("status")

            r = model.retrieve(pk=id_)
            if not r:
                return Response({"err_code": 1, "msg": "对应id测试计划不存在"}, status.HTTP_400_BAD_REQUEST)

            result_two = 1
            old_status = r.get("status")
            if status_ == "APPROVED":
                if not (old_status == "DEBUGGING" and r.get("plan_type") != 0):
                    return Response({"err_code": 1, "msg": f"正式类型用例{old_status} => {status_}不允许"},
                                    status.HTTP_400_BAD_REQUEST)

                if user.email not in ["<EMAIL>", "<EMAIL>", "<EMAIL>",
                                      "<EMAIL>", "<EMAIL>"]:
                    return Response({"err_code": 1, "msg": "没有权限"}, status.HTTP_403_FORBIDDEN)

            else:

                project_number = r.get("project_number")
                f, data = fs_service.get_project_members(project_code=project_number)
                if not f:
                    logger.error(data)
                    return Response({"err_code": 3, "msg": data.get("message")},
                                    status.HTTP_500_INTERNAL_SERVER_ERROR)

                members = data.get("data")
                members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

                if user.email not in members:
                    return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

                if status_ == "RUNNING":
                    if old_status != "APPROVED":
                        return Response({"err_code": 1, "msg": f"{old_status} => {status_}不允许"},
                                        status.HTTP_400_BAD_REQUEST)
                elif status_ == "COMPLETED":
                    if old_status != "RUNNING":
                        return Response({"err_code": 1, "msg": f"{old_status} => {status_}不允许"},
                                        status.HTTP_400_BAD_REQUEST)

                    for i in r["test_cases"]:
                        r_tmp = i.get("result").get("result_two")

                        if r_tmp is None:
                            return Response(
                                {"err_code": 1, "msg": f"测试用例({i.get('name')}[{i.get('number')}])未执行"},
                                status.HTTP_400_BAD_REQUEST)

                        if not r_tmp:
                            result_two = 0

                else:
                    return Response({"err_code": 1, "msg": f"手动变更为{status_}不允许"}, status.HTTP_400_BAD_REQUEST)

            if status_ == "COMPLETED":
                model.update(pk=id_, status=status_, result_two=result_two)
            else:
                model.update(pk=id_, status=status_)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseExecView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            user = request.user

            serializer = TestCaseExecSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            tp = TestPlanModel().retrieve(pk=serializer.validated_data.get("test_plan_id"))
            if not tp:
                return Response({"err_code": 1, "msg": "测试计划不存在。"}, status.HTTP_400_BAD_REQUEST)

            # if tp.get("status") != "RUNNING":
            #     return Response({"err_code": 1, "msg": "测试计划未运行中，无法执行测试用例。"},
            #                     status.HTTP_400_BAD_REQUEST)

            project_number = tp.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            model = TestCaseResultModel()
            model.create(
                **serializer.validated_data,
                creator_email=user.email, creator_name=user.username
            )

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestCaseResultSyncView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = TestCaseResultSyncSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            t_id = serializer.validated_data.get("test_plan_id")

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            model = TestPlanModel()
            t = model.retrieve(pk=t_id)

            if not t:
                return Response({"err_code": 1, "msg": "id无效，对应计划不存在！"}, status.HTTP_400_BAD_REQUEST)

            if t.get("plan_type") != 0:
                return Response({"err_code": 1, "msg": "只有对外发布计划才能同步测试结果！"},
                                status.HTTP_400_BAD_REQUEST)

            project_number = t.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            f, data = fs_service.sync_test_plan_result(token, t)

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanTestCasesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            serializer = TestPlanTestCasesSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = TestPlanModel()
            result = model.get_test_plan_test_cases(pk=pk, **serializer.validated_data)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanTestCasesDeleteView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, pk=None):
        try:
            serializer = TestPlanTestCasesDeleteSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            test_case_ids = serializer.validated_data.get("test_case_ids")

            user = request.user
            model = TestPlanModel()

            r = model.retrieve(pk=pk)
            if not r:
                return Response({"err_code": 0}, status.HTTP_200_OK)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            if r.get("status") != "DEBUGGING":
                return Response({"err_code": 1, "msg": "非调试阶段，不能删除用例"}, status.HTTP_400_BAD_REQUEST)

            result = model.delete_test_plan_test_cases(pk=pk, test_case_ids=test_case_ids)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanTestCasesAddView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, pk=None):
        try:
            serializer = TestPlanTestCasesAddSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            test_case_ids = serializer.validated_data.get("test_case_ids")

            user = request.user
            model = TestPlanModel()

            r = model.retrieve(pk=pk)
            if not r:
                return Response({"err_code": 0}, status.HTTP_200_OK)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            if r.get("status") != "DEBUGGING":
                return Response({"err_code": 1, "msg": "非调试阶段，不能添加用例"}, status.HTTP_400_BAD_REQUEST)

            result = model.add_test_plan_test_cases(pk=pk, test_case_ids=test_case_ids)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanTestCasesUpdateView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, pk=None):
        try:
            serializer = TestPlanTestCasesUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            test_case_ids = serializer.validated_data.get("test_case_ids")

            user = request.user
            model = TestPlanModel()

            r = model.retrieve(pk=pk)
            if not r:
                return Response({"err_code": 0}, status.HTTP_200_OK)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            if r.get("status") != "DEBUGGING":
                return Response({"err_code": 1, "msg": "非调试阶段，不能更新用例"}, status.HTTP_400_BAD_REQUEST)

            result = model.update_test_plan_test_cases(pk=pk, test_case_ids=test_case_ids)

            return Response({"err_code": 0, "data": result}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestPlanCopyView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, pk=None):
        try:
            serializer = TestPlanCopySerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            model = TestPlanModel()

            r = model.retrieve4(pk=pk)
            if not r:
                return Response({"err_code": 0}, status.HTTP_200_OK)

            project_number = r.get("project_number")
            f, data = fs_service.get_project_members(project_code=project_number)
            if not f:
                logger.error(data)
                return Response({"err_code": 3, "msg": data.get("message")},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            members = data.get("data")
            members = [i.get("userInfo").get("username") for i in members if i.get("roleId") == "22"]

            if user.email not in members:
                return Response({"err_code": 1, "msg": "无权限。非项目测试工程师"}, status.HTTP_400_BAD_REQUEST)

            m_version = serializer.validated_data.get("m_version")
            pv = ProductVersionModel().retrieve_by_version_name(version_name=m_version)
            if not pv:
                return Response({"err_code": 1, "msg": "对应产品版本未提测！"}, status.HTTP_400_BAD_REQUEST)

            tp_id = model.copy(r, user, **serializer.validated_data)

            return Response({"err_code": 0, "data": {"id": tp_id}}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class AtFilesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            file_ids = request.query_params.get("file_ids")
            if not file_ids:
                return Response({"err_code": 1, "msg": "file_ids不能为空！"}, status.HTTP_400_BAD_REQUEST)
            file_ids = [int(i) for i in file_ids.split(",")]

            objs = AtFile.objects.filter(id__in=file_ids)

            results = []
            for obj in objs:
                results.append({
                    "id": obj.id,
                    "title": obj.title,
                    "size": obj.size,
                    "extension": obj.extension,
                })

            return Response({"err_code": 0, "data": results}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class RelatedVersionsPushView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = RelatedVersionsPushSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)
            token = user_fs_info.access_token

            tp_id = serializer.validated_data.get("test_plan_id")

            tp = TestPlanModel().retrieve(pk=tp_id)
            if not tp:
                return Response({"err_code": 1, "msg": "id无效，对应计划不存在！"}, status.HTTP_400_BAD_REQUEST)
            if tp.get("status") != "COMPLETED":
                return Response({"err_code": 1, "msg": "只有已完成的计划才能同步！"}, status.HTTP_400_BAD_REQUEST)
            if not tp.get("test_product"):
                return Response({"err_code": 1, "msg": "测试产品不能为空，不能同步！"}, status.HTTP_400_BAD_REQUEST)

            f, data = fs_service.push_related_versions(token, tp.get("project_number"), tp.get("id"),
                                                       tp.get("test_product"))

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
