from rest_framework import serializers


class CustomCMDListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    cmd_name_re = serializers.Char<PERSON>ield(max_length=255, required=False, allow_blank=True, allow_null=True)
    cmd_code_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class CustomCMDSerializer(serializers.Serializer):
    cmd_name = serializers.CharField(max_length=255)
    cmd_code = serializers.CharField(max_length=255)
    desc = serializers.Char<PERSON>ield(required=False, allow_null=True, allow_blank=True)
    params = serializers.JSO<PERSON>ield(required=False, allow_null=True)


class CustomCMDUpdateSerializer(serializers.Serializer):
    cmd_name = serializers.CharField(max_length=255)
    desc = serializers.Cha<PERSON><PERSON><PERSON>(required=False, allow_null=True, allow_blank=True)
    params = serializers.JSO<PERSON>ield(required=False, allow_null=True)
