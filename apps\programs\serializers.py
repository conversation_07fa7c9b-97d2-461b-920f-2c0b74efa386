from rest_framework import serializers


class ProgramListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    name_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    number_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    type = serializers.IntegerField(required=False)


class LastProgramSerializer(serializers.Serializer):
    program_number = serializers.Char<PERSON>ield(max_length=255)


class ProgramSerializer(serializers.Serializer):
    name = serializers.Char<PERSON>ield(max_length=255)
    number = serializers.Char<PERSON>ield(max_length=255)
    desc = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    type = serializers.IntegerField(default=0)
    icon = serializers.ImageField(required=False, allow_null=True)
    extra_args = serializers.J<PERSON><PERSON><PERSON>(required=False)


class ProgramUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    desc = serializers.Char<PERSON>ield(required=False, allow_blank=True, allow_null=True)
    type = serializers.IntegerField(default=0)
    icon = serializers.ImageField(required=False, allow_null=True)
    extra_args = serializers.JSONField(required=False)


class ProgramVersionListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    program_name_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    version_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class ProgramVersionSerializer(serializers.Serializer):
    program_id = serializers.IntegerField()
    version = serializers.CharField(max_length=255)
    path = serializers.URLField()
    linux_path = serializers.URLField(required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField()


class ProgramVersionUpdateSerializer(serializers.Serializer):
    program_id = serializers.IntegerField()
    version = serializers.CharField(max_length=255)
    path = serializers.URLField()
    linux_path = serializers.URLField(required=False, allow_null=True, allow_blank=True)
    desc = serializers.CharField()
