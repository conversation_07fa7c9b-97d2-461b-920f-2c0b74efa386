from rest_framework import serializers


class DiffToolUsageRecordListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    project_number = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    diff_package_name_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    diff_package_md5_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    diff_package_version_re = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class DiffToolUsageRecordSerializer(serializers.Serializer):
    project_name = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255)
    input_file_type = serializers.ChoiceField(choices=["bin", "hex", "zip"])
    old_package = serializers.FileField()
    old_package_version = serializers.CharField(max_length=255)
    new_package = serializers.FileField()
    new_package_version = serializers.CharField(max_length=255)
    diff_package_name = serializers.CharField(max_length=255)
    diff_package_version = serializers.CharField(max_length=255)


class DiffToolUsageRecordVerifySerializer(serializers.Serializer):
    id = serializers.IntegerField()
    diff_package_status = serializers.ChoiceField(choices=[1, 2])


class DiffPackageShareUrlSerializer(serializers.Serializer):
    diff_record_id = serializers.IntegerField()
    expiration_time = serializers.IntegerField(default=60 * 60 * 6)
