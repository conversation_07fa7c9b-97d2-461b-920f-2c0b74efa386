CREATE TABLE public.chip_color (
    id SERIAL PRIMARY KEY,                  -- 自增主键
    module VARCHAR(50) NOT NULL,            -- 模块
    color VARCHAR(10) NOT NULL               -- 引脚颜色（HEX 颜色码）
);


ALTER TABLE public.chip_color RENAME TO chip_module;

ALTER TABLE public.chip_module
ADD COLUMN func JSONB,       -- 存储字符串数组，如 ["SPI_CLK", "SPI_MISO"]
ADD COLUMN config JSONB;




ALTER TABLE public.chip_module
RENAME COLUMN alt_value TO alt_values;