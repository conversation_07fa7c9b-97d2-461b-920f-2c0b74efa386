import logging
import time
import random
import json
import requests
from django.utils import timezone
from decimal import Decimal
from datetime import datetime
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from .jenkins_service import JenkinsService
from .generatior import JenkinsfileGenerator
from .models import PipelineProject, PipelineBuild
from urllib.parse import urlparse
from .webhook_utils import software_hash
from utils.fs_app import FSApp
from .webhook_utils import generate_build_id
from .tasks import provision_jenkins_project,send_feishu_notification_task,send_feishu_notification_task_result,process_webhook_build_task

logger = logging.getLogger("auto_jenkins")

fs = FSApp()


GITLAB_URL = "http://*********"   
GITLAB_TOKEN = "**************************"

jenkins_user = "jid"
jenkins_token =  "110d936c0bf69044f678b609cec5c29c02"




def format_duration_mm_ss(duration):
    """
    duration: 可以是 Decimal/float/int/str(单位:秒）
    返回：'xx分xx秒'
    """
    try:
        if duration is None:
            return '0分0秒'
        total_seconds = int(float(duration))
        m, s = divmod(total_seconds, 60)
        return f"{m}分{s}秒"
    except Exception:
        return '0分0秒'
    
def fmt(dt):
    return dt.strftime('%Y-%m-%d %H:%M:%S') if dt else None


class PipelineProjectsView(APIView):
    """Pipeline项目管理"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取所有Pipeline项目"""
        project_id = request.query_params.get('editId')

        if project_id:           # 获取单个项目
            try:
                if project_id == "latest":
                   project = PipelineBuild.objects.order_by('-start_time').first().project
                else:
                    project = PipelineProject.objects.get(id=project_id)
                return Response({
                    "success": True,
                    "data": {
                        "id": project.id,
                        "project_name": project.project_name,
                        "git_url": project.git_url,
                        "engineering_group": project.engineering_group,
                        "engineering_path": project.engineering_path,
                        "agent": project.agent,
                        "created_at": project.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                        "updated_at": project.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                        "creator": project.creator,
                        "belong_project": project.belong_project,
                        "flow_config": project.flow_config,
                        "frontend_raw_config": project.frontend_raw_config,
                        "enable_webhook": project.enable_webhook,
                        "branch":project.branch,
                        "pipeline_config":project.pipeline_config
                    }
                })
            except PipelineProject.DoesNotExist:
                return Response({
                    "success": False,
                    "message": "项目不存在"
                }, status=404)
        project_name = request.query_params.get('project_name')
        if project_name:
            projects = PipelineProject.objects.filter(belong_project=project_name).order_by('-updated_at')
        else:
            projects = PipelineProject.objects.all().order_by('-updated_at')
        
        results = []
        for project in projects:
            config = project.frontend_raw_config
            nodes = config.get("nodes",[])
            for node in nodes:
                if node.get('type') =="project-init":
                    env = node.get("environment", {})
                    software_type = env.get("PROJECT_TYPE")
                    break   # 找到就可以退出循环
            results.append({
                "id": project.id,
                "project_name": project.project_name,
                "git_url": project.git_url,
                "branch": project.branch,
                "created_at": project.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                "updated_at": project.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                "creator": project.creator,
                "belong_project": project.belong_project,
                "flow_config": project.flow_config,
                "software_type": software_type
            })
        
        return Response({
            "success": True,
            "data": {
                "projects": results,
                "total_count": len(results)
            }
        })
    
    def post(self, request):
        """创建Pipeline项目"""
        data = request.data
        project_name = data.get('project_name')
        git_url = data.get('git_url')
        engineering_group = data.get('engineering_group')
        engineering_path = data.get('engineering_path')
        agent = data.get('agent', 'slave_win10')
        branch = data.get('branch', 'master')
        pipeline_config = data.get('pipeline_config')
        belong_project =   data.get('belong_project')
        flow_config = data.get('description')
        frontend_raw_config = data.get('frontend_config')
        enable_webhook = data.get('enable_webhook', False)
    
        
        # 验证必需参数
        if not all([project_name, git_url, branch, pipeline_config]):
            return Response({
                "success": False,
                "message": "缺少必需参数"
            }, status=status.HTTP_400_BAD_REQUEST)
        try:                 
            generator = JenkinsfileGenerator()               # 生成Jenkinsfile
            generation_result = generator.generate(pipeline_config)
            if not generation_result['success']:
                return Response({
                    "success": False,
                    "message": "生成Jenkinsfile失败",
                    "errors": generation_result['errors']
                }, status=status.HTTP_400_BAD_REQUEST)
            jenkinsfile_content = generation_result['jenkinsfile']
            project,created = PipelineProject.objects.update_or_create(
                project_name=project_name,
                defaults=  {
                    'git_url': git_url,
                    'engineering_group': engineering_group,
                    'engineering_path': engineering_path,
                    'agent': agent,
                    'pipeline_config': pipeline_config,
                    'jenkinsfile_content': jenkinsfile_content,
                    'jenkins_job_name': project_name,
                    'creator': request.user.username if request.user.is_authenticated else 'system',
                    'creator_email': request.user.email if request.user.is_authenticated else '<EMAIL>',
                    'belong_project': belong_project,
                    'flow_config': flow_config,
                    'frontend_raw_config': frontend_raw_config,
                    'enable_webhook': enable_webhook,
                    'branch':branch
                }
            )
            task = provision_jenkins_project.delay(
                project_name=project_name,
                jenkinsfile_content=jenkinsfile_content,
                git_url=data.get('git_url'),
                pipeline_config=pipeline_config,
                branch=branch,
                engineering_path=data.get('engineering_path'),
                engineering_group=data.get('engineering_group'),
                enable_webhook=data.get('enable_webhook', False)
            )
            logger.info(f"创建项目任务ID: {task.id},项目是 {project_name}")
            response_message ="项目创建请求已提交，正在后台处理..." if created else "项目更新请求已提交，正在后台处理..."
            return Response({
                "success": True,
                "message": response_message,
                "data": {
                    "id": project.id,
                    "project_name": project.project_name,
                    "jenkins_job_name": project_name,
                    "webhook_enabled": enable_webhook
                }
            })
        except Exception as e:
            logger.error(f"创建Pipeline项目失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"创建失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PipelineProjectDetailView(APIView):
    """Pipeline项目详情"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, project_name):
        """获取项目详情"""
        try:
            project = PipelineProject.objects.get(project_name=project_name)
            jenkins_service = JenkinsService()
            
            # 获取Jenkins状态
            jenkins_exists = jenkins_service.check_job_exists(project.jenkins_job_name)
            jenkins_info = None
            if jenkins_exists:
                jenkins_info = jenkins_service.get_job_info(project.jenkins_job_name)
            
            return Response({
                "success": True,
                "data": {
                    "id": project.id,
                    "project_name": project.project_name,
                    "git_url": project.git_url,
                    "engineering_group": project.engineering_group,
                    "engineering_path": project.engineering_path,
                    "agent": project.agent,
                    "pipeline_config": project.pipeline_config,
                    "jenkinsfile_content": project.jenkinsfile_content,
                    "jenkins_job_name": project.jenkins_job_name,
                    "jenkins_exists": jenkins_exists,
                    "jenkins_info": jenkins_info,
                    "created_at": project.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    "updated_at": project.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                    "creator": project.creator
                }
            })
            
        except PipelineProject.DoesNotExist:
            return Response({
                "success": False,
                "message": "项目不存在"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"获取项目详情失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"获取失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request, project_name):
        """更新项目配置"""
        try:
            project = PipelineProject.objects.get(project_name=project_name)
            data = request.data
            
            # 更新字段
            if 'agent' in data:
                project.agent = data['agent']
            if 'pipeline_config' in data:
                project.pipeline_config = data['pipeline_config']
                
                # 重新生成Jenkinsfile
                generator = JenkinsfileGenerator()
                generation_result = generator.generate(data['pipeline_config'])
                if not generation_result['success']:
                    return Response({
                        "success": False,
                        "message": "生成Jenkinsfile失败",
                        "errors": generation_result['errors']
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                project.jenkinsfile_content = generation_result['jenkinsfile']
                
                # 更新Jenkins Job
                jenkins_service = JenkinsService()
                jenkins_success = jenkins_service.create_or_update_pipeline_job(
                    project.jenkins_job_name,
                    project.jenkinsfile_content,
                    project.git_url,
                    project.pipeline_config,
                    project.branch,
                    project.engineering_path,
                    project.engineering_group
                )
                
                if not jenkins_success:
                    return Response({
                        "success": False,
                        "message": "更新Jenkins Job失败"
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            project.save()
            
            return Response({
                "success": True,
                "message": "项目更新成功"
            })
            
        except PipelineProject.DoesNotExist:
            return Response({
                "success": False,
                "message": "项目不存在"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"更新项目失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"更新失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, project_name):
        """删除项目 - 仅删除数据库记录"""
        try:
            project = PipelineProject.objects.get(id=project_name)
            
            # 删除数据库记录
            project.delete()
            
            logger.info(f"项目id删除成功: {project_name}")
            return Response({
                "success": True,
                "message": "项目删除成功"
            })
            
        except PipelineProject.DoesNotExist:
            return Response({
                "success": False,
                "message": "项目不存在"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"删除项目失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"删除失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PipelineBuildView(APIView):
    """Pipeline构建执行"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def post(self, request, project_name):
        """触发Pipeline构建"""
        try:
            project = PipelineProject.objects.get(project_name=project_name)
            jenkins_service = JenkinsService()
            
            # 获取构建参数
            build_params = request.data.get('parameters', {})
            build_id = generate_build_id(project_name)
            project = PipelineProject.objects.get(project_name=project_name)
            creator_email = request.user.email if request.user.is_authenticated else '<EMAIL>'
            creator = request.user.username if request.user.is_authenticated else ''   
            fs_task_id = send_feishu_notification_task.delay(creator_email, project.project_name, project.branch, '开始执行任务',creator)
            logger.info(f"消息队列 开始打包通知任务ID: {fs_task_id}")
            if project.engineering_group:
                project_name = f"{project.engineering_group}/{project.jenkins_job_name}"
            else:
                project_name = project.jenkins_job_name
            if not jenkins_service.check_job_exists(project_name):                  # 检查Jenkins Job是否存在
                    return Response({
                        "success": False,
                        "message": f"{project.jenkins_job_name} 项目不存在"    
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            pipeline_config = project.pipeline_config
            pipeline_config.update({
                'task_id': build_id,
                'django_base_url': 'http://**********:9000'
            })
            generator = JenkinsfileGenerator()
            jenkinsfile_result = generator.generate(pipeline_config)
            if jenkinsfile_result['success']:
                  new_jenkinsfile_content = jenkinsfile_result['jenkinsfile']
                  jenkins_success = jenkins_service.create_or_update_pipeline_job(
                    project.jenkins_job_name,
                    new_jenkinsfile_content,    # 改为新生成的内容
                    project.git_url,
                    pipeline_config,
                    project.branch,
                    project.engineering_path,
                    project.engineering_group
                  )
                  if not jenkins_success:
                    return Response({
                        "success": False,
                        "message": "更新Jenkins Job失败"
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                return Response({
                    "success": False,
                    "message": f"生成Jenkinsfile失败: {', '.join(jenkinsfile_result['errors'])}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            try:
                task_id, queue_id = jenkins_service.start_jenkins_job(
                    project.jenkins_job_name  if not project.engineering_group else f"{project.engineering_group}/{project.jenkins_job_name}",
                    build_params 
                )
            except Exception as e:
                return Response({
                    "success": False,
                    "message": f"触发构建失败: {str(e)}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            try:
                build_record = PipelineBuild.objects.create(
                  project=project,
                  task_id=build_id,  #build id 唯一值 
                  status='running',  # 已经在Jenkins中运行
                  start_time=timezone.now(),
                  jenkins_url='',  # 初始为空，回调时更新
                  upload_url='',   # 初始为空
                  created_by=request.user.username if request.user.is_authenticated else 'system',
                  creator_email = request.user.email if request.user.is_authenticated else '<EMAIL>' # 如果没有就发给杜迹
                )
                logger.info(f"构建记录已创建: {build_record.task_id}")
            except Exception as e:
              logger.error(f"创建构建记录失败: {str(e)}")
            return Response({
                "success": True,
                "message": "构建已触发",
                "data": {
                    "task_id": task_id,
                    "project_name": project_name,
                    "jenkins_job_name": project.jenkins_job_name,
                    "queue_id": queue_id
                }
            })
        except PipelineProject.DoesNotExist:
            return Response({
                "success": False,
                "message": "项目不存在"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"触发构建失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"触发失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class JenkinsTaskStatusView(APIView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.jenkins_service = JenkinsService()

    def get(self, request, task_id):
        """查询Jenkins任务状态"""
        logger.info(f"GET /auto_jenkins/tasks/{task_id}/status called")
        
        try:
            # 使用增强的任务状态查询
            result = self.jenkins_service.get_enhanced_task_status(task_id)
            
            if not result["success"]:
                return Response({
                    "success": False,
                    "message": result.get("error", "任务不存在或已过期")
                }, status=status.HTTP_404_NOT_FOUND)
            
            task_data = result["data"]
            data_source = result.get("source", "unknown")
            
            # 构造响应数据，不包含详细日志
            response_data = {
                "task_id": task_data["task_id"],
                "status": task_data["status"],
                "progress": task_data["progress"],
                "start_time": task_data["start_time"],
                "end_time": task_data["end_time"],
                "jenkins_build_number": task_data["jenkins_build_number"],
                "build_cmd": task_data.get("build_cmd"),
                "upload_cmd": task_data.get("upload_cmd"),
                "latest_logs": task_data["logs"][-5:] if task_data.get("logs") else [],  # 只返回最新5条日志
                "total_logs_count": len(task_data.get("logs", [])),
                "data_source": data_source  # 标识数据来源
            }
            
            if task_data.get("error_message"):
                response_data["error_message"] = task_data["error_message"]
            
            logger.info(f"任务状态查询成功: {task_id}, 来源: {data_source}")
            return Response({
                "success": True,
                "data": response_data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            error_msg = f"查询任务状态失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return Response({
                "success": False,
                "message": error_msg
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class JenkinsTaskLogsView(APIView):
    """获取任务日志"""
    def get(self, request, task_id):
        jenkins_service = JenkinsService()
        task_data = jenkins_service.get_task_data(task_id)
        
        if not task_data:
            return Response({
                "success": False,
                "message": "任务不存在或已过期"
            }, status=status.HTTP_404_NOT_FOUND)
        
        return Response({
            "success": True,
            "data": {
                "task_id": task_id,
                "logs": task_data.get("logs", [])
            }
        })

class JenkinsNodesView(APIView):
    """获取Jenkins节点列表"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取所有Jenkins节点"""
        try:
            jenkins_service = JenkinsService()
            result = jenkins_service.get_jenkins_nodes()
            
            if not result["success"]:
                return Response({
                    "success": False,
                    "message": result.get("error", "获取节点列表失败")
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            return Response({
                "success": True,
                "data": result["data"]
            })
            
        except Exception as e:
            logger.error(f"获取Jenkins节点列表失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"获取失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GitLabWebHookView(APIView):
    """GitLab WebHook接收接口"""
    authentication_classes = []  # git_lab触发Jenkins的自动打包
    permission_classes = []

    def post(self, request):
        """处理GitLab WebHook事件"""
        try:
            event_data = request.data
            logger.info(f"收到GitLab WebHook事件: {event_data}")
            logger.info(f"收到GitLab WebHook事件: {event_data.get('object_kind', 'unknown')}")
            
            # 获取Git URL
            project_info = event_data.get('project', {})
            git_url = project_info.get('git_http_url') or project_info.get('http_url')
            
            if not git_url:
                return Response({
                    "success": False,
                    "message": "无法获取Git URL"
                }, status=status.HTTP_400_BAD_REQUEST)
            branch = None
            object_kind = event_data.get('object_kind')
            
            if object_kind == 'merge_request':
                branch = event_data.get('object_attributes', {}).get('target_branch')            # Merge Request事件
            elif object_kind == 'push':                                                          # Push事件
                ref = event_data.get('ref', '')
                if ref.startswith('refs/heads/'):
                    branch = ref.replace('refs/heads/', '')
            if not branch:
                return Response({
                    "success": False,
                    "message": f"无法从事件中获取分支信息: {object_kind}"
                }, status=status.HTTP_400_BAD_REQUEST)
            try:                                                                                            # 查找匹配的项目配置: git_url + branch
                temp_url = urlparse(git_url) 
                path = temp_url.path.lstrip('/')
                git_url = f"git@{temp_url.hostname}:{path}"
                project = PipelineProject.objects.get(
                    git_url=git_url,
                    branch=branch,
                    enable_webhook=True
                )
                logger.info(f"找到匹配项目: {project.project_name}, 分支: {branch}")
                resulut_id = process_webhook_build_task.delay(project.id, branch)
                logger.info(f"消息队列 webhook 触发构建任务ID: {resulut_id}")
                return Response({
                    "success": True,
                    "message": f"成功触发构建: {project.project_name}",
                    "data": {
                        "project_name": project.project_name,
                        "branch": branch,
                        "celery_task_id": resulut_id.id  # 使用 .id 获取任务ID字符串
                    }
                })
            except PipelineProject.DoesNotExist:
                logger.warning(f"未找到匹配的项目配置: {git_url}, 分支: {branch}")
                return Response({
                    "success": False,
                    "message": f"未找到匹配的项目配置: Git URL = {git_url}, 分支 = {branch}"
                }, status=status.HTTP_404_NOT_FOUND)
                
        except Exception as e:
            error_msg = f"处理GitLab WebHook失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return Response({
                "success": False,
                "message": error_msg
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class JenkinsCallbackView(APIView):
    """Jenkins构建完成回调接口"""
    authentication_classes = []
    permission_classes = []
    
    def post(self, request):
        logger.info(f"Jenkins回调 - Headers: {dict(request.headers)}")
        logger.info(f"Jenkins回调 - Body: {request.body}")
        try:
            if request.content_type == 'application/json':
                callback_data = json.loads(request.body)
            else:
                callback_data = dict(request.POST)

            logger.info(f"Jenkins回调数据: {callback_data}")
            task_id = callback_data.get('task_id')
            jenkins_status = callback_data.get('status')
            duration_ms = callback_data.get('duration')
            jenkins_url = callback_data.get('jenkins_url', '')
            
            if not task_id:
                return Response({
                    "success": False,
                    "message": "缺少task_id"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            
            final_status = 'completed' if jenkins_status == 'SUCCESS' else 'failed'
            duration_seconds = Decimal(str(duration_ms)) / Decimal('1000')     # 转换duration
            end_time = timezone.now()

            # 更新构建记录
            updated_count = PipelineBuild.objects.filter(task_id=task_id).update(
                status=final_status,
                duration=duration_seconds,
                jenkins_url=jenkins_url,
                error_message='' if jenkins_status == 'SUCCESS' else f'Jenkins构建失败: {jenkins_status}',
                end_time=end_time
            )
            if updated_count == 0:
                logger.warning(f"未找到task_id为{task_id}的构建记录")
                return Response({
                    "success": False,
                    "message": f"未找到构建记录: {task_id}"
                }, status=status.HTTP_404_NOT_FOUND)
            logger.info(f"构建记录更新成功: {task_id}, 状态: {final_status}, 时长: {duration_seconds}秒")
            pipeline_job = PipelineBuild.objects.filter(task_id=task_id).first()
            project = pipeline_job.project
            result = '任务执行成功' if final_status == 'completed' else '任务执行失败'
            total_time = format_duration_mm_ss(duration_seconds)
            email = pipeline_job.creator_email if pipeline_job.creator_email else '<EMAIL>'
            # fs.send_jenkins_task_result_msg(email,project.project_name,project.branch, 
            #                                    result,pipeline_job.created_by,
            #                                    total_time,pipeline_job.upload_url) 
            fs_task_id = send_feishu_notification_task_result.delay(email,project.project_name,project.branch, result,
                                                                    pipeline_job.created_by,total_time,pipeline_job.upload_url)
            logger.info(f"消息队列 打包结果通知任务ID: {fs_task_id}")
            return Response({
                "success": True,
                "message": "回调处理成功",
                "data": {
                    "task_id": task_id,
                    "status": final_status,
                    "duration": str(duration_seconds) if duration_seconds else None
                }
            })
        except Exception as e:
            logger.error(f"Jenkins回调处理失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"回调处理失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PipelineBuildUpdateView(APIView):
    """通过git_url和branch更新构建记录的upload_url"""
    authentication_classes = []  # 供jenkins tools 调用   将打包升级的url  传到后端的url里 
    permission_classes = []
    
    def post(self, request):
        """更新最新构建记录的upload_url"""
        try:
            git_url = request.data.get('git_url')
            branch = request.data.get('branch') 
            upload_url = request.data.get('upload_url')
            
            if not all([git_url, branch, upload_url]):
                return Response({
                    "success": False,
                    "message": "缺少必需参数: git_url, branch, upload_url"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 1. 根据git_url和branch找到项目
            try:
                project = PipelineProject.objects.get(
                    git_url=git_url,
                    branch=branch
                )
            except PipelineProject.DoesNotExist:
                return Response({
                    "success": False, 
                    "message": f"未找到匹配的项目: git_url={git_url}, branch={branch}"
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 2. 找到该项目最新的运行中或已完成的构建记录
            build_record = PipelineBuild.objects.filter(
                project=project,
                status__in=['running', 'completed', 'failed']
            ).order_by('-start_time').first()
            
            if not build_record:
                return Response({
                    "success": False,
                    "message": f"项目 {project.project_name} 没有找到构建记录"
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 3. 更新upload_url
            build_record.upload_url = upload_url
            build_record.save()
            
            logger.info(f"构建记录upload_url已更新: task_id={build_record.task_id}, upload_url={upload_url}")
            
            return Response({
                "success": True,
                "message": "upload_url更新成功",
                "data": {
                    "project_name": project.project_name,
                    "upload_url": upload_url,
                    "build_status": build_record.status,
                    "message": "upload_url更新成功"
                }
            })
            
        except Exception as e:
            logger.error(f"更新upload_url失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"更新失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PipelineBuildCheckView(APIView):
      """通过git_url和branch查询构建记录的upload_url状态"""
      authentication_classes = []  # 供上位机调用 返回upload_url
      permission_classes = []

      def post(self, request):
          """查询最新构建记录是否有upload_url"""
          try:
              git_url = request.data.get('git_url')
              branch = request.data.get('branch')

              if not all([git_url, branch]):
                  return Response({
                      'success': False,
                      'message': '缺少必要参数: git_url 和 branch'
                  }, status=400)

              # 通过git_url和branch找到对应的项目
              try:
                  project = PipelineProject.objects.get(git_url=git_url, branch=branch)
              except PipelineProject.DoesNotExist:
                  return Response({
                      'success': False,
                      'message': f'未找到匹配的项目: git_url={git_url}, branch={branch}'
                  }, status=404)

              # 查找该项目的最新构建记录（第一条）
              latest_build = PipelineBuild.objects.filter(
                  project=project
              ).order_by('-start_time').first()  # 按开始时间降序，取第一条
              print(latest_build.id)

              if not latest_build:
                  return Response({
                      'success': False,
                      'message': '该项目暂无构建记录'
                  }, status=404)

              # 判断是否有upload_url
              has_upload_url = bool(latest_build.upload_url)

              return Response({
                  'success': True,
                  'data': {
                      'has_upload_url': has_upload_url,
                      'upload_url': latest_build.upload_url if has_upload_url else None,
                      'start_time': latest_build.start_time,
                      'end_time': latest_build.end_time
                  },
                  'message': '查询成功'
              })

          except Exception as e:
              return Response({
                  'success': False,
                  'message': f'服务器错误: {str(e)}'
              }, status=500)


class PipelineBuildListAll(APIView):
    """获取构建记录列表"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """获取构建记录列表（支持全量/按 git_url、branch 过滤；支持分页）"""
        try:
            git_url = request.data.get('git_url')
            branch = request.data.get('branch')
            page = int(request.data.get('page', 1))
            page_size = int(request.data.get('page_size', 20))
            belong_project = request.data.get('belong_project')  # 新增：所属项目
            if page < 1: page = 1
            if page_size < 1 or page_size > 200: page_size = 20

            # 基础查询：按时间倒序，连表避免 N+1
            qs = PipelineBuild.objects.select_related('project').order_by('-start_time')

            # 可选过滤
            if git_url:
                qs = qs.filter(project__git_url=git_url)
            if branch:
                qs = qs.filter(project__branch=branch)
            if belong_project:                         # 新增：按所属项目过滤
                qs = qs.filter(project__belong_project__iexact=belong_project)

            total = qs.count()
            start = (page - 1) * page_size
            end = start + page_size
            qs = qs[start:end]

            turn_status = {
                "completed": "成功",
                "failed": "失败",
                "running": "运行中",
            }
            builds_data = []
            for build in qs:
                p = build.project
                builds_data.append({
                    "id": build.id,
                    "task_id": build.task_id,
                    "project_id": p.id,
                    "job_name": p.project_name,
                    "git_url": getattr(p, 'git_url', None),
                    "branch": getattr(p, 'branch', None),
                    "status": turn_status.get(build.status, build.status),
                    "start_time": fmt(build.start_time),
                    "end_time": fmt(build.end_time),
                    "created_by": build.created_by or "系统",
                })

            return Response({
                "success": True,
                "data": builds_data,
                "total": total,
                "page": page,
                "page_size": page_size,
            })

        except Exception as e:
            logger.error(f"获取构建记录列表失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"获取失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PipeSearchExist(APIView):
    def post(self, request):
        try:
            git_url = request.data.get('git_url')
            branch = request.data.get('branch')

            if not git_url or not branch:
                return Response({
                    "success": False,
                    "message": "缺少必要参数: git_url 和 branch"
                }, status=status.HTTP_400_BAD_REQUEST)

            project = PipelineProject.objects.filter(git_url=git_url, branch=branch).first()
            if not project:
                return Response({
                    "success": False,
                    "message": "未找到匹配的项目"
                }, status=status.HTTP_404_NOT_FOUND)

            latest_build = (
                PipelineBuild.objects
                .filter(project=project)
                .order_by('-start_time')
                .first()
            )

            if not latest_build:
                return Response({
                    "success": True,
                    "data": {
                        "running": False,
                        "can_build": True,
                        "latest": None
                    },
                    "message": "该项目暂无构建记录"
                })

            is_running = (latest_build.status == 'running' and latest_build.end_time is None)  # 没有回调更新记录 则认为还在运行
            latest_info = { 
                "status": latest_build.status,
                "start_time": latest_build.start_time.strftime('%Y-%m-%d %H:%M:%S') if latest_build.start_time else None,
                "end_time": latest_build.end_time.strftime('%Y-%m-%d %H:%M:%S') if latest_build.end_time else None,
            }
            return Response({
                "success": True,
                "data": {
                    "running": is_running,
                    "can_build": not is_running,
                    "latest": latest_info
                },
                "message": "查询成功" if not is_running else "项目正在运行中"
            })
        except Exception as e:
            logger.error(f"获取项目是否存在失败: {str(e)}")


class JenkinsConsoleLogsView(APIView):
      """获取Jenkins控制台日志"""
      authentication_classes = [JWTAuthentication]
      permission_classes = [IsAuthenticated]

      def get(self, request, task_id):
          try:
              # 根据task_id找到构建记录
              build = PipelineBuild.objects.get(task_id=task_id)

              if not build.jenkins_url:
                  return Response({
                      "success": False,
                      "message": "该构建记录缺少Jenkins URL"
                  }, status=400)
              console_url = build.jenkins_url.rstrip('/') + '/consoleText'   
              response = requests.get(console_url, auth=(jenkins_user, jenkins_token),  timeout=30)
              if response.status_code == 200:
                  console_logs = response.text.split('\n')
                  return Response({
                      "success": True,
                      "data": {
                          "task_id": task_id,
                          "jenkins_logs": console_logs,
                          "jenkins_url": build.jenkins_url
                      }
                  })
              else:
                  return Response({
                      "success": False,
                      "message": f"获取Jenkins日志失败，状态码: {response.status_code}"
                  }, status=500)

          except PipelineBuild.DoesNotExist:
              return Response({
                  "success": False,
                  "message": "未找到对应的构建记录"
              }, status=404)
          except Exception as e:
              logger.error(f"获取Jenkins控制台日志失败: {str(e)}")
              return Response({
                  "success": False,
                  "message": f"获取日志失败: {str(e)}"
              }, status=500)
    

class SoftVersionView(APIView):
    """获取软件的相关版本"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取软件的相关版本"""
        data = software_hash()
        return Response({
            "success": True,
            "data": data,
            "message": "获取软件版本成功"
        })
