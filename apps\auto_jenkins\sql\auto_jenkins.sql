--
-- Pipeline项目配置表
--
CREATE TABLE public.pipeline_projects (
    id SERIAL PRIMARY KEY,
    project_name VARCHAR(100) UNIQUE NOT NULL,
    git_url VARCHAR(200) NOT NULL,
    engineering_group VARCHAR(50) NOT NULL,
    engineering_path VARCHAR(200) NOT NULL,
    agent <PERSON><PERSON><PERSON><PERSON>(50) DEFAULT 'slave_win10' NOT NULL,
    pipeline_config JSONB NOT NULL,
    jenkinsfile_content TEXT NOT NULL,
    jenkins_job_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
    creator VA<PERSON><PERSON><PERSON>(50) NOT NULL
);



ALTER TABLE public.pipeline_projects
ADD COLUMN belong_project VARCHAR(256) NOT NULL DEFAULT '',
ADD COLUMN flow_config VARCHAR(256) NOT NULL DEFAULT '',
ADD COLUMN frontend_raw_config J<PERSON>NB NOT NULL DEFAULT '{}';


ALTER TABLE public.pipeline_projects    #添加enable_webhook字段
ADD COLUMN enable_webhook BOOLEAN NOT NULL DEFAULT FALSE;

 
ALTER TABLE public.pipeline_projects #删除project_name的唯一约束
DROP CONSTRAINT IF EXISTS pipeline_projects_project_name_key;


ALTER TABLE public.pipeline_projects
ADD COLUMN branch VARCHAR(100) NOT NULL DEFAULT 'master'; # 添加分支


ALTER TABLE public.pipeline_projects
ADD CONSTRAINT pipeline_projects_git_url_branch_key UNIQUE (git_url, branch);  #添加联合唯一的索引



CREATE TABLE public.pipeline_builds (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL REFERENCES pipeline_projects(id) ON DELETE CASCADE,
    task_id VARCHAR(100) UNIQUE NOT NULL,
    status VARCHAR(100) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    jenkins_url VARCHAR(200) NOT NULL,
    upload_url VARCHAR(200) NOT NULL,
    end_time TIMESTAMP,
    error_message TEXT,
    created_by VARCHAR(100),
   duration NUMERIC(10,3)  -- 总共10位数字，3位小数
);


ALTER TABLE public.pipeline_projects ADD COLUMN IF NOT EXISTS creator_email VARCHAR(100);
ALTER TABLE public.pipeline_builds ADD COLUMN IF NOT EXISTS creator_email VARCHAR(100);