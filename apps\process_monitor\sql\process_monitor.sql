--测试计划表
CREATE TABLE IF NOT EXISTS public.process_monitor(
    id serial primary key, --id
    run_id varchar(255),
    code varchar(255),
    name varchar(255),
    psn varchar(255),
    status BOOLEAN,
    "value" varchar(255),
    extra_info text,
    cur_time timestamp,
    project_name varchar(255),   --项目名称
    project_number varchar(255),   --项目编号
    test_plan_name varchar(255),   --计划名称
    test_plan_id int,   --计划id
    tester_name varchar(255),   --测试人员
    tester_email varchar(255),   --测试人员邮箱
    create_time timestamp --创建时间
);