from rest_framework import serializers


class TestPlanList<PERSON>erializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    project_number = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    creator_name_re = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    plan_type_list = serializers.ListField(child=serializers.CharField(), min_length=0, required=False)
    test_type_list = serializers.ListField(child=serializers.Char<PERSON><PERSON>(), min_length=0, required=False)
    m_version = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    result_two = serializers.Char<PERSON><PERSON>(required=False, allow_null=True, allow_blank=True)
    status = serializers.Char<PERSON><PERSON>(max_length=255, required=False, allow_null=True, allow_blank=True)
    status_list = serializers.ListField(child=serializers.Char<PERSON><PERSON>(max_length=255), min_length=0, required=False)
    p_start_time = serializers.DateField(required=False, allow_null=True)
    p_end_time = serializers.DateField(required=False, allow_null=True)
    action_type = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    id = serializers.IntegerField(required=False)


class TestPlanSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    abnormal_stop = serializers.BooleanField()
    finish_notice = serializers.BooleanField()
    product_version = serializers.CharField()
    sub_versions = serializers.CharField()
    m_version = serializers.CharField()
    test_type = serializers.CharField(max_length=255)
    test_type_name = serializers.CharField(max_length=255)
    project_name = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255)
    project_id = serializers.CharField(max_length=255)
    test_cases = serializers.ListField(child=serializers.IntegerField(), min_length=0, default=[])
    test_case_extra_args = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    plan_use = serializers.CharField(max_length=255)
    plan_type = serializers.IntegerField()
    p_start_time = serializers.DateField(required=False, allow_null=True)
    p_end_time = serializers.DateField(required=False, allow_null=True)
    pic_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    pic_email = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    sample_information = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    test_product_id = serializers.IntegerField(required=False, allow_null=True, default=None)


class TestPlanCopySerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    abnormal_stop = serializers.BooleanField()
    finish_notice = serializers.BooleanField()
    product_version = serializers.CharField()
    sub_versions = serializers.CharField()
    m_version = serializers.CharField()
    test_type = serializers.CharField(max_length=255)
    test_type_name = serializers.CharField(max_length=255)
    project_name = serializers.CharField(max_length=255)
    project_number = serializers.CharField(max_length=255)
    project_id = serializers.CharField(max_length=255)
    plan_use = serializers.CharField(max_length=255)
    plan_type = serializers.IntegerField()
    p_start_time = serializers.DateField(required=False, allow_null=True)
    p_end_time = serializers.DateField(required=False, allow_null=True)
    pic_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    pic_email = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    sample_information = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)


class TestPlanUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    desc = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    abnormal_stop = serializers.BooleanField()
    finish_notice = serializers.BooleanField()
    test_type = serializers.CharField(max_length=255)
    test_type_name = serializers.CharField(max_length=255)
    test_cases = serializers.ListField(child=serializers.IntegerField(), min_length=0, default=[])
    m_version = serializers.CharField()
    sub_versions = serializers.CharField()
    product_version = serializers.CharField()
    test_case_extra_args = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    plan_use = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    p_start_time = serializers.DateField(required=False, allow_null=True)
    p_end_time = serializers.DateField(required=False, allow_null=True)
    pic_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    pic_email = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    sample_information = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    test_product_id = serializers.IntegerField(required=False, allow_null=True, default=None)


class TestSubPlanStatusUpdateSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    sub_plan_id = serializers.IntegerField()
    status = serializers.BooleanField()


class TestPlanReviewRSerializer(serializers.Serializer):
    project_number = serializers.CharField(max_length=255)
    name = serializers.CharField(max_length=255)
    status = serializers.CharField(max_length=255)


class TestPlanUpdateStatusSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    status = serializers.CharField(max_length=255)


class TestCaseExecSerializer(serializers.Serializer):
    test_plan_id = serializers.IntegerField()
    test_case_id = serializers.IntegerField()
    test_case_version = serializers.CharField(max_length=255)
    exec_id = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    result = serializers.BooleanField(required=False, allow_null=True, default=None)
    result_two = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    value = serializers.CharField()
    remark = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    generation_mode = serializers.IntegerField()
    p_start_time = serializers.DateField(required=False, allow_null=True)
    p_end_time = serializers.DateField(required=False, allow_null=True)
    file = serializers.FileField(required=False, allow_null=True)


class TestCaseResultSyncSerializer(serializers.Serializer):
    test_plan_id = serializers.IntegerField()


class TestPlanTestCasesSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    disabled = serializers.BooleanField(allow_null=True, required=False, default=None)
    execute_mode = serializers.ListField(child=serializers.CharField(), min_length=0, required=False)
    module = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    name = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    result_two = serializers.ListField(child=serializers.CharField(required=False, allow_null=True, allow_blank=True),
                                       min_length=0, required=False, allow_null=True)
    status = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    p_start_time = serializers.DateField(required=False, allow_null=True)
    p_end_time = serializers.DateField(required=False, allow_null=True)


class TestPlanTestCasesDeleteSerializer(serializers.Serializer):
    test_case_ids = serializers.ListField(child=serializers.IntegerField(), min_length=1)


class TestPlanTestCasesAddSerializer(serializers.Serializer):
    test_case_ids = serializers.ListField(child=serializers.IntegerField(), min_length=1)


class TestPlanTestCasesUpdateSerializer(serializers.Serializer):
    test_case_ids = serializers.ListField(child=serializers.IntegerField(), min_length=0, required=False)


class RelatedVersionsPushSerializer(serializers.Serializer):
    test_plan_id = serializers.IntegerField()
