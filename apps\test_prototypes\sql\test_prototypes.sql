--测试样件表
CREATE TABLE IF NOT EXISTS public.test_prototypes(
    id serial primary key,
    "name" VARCHAR(255),
    "type" INTEGER,
    "status" INTEGER,
    "number" VARCHAR(255),
    "project" VARCHAR(255),
    "project_name" VARCHAR(255),
    "desc" text,
    create_time timestamp,
    update_time timestamp,
    user_name varchar(255),
	user_email varchar(255),
	project_name varchar(255),
	prototype_stage int
);
ALTER TABLE public.test_prototypes OWNER TO atpms;
