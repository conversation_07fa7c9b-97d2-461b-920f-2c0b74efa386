-- 创建一级分组表
CREATE TABLE public.level1_group (
    id SERIAL PRIMARY KEY,
    var_name VARCHAR NOT NULL,
    node_level VARCHAR NOT NULL,
    "group" VARCHAR NOT NULL,
    "desc" VARCHAR
);





-- 创建二级主参数表
CREATE TABLE public.level2_main_param (
    id SERIAL PRIMARY KEY,
    var_name VARCHAR NOT NULL,
    node_level VARCHAR NOT NULL,
    "group" VARCHAR NOT NULL,
    parent_param_key VARCHAR NOT NULL,
    parent_param_name VARCHAR NOT NULL,
    "desc" VARCHAR,
    type VARCHAR NOT NULL,
    default_value VARCHAR
);




-- 创建三级子参数表
CREATE TABLE public.level3_sub_param (
    id SERIAL PRIMARY KEY,
    "group" VARCHAR NOT NULL,
    parent_param_key VARCHAR NOT NULL,
    sub_param_key VARCHAR NOT NULL UNIQUE,
    sub_param_name VARCHAR NOT NULL,
    param_type VARCHAR NOT NULL,
    default_value VARCHAR NOT NULL,
    "desc" VARCHAR,
    var_name VARCHAR NOT NULL ,
    node_level VARCHAR NOT NULL
);




-- 创建主参数枚举值表
CREATE TABLE public.main_param_enum (
    id SERIAL PRIMARY KEY,
    param_key VARCHAR NOT NULL,
    enum_key JSONB NOT NULL,
    var_name VARCHAR NOT NULL,          -- 无默认值
    node_level VARCHAR NOT NULL        -- 无默认值
);


-- 创建主参数数字范围值表
CREATE TABLE public.main_param_unit8(
    id SERIAL PRIMARY KEY,
    param_key VARCHAR NOT NULL,
    "max" VARCHAR NOT NULL,
    "min" VARCHAR NOT NULL,
    unit VARCHAR NOT NULL,
    var_name VARCHAR NOT NULL,          -- 无默认值
    node_level VARCHAR NOT NULL        -- 无默认值
);



-- 创建关联关系表（子参数与枚举值关联表）
CREATE TABLE public.sub_param_enum_relation (
    id SERIAL PRIMARY KEY,
    parent_param_key VARCHAR NOT NULL,
    parent_enum_key VARCHAR NOT NULL,
    sub_param_key JSONB NOT NULL,        -- 使用 JSONB 类型
    var_name VARCHAR NOT NULL,        -- 无默认值
    node_level VARCHAR NOT NULL      -- 无默认值
);

-- 创建表（
CREATE TABLE public.main_special_enum (
  id SERIAL PRIMARY KEY,
  "type" VARCHAR(50) NOT NULL,
  enum_key VARCHAR(500),
  optional_key TEXT[] DEFAULT '{}',
  project_code VARCHAR(50) NULL,
  project_name VARCHAR(120) NULL,
  project_gitlab VARCHAR(255) NULL,
  project_branch VARCHAR(120) NULL
);