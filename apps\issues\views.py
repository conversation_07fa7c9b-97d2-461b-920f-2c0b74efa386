import traceback
import logging
import json
import os
import uuid

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.files.storage import FileSystemStorage
from django.conf import settings

from .serializers import IssueListSerializer, IssueSerializer, IssuePushSerializer
from .models import IssueModel
from users.models import UserFSInfo
from utils.fs_service import FSService

logger = logging.getLogger("machine")

issue_image_fs = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, "issues/images"),
                                   base_url="/media/issues/images")
issue_video_fs = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, "issues/videos"),
                                   base_url="/media/issues/videos")


class IssuesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = IssueListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            model = IssueModel()
            content = model.list(**serializer.validated_data)

            for i in content["results"]:
                i["occur_time"] = i["occur_time"].strftime("%Y-%m-%d %H:%M:%S")
                if i["video"]:
                    i["video"] = issue_video_fs.url(i["video"])
                else:
                    i["video"] = None
                if i["images"]:
                    i["images"] = [issue_image_fs.url(j) for j in i["images"].split(", ")]
                else:
                    i["images"] = []

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        try:
            serializer = IssueSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            image_files = serializer.validated_data.get("images", [])
            image_file_paths = []
            for image_file in image_files:
                dir_name = str(uuid.uuid4())
                image_file_path = issue_image_fs.save(os.path.join(dir_name, image_file.name), image_file)
                image_file_paths.append(image_file_path)
            image_file_paths = ", ".join(image_file_paths)

            video_file = serializer.validated_data.get("video")
            if video_file:
                dir_name = str(uuid.uuid4())
                video_file_path = issue_video_fs.save(os.path.join(dir_name, video_file.name), video_file)
            else:
                video_file_path = ""

            model = IssueModel()
            raw = {}
            for i in request.data:
                if i in ["images", "video"]:
                    continue
                raw[i] = request.data[i]
            model.create(**serializer.validated_data, raw=json.dumps(raw),
                         image_paths=image_file_paths, video_path=video_file_path)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class IssueDetailView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        try:
            model = IssueModel()
            content = model.retrieve(pk=pk)

            return Response({"err_code": 0, "data": content, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        try:
            model = IssueModel()
            model.delete(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class IssuePushView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, pk=None):
        try:
            model = IssueModel()
            r = model.retrieve(pk)
            if not r:
                return Response({"err_code": 1, "msg": "问题不存在"}, status.HTTP_400_BAD_REQUEST)
            if r.get("status") != 0:
                return Response({"err_code": 1, "msg": "只有未处理状态的问题可以推送"}, status.HTTP_400_BAD_REQUEST)

            serializer = IssuePushSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)

            f, data = FSService().push_issue(
                token=user_fs_info.access_token,
                **serializer.validated_data
            )

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

            model.update(status=1, pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class IssueCancelView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, pk=None):
        try:
            model = IssueModel()

            r = model.retrieve(pk)
            if not r:
                return Response({"err_code": 1, "msg": "问题不存在"}, status.HTTP_400_BAD_REQUEST)
            if r.get("status") != 0:
                return Response({"err_code": 1, "msg": "只有未处理状态的问题可以取消"}, status.HTTP_400_BAD_REQUEST)

            images = r.get("images")
            if images:
                for i in images.split(", "):
                    fn = i
                    dir_n = os.path.dirname(issue_image_fs.path(fn))
                    issue_image_fs.delete(fn)
                    try:
                        os.rmdir(dir_n)
                    except Exception:
                        pass
            video = r.get("video")
            if video:
                fn = video
                dir_n = os.path.dirname(issue_video_fs.path(fn))
                issue_video_fs.delete(fn)
                try:
                    os.rmdir(dir_n)
                except Exception:
                    pass

            model.cancel(pk=pk)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProjectVersionTypeView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)

            f, data = FSService().get_project_version_type(
                token=user_fs_info.access_token
            )

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("msg")}, status.HTTP_200_OK)

            return Response({"err_code": 0, "data": data.get("data"), "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProjectVersionNumberView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user
            user_fs_info = UserFSInfo.objects.get(employee_number=user.employee_number)

            f, data = FSService().get_project_version_number(
                token=user_fs_info.access_token,
                project_number=request.query_params.get("project_number"),
                type_=request.query_params.get("type")
            )

            if not f:
                if data.get("message") == 401:
                    return Response({"err_code": 3, "msg": "产品开发平台token过期"}, status.HTTP_401_UNAUTHORIZED)
                return Response({"err_code": 3, "msg": data.get("msg")}, status.HTTP_200_OK)

            return Response({"err_code": 0, "data": data.get("data"), "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
