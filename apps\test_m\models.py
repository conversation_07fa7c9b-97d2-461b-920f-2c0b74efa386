import datetime

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone, sql_insert_many
from django.db import transaction


class TestCaseModel:
    def __init__(self):
        self.table_name = "public.test_cases"

    def create(self, **kwargs):
        now = datetime.datetime.now()
        params = {
            "name": kwargs.get("name"),
            "project_number": kwargs.get("project_number"),
            "tc_file": kwargs.get("tc_file_path"),
            "submitter": kwargs.get("submitter"),
            "version": kwargs.get("version_"),
            "create_time": now,
            "update_time": now,
        }

        sql = """
            INSERT INTO {table_name} ("name", project_number, submitter, tc_file, version,
                create_time, update_time) 
            VALUES (%(name)s, %(project_number)s, %(submitter)s, %(tc_file)s, %(version)s,
            %(create_time)s, %(update_time)s)
            ON CONFLICT ( project_number ) DO UPDATE
            SET "name" = excluded.name, submitter = excluded.submitter, tc_file = excluded.tc_file,
            version = excluded.version,
            update_time = excluded.update_time
            returning id, "name", project_number, version, update_time
            ;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):

        params = {
        }

        sql_where_list = []

        name = kwargs.get("name")
        if name is not None and name != '':
            sql_where_list.append("tc.name = %(name)s")
            params["name"] = name

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("tc.project_number = %(project_number)s")
            params["project_number"] = project_number

        submitter = kwargs.get("submitter")
        if submitter is not None and submitter != '':
            sql_where_list.append("u.username = %(submitter)s")
            params["submitter"] = submitter

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name} as tc
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name} as tc
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY update_time desc"

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} as tc left join public.user as u
                on tc.submitter = u.employee_number
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT tc.id, tc."name", tc.project_number, u.username as submitter, tc.version, tc.tc_file,
                tc.create_time, tc.update_time
             FROM {table_name} as tc left join public.user as u
             on tc.submitter = u.employee_number
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT tc.id, tc."name", tc.project_number, u.username as submitter, tc.version,  tc.tc_file,
                tc.create_time, tc.update_time
                FROM {table_name} as tc left join public.user as u
                on tc.submitter = u.employee_number
                WHERE tc.id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def get(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT tc.id, tc."name", tc.project_number, u.username as submitter, tc.version,  tc.tc_file,
                tc.create_time, tc.update_time
             FROM {table_name} as tc left join public.user as u
             on tc.submitter = u.employee_number
            {sql_where}
            LIMIT 1
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        project_number = kwargs.get("project_number")
        if project_number is not None:
            sql_set_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        occur_time = kwargs.get("occur_time")
        if occur_time is not None:
            sql_set_list.append("occur_time = %(occur_time)s")
            params["occur_time"] = occur_time

        tester = kwargs.get("tester")
        if tester is not None:
            sql_set_list.append("tester = %(tester)s")
            params["tester"] = tester

        machine_number = kwargs.get("machine_number")
        if machine_number is not None:
            sql_set_list.append("machine_number = %(machine_number)s")
            params["machine_number"] = machine_number

        status = kwargs.get("status")
        if status is not None:
            sql_set_list.append("status = %(status)s")
            params["status"] = status

        version = kwargs.get("version")
        if version is not None:
            sql_set_list.append("version = %(version)s")
            params["version"] = version

        raw = kwargs.get("raw")
        if raw is not None:
            sql_set_list.append("raw = %(raw)s")
            params["raw"] = raw

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result


class TestCase2Model:
    def __init__(self):
        self.table_name = "public.test_case2"

    def create(self, **kwargs):
        params = {
            "name": kwargs.get("name"),
            "number": kwargs.get("number"),
            "desc": kwargs.get("desc"),
            "create_time": datetime.datetime.now(),
            "update_time": datetime.datetime.now(),
        }

        sql = """
            INSERT INTO {table_name} ("name",  "number", "desc", create_time, update_time) 
            VALUES (%(name)s, %(number)s, %(desc)s, %(create_time)s, %(update_time)s);
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):
        name = kwargs.get("name")

        params = {
        }

        sql_where_list = []

        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name}
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} 
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, "name", "number", "desc", create_time, update_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", "number", "desc", create_time, update_time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        number = kwargs.get("number")
        if number is not None:
            sql_set_list.append("number = %(number)s")
            params["number"] = number

        desc = kwargs.get("desc")
        if desc is not None:
            sql_set_list.append("\"desc\" = %(desc)s")
            params["desc"] = desc

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result

    def bulk_create(self, data):
        col_names = ["name", "number", "desc"]
        argslist = []

        now = datetime.datetime.now()

        for i in data:
            temp_list = []
            for j in col_names:
                temp_list.append(i.get(j))
            temp_list.append(now)
            temp_list.append(now)
            argslist.append(temp_list)

        sql = """
            INSERT INTO {table_name} ("name", "number", "desc", create_time, update_time) 
            VALUES %s 
            ON CONFLICT ("name") DO NOTHING
            ;
        """.format(table_name=self.table_name)

        sql_insert_many(sql, argslist=argslist)


class TestCaseTypeModel:
    def __init__(self):
        self.table_name = "public.test_case_types"

    def create(self, **kwargs):
        params = {
            "name": kwargs.get("name"),
            "number": kwargs.get("number"),
            "desc": kwargs.get("desc"),
            "create_time": datetime.datetime.now(),
            "update_time": datetime.datetime.now(),
        }

        sql = """
            INSERT INTO {table_name} ("name",  "number", "desc", create_time, update_time) 
            VALUES (%(name)s, %(number)s, %(desc)s, %(create_time)s, %(update_time)s);
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {
        }

        sql_where_list = []

        name_re = kwargs.get("name_re")
        if name_re is not None and name_re != '':
            sql_where_list.append("name ~* %(name_re)s")
            params["name_re"] = name_re

        number_re = kwargs.get("number_re")
        if number_re is not None and number_re != '':
            sql_where_list.append("number ~* %(number_re)s")
            params["number_re"] = number_re

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name}
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} 
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, "name", "number", "desc", create_time, update_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", "number", "desc", create_time, update_time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        number = kwargs.get("number")
        if number is not None:
            sql_set_list.append("number = %(number)s")
            params["number"] = number

        desc = kwargs.get("desc")
        if desc is not None:
            sql_set_list.append("\"desc\" = %(desc)s")
            params["desc"] = desc

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result

    def bulk_create(self, data):
        col_names = ["name", "number", "desc"]
        argslist = []

        now = datetime.datetime.now()

        for i in data:
            temp_list = []
            for j in col_names:
                temp_list.append(i.get(j))
            temp_list.append(now)
            temp_list.append(now)
            argslist.append(temp_list)

        sql = """
            INSERT INTO {table_name} ("name", "number", "desc", create_time, update_time) 
            VALUES %s 
            ON CONFLICT ("name") DO NOTHING
            ;
        """.format(table_name=self.table_name)

        sql_insert_many(sql, argslist=argslist)


class TestCaseStatusModel:
    def __init__(self):
        self.table_name = "public.test_case_status"

    def create(self, **kwargs):
        params = {
            "name": kwargs.get("name"),
            "number": kwargs.get("number"),
            "desc": kwargs.get("desc"),
            "create_time": datetime.datetime.now(),
            "update_time": datetime.datetime.now(),
        }

        sql = """
            INSERT INTO {table_name} ("name",  "number", "desc", create_time, update_time) 
            VALUES (%(name)s, %(number)s, %(desc)s, %(create_time)s, %(update_time)s);
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    @staticmethod
    def _build_sql_where(**kwargs):
        name = kwargs.get("name")

        params = {
        }

        sql_where_list = []

        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name}
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} 
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, "name", "number", "desc", create_time, update_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", "number", "desc", create_time, update_time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        number = kwargs.get("number")
        if number is not None:
            sql_set_list.append("number = %(number)s")
            params["number"] = number

        desc = kwargs.get("desc")
        if desc is not None:
            sql_set_list.append("\"desc\" = %(desc)s")
            params["desc"] = desc

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result

    def bulk_create(self, data):
        col_names = ["name", "number", "desc"]
        argslist = []

        now = datetime.datetime.now()

        for i in data:
            temp_list = []
            for j in col_names:
                temp_list.append(i.get(j))
            temp_list.append(now)
            temp_list.append(now)
            argslist.append(temp_list)

        sql = """
            INSERT INTO {table_name} ("name", "number", "desc", create_time, update_time) 
            VALUES %s 
            ON CONFLICT ("name") DO NOTHING
            ;
        """.format(table_name=self.table_name)

        sql_insert_many(sql, argslist=argslist)
