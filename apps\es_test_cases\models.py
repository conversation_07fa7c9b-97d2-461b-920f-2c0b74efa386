from django.db import models


class NIOTestCaseModel(models.Model):
    id = models.Char<PERSON><PERSON>(max_length=255, primary_key=True)
    test_category = models.Char<PERSON><PERSON>(max_length=255)
    name = models.Char<PERSON>ield(max_length=255)
    test_steps = models.Char<PERSON>ield(null=True, blank=True)
    expected_result = models.CharField(null=True, blank=True)
    priority = models.Char<PERSON>ield(max_length=50)
    software_iteration = models.CharField(max_length=50)
    config_changes = models.Char<PERSON>ield(max_length=50)
    boot_iteration = models.Char<PERSON>ield(max_length=50)
    hardware_iteration = models.Char<PERSON>ield(max_length=50)
    test_result = models.CharField(max_length=50, null=True, blank=True)
    test_data = models.Char<PERSON>ield(null=True, blank=True)
    remarks = models.CharField(null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        managed = False
        app_label = 'es_test_cases'
        db_table = 'nio_test_cases'


class VoyahTestCaseModel(models.Model):
    id = models.CharField(max_length=255, primary_key=True)
    module_category = models.Char<PERSON>ield(max_length=255)
    test_category = models.CharField(max_length=255, null=True, blank=True)
    name = models.CharField(max_length=255)
    precondition = models.CharField(null=True, blank=True)
    test_steps = models.CharField(null=True, blank=True)
    expected_result = models.CharField(null=True, blank=True)
    test_purpose = models.CharField(null=True, blank=True)
    test_result = models.CharField(max_length=50, null=True, blank=True)
    test_data = models.CharField(null=True, blank=True)
    remarks = models.CharField(null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        managed = False
        app_label = 'es_test_cases'
        db_table = 'voyah_test_cases'
