import requests
import openpyxl

workbook = openpyxl.load_workbook('H56C协议栈测试用例.xlsx')


def unmerge_sheet_cells(sheet):
    # 获取所有合并单元格的范围列表
    merged_cells = list(sheet.merged_cells.ranges)

    # 遍历每个合并单元格的范围
    for merged_cell in merged_cells:
        # 获取合并单元格的范围坐标
        min_col, min_row, max_col, max_row = merged_cell.bounds
        # 获取合并单元格左上角的值
        value = sheet.cell(row=min_row, column=min_col).value
        # 拆分合并单元格
        sheet.unmerge_cells(str(merged_cell))
        # 将值填充到原合并范围的每个单元格
        for row in range(min_row, max_row + 1):
            for col in range(min_col, max_col + 1):
                sheet.cell(row=row, column=col).value = value


for sheet in workbook.worksheets:
    if sheet.title.strip() not in ["LIN从测试"]:
        continue

    unmerge_sheet_cells(sheet)

    count = 0
    for row in sheet.iter_rows(values_only=True):
        count += 1

        if count < 3:
            continue

        if not row[0]:
            print(count, row)
            continue

        id_ = row[0]
        module_category = row[1]
        test_category = row[2]
        name = row[3]
        precondition = row[4]
        test_steps = row[5]
        expected_result = row[6]
        test_purpose = row[7]
        test_result = row[8]
        test_data = row[9]
        remarks = row[10]

        data = {
            "id": id_,
            "module_category": module_category,
            "test_category": test_category,
            "name": name,
            "precondition": precondition,
            "test_steps": test_steps,
            "expected_result": expected_result,
            "test_purpose": test_purpose,
            "test_result": test_result,
            "test_data": test_data,
            "remarks": remarks,
        }

        r = requests.post("http://127.0.0.1:9091/es_test_cases/voyah", json=data)
        r = r.json()
        print(r)

        if r.get("err_code") != 0:
            print(sheet.title, count)

