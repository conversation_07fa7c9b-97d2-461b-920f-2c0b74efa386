import psycopg2
import os
from datetime import datetime  # 导入 datetime 库用来获取当前时间
from django.core.files.storage import FileSystemStorage
import os
import uuid
from django.conf import settings
from djangoblog import settings as myproject_settings

settings.configure(default_settings=myproject_settings, DEBUG=True)

image_resource_fs = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, "image_resources"),
                                      base_url="/media/image_resources")
def image_func(image_name,image_data):
    # 生成缩略图
    dir_name = str(uuid.uuid4())
    print(f"dir_name: {dir_name}")
    image_path = image_resource_fs.save(os.path.join(dir_name, image_name),image_data)
    return  image_path


# 连接数据库
try:
    mydb = psycopg2.connect(
        host="*********",
        user="atpms",
        password="TgeIJgD9",
        database="atpms",
        port="5432"
    )
    # 获取游标
    cursor = mydb.cursor()
    print("数据库连接成功！")

    # 照片路径
    photo_folder_path = "../gray/"
    photo_files = os.listdir(photo_folder_path)
    photo_files = sorted(photo_files, key=lambda x: int(os.path.splitext(x)[0]))


    # 构造插入语句的值部分的列表
    values_list = []
    for i, photo_file in enumerate(photo_files):  # 默认从0开始计数
        # 构造每条记录的对应字段
        name = f"灰阶{i}"
        rgb = "#%02X%02X%02X" % (i, i, i)
        desc = f"灰阶图片{i}"

        photo_full_path = os.path.join(photo_folder_path, photo_file)

        # 读取图片文件内容
        with open(photo_full_path, 'rb') as f:
            image_data = f

            image_path = image_func(photo_file, image_data)
        # 获取当前时间
            dir_name = str(uuid.uuid4())
            thumbnail_image_path = os.path.join(dir_name, f"thumbnail_{photo_file}")
            current_time = datetime.now()
            thumbnail_image_path = image_resource_fs.save(thumbnail_image_path, image_data)

        # 将数据添加到值列表中
            values_list.append((i + 1, name, image_path, 1, rgb, desc, current_time, current_time,thumbnail_image_path))  # id, name, image, type, rgb, desc, create_time, update_time

    # 构造sql插入语句
    sql = """
    INSERT INTO image_resources (id, name, image, type, rgb, "desc", create_time, update_time,thumbnail) 
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s,%s)
    """

    # 执行批量插入
    cursor.executemany(sql, values_list)
    mydb.commit()
    print("数据插入成功！")
except psycopg2.Error as err:
    print(f"数据库操作出现错误: {err}")
finally:
    # 关闭游标和数据库连接
    if cursor:
        cursor.close()
    if mydb:
        mydb.close()
