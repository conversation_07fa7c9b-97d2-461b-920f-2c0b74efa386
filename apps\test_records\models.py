import datetime
import json

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone, sql_insert_many
from django.db import transaction

from test_cases.models import TestCaseModel


class TestRecordModel:
    def __init__(self):
        self.table_name = "public.test_records"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            params = {
                "task_id": kwargs.get("task_id"),
                "test_record_id": kwargs.get("test_record_id"),
                "project_number": kwargs.get("project_number"),
                "project_name": kwargs.get("project_name"),
                "tester_email": kwargs.get("tester_email"),
                "tester_name": kwargs.get("tester_name"),
                "tester_employee_number": kwargs.get("tester_employee_number"),
                "test_plan_name": kwargs.get("test_plan_name"),
                "test_plan_id": kwargs.get("test_plan_id"),
                "test_sub_plan_name": kwargs.get("test_sub_plan_name"),
                "test_sub_plan_id": kwargs.get("test_sub_plan_id"),
                "test_case_number": kwargs.get("test_case_number"),
                "test_case_name": kwargs.get("test_case_name"),
                "test_case_id": kwargs.get("test_case_id"),
                "test_time": kwargs.get("test_time"),
                "end_time": kwargs.get("end_time"),
                "machine_number": kwargs.get("machine_number"),
                "result": kwargs.get("result"),
                "raw": json.dumps(kwargs.get("raw")),
                "create_time": now,
                "update_time": now,
            }
            if params.get("result") is None:
                params["status"] = 0
            else:
                if params.get("result"):
                    params["status"] = 1
                else:
                    params["status"] = 0

            sql = """
                INSERT INTO public.test_record_items (task_id, test_record_id, project_number, project_name, 
                test_plan_name, test_plan_id, test_sub_plan_name, test_sub_plan_id, test_case_number,
                test_case_name, test_case_id, test_time, end_time, tester_email, tester_name, tester_employee_number,
                machine_number, result, status, raw, create_time, update_time) 
                VALUES (%(task_id)s, %(test_record_id)s, %(project_number)s, %(project_name)s,
                    %(test_plan_name)s, %(test_plan_id)s, %(test_sub_plan_name)s, %(test_sub_plan_id)s,
                    %(test_case_number)s, %(test_case_name)s, %(test_case_id)s, %(test_time)s, %(end_time)s, %(tester_email)s, 
                    %(tester_name)s, %(tester_employee_number)s, %(machine_number)s, %(result)s, %(status)s, 
                    %(raw)s, %(create_time)s, %(update_time)s)
                RETURNING id
                ;
            """.format(table_name=self.table_name)

            test_record_item_id = sql_fetchone(sql, params)[0]

            if kwargs.get("is_start"):
                params = {
                    "id": kwargs.get("test_record_id"),
                    "task_id": kwargs.get("task_id"),
                    "project_number": kwargs.get("project_number"),
                    "project_name": kwargs.get("project_name"),
                    "tester_email": kwargs.get("tester_email"),
                    "tester_name": kwargs.get("tester_name"),
                    "tester_employee_number": kwargs.get("tester_employee_number"),
                    "test_plan_name": kwargs.get("test_plan_name"),
                    "test_plan_id": kwargs.get("test_plan_id"),
                    "test_sub_plan_name": kwargs.get("test_sub_plan_name"),
                    "test_sub_plan_id": kwargs.get("test_sub_plan_id"),
                    "machine_number": kwargs.get("machine_number"),
                    "create_time": now,
                    "update_time": now,
                }
                sql = """
                    INSERT INTO public.test_records (id, task_id, project_number, project_name, 
                    test_plan_name, test_plan_id, test_sub_plan_name, test_sub_plan_id, 
                    tester_email, tester_name, tester_employee_number,
                    machine_number, create_time, update_time) 
                    VALUES (%(id)s, %(task_id)s, %(project_number)s, %(project_name)s,
                        %(test_plan_name)s, %(test_plan_id)s, %(test_sub_plan_name)s, %(test_sub_plan_id)s,
                        %(tester_email)s, %(tester_name)s, %(tester_employee_number)s, %(machine_number)s,
                        %(create_time)s, %(update_time)s)
                    ;
                """.format(table_name=self.table_name)
                sql_execute(sql, params)

            if kwargs.get("is_end"):
                params = {
                    "id": kwargs.get("test_record_id"),
                    "status": 1,
                    "update_time": now
                }
                sql = """
                    UPDATE public.test_records    
                        SET 
                        status = %(status)s,
                        update_time = %(update_time)s
                        WHERE id = %(id)s;
                """
                sql_execute(sql, params)

            return test_record_item_id

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        test_plan_name = kwargs.get("test_plan_name")
        if test_plan_name is not None and test_plan_name != '':
            sql_where_list.append("tr.test_plan_name ~* %(test_plan_name)s")
            params["test_plan_name"] = test_plan_name

        test_sub_plan_name = kwargs.get("test_sub_plan_name")
        if test_sub_plan_name is not None and test_sub_plan_name != '':
            sql_where_list.append("tr.test_sub_plan_name ~* %(test_sub_plan_name)s")
            params["test_sub_plan_name"] = test_sub_plan_name

        tester_name = kwargs.get("tester_name")
        if tester_name is not None and tester_name != '':
            sql_where_list.append("tr.tester_name ~* %(tester_name)s")
            params["tester_name"] = tester_name

        machine_name = kwargs.get("machine_name")
        if machine_name is not None and machine_name != '':
            sql_where_list.append("(m.name ~* %(machine_name)s OR m.m_number ~* %(machine_name)s)")
            params["machine_name"] = machine_name

        creator_employee_number = kwargs.get("creator_employee_number")
        if creator_employee_number is not None and creator_employee_number != '':
            sql_where_list.append("creator_employee_number = %(creator_employee_number)s")
            params["creator_employee_number"] = creator_employee_number

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("tr.project_number = %(project_number)s")
            params["project_number"] = project_number

        test_plan_id = kwargs.get("test_plan_id")
        if test_plan_id is not None and test_plan_id != '':
            sql_where_list.append("tr.test_plan_id = %(test_plan_id)s")
            params["test_plan_id"] = test_plan_id

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY tr.create_time desc "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} as tr left join public.machines as m 
             on tr.machine_number = m.m_number
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT tr.id, tr.project_number, tr.project_name, tr.test_plan_name, tr.test_sub_plan_name, tr.tester_name, 
            tr.machine_number, m.name as machine_name, tr.status, tr.create_time, tr.update_time
             FROM public.test_records as tr left join public.machines as m 
             on tr.machine_number = m.m_number
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        print(sql, params)
        content["results"] = sql_fetchall_dict(sql, params)

        for i in content["results"]:
            i["create_time"] = i["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            i["update_time"] = i["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, "name", "desc", durability, sub_plans, project_name, project_number, project_id, creator_name,
                creator_employee_number, creator_email, product_version, create_time, update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["product_version"] = json.loads(result["product_version"])
            result["sub_plans"] = json.loads(result["sub_plans"])
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
    """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()
            params = {
                "id": kwargs.get("pk"),
                "update_time": now,
            }

            sql_set_list = []

            name = kwargs.get("name")
            if name is not None:
                sql_set_list.append("name = %(name)s")
                params["name"] = name

            desc = kwargs.get("desc")
            if desc is not None:
                sql_set_list.append("\"desc\" = %(desc)s")
                params["desc"] = desc

            durability = kwargs.get("durability")
            if durability is not None:
                sql_set_list.append("durability = %(durability)s")
                params["durability"] = durability

            product_version = kwargs.get("product_version")
            if product_version is not None:
                sql_set_list.append("product_version = %(product_version)s")
                params["product_version"] = product_version

            sub_plans = kwargs.get("sub_plans")
            if sub_plans is not None:
                for sp in sub_plans:
                    sp["test_cases"] = [TestCaseModel().retrieve(i) for i in sp["test_cases"]]
                sql_set_list.append("sub_plans = %(sub_plans)s")
                params["sub_plans"] = json.dumps(sub_plans)

            if not sql_set_list:
                return

            sql_set = ", ".join(sql_set_list)

            sql = """
                UPDATE {table_name}     
                    SET 
                    {sql_set},
                    update_time = %(update_time)s
                    WHERE id = %(id)s;
            """.format(
                table_name=self.table_name,
                sql_set=sql_set,
            )
            sql_execute(sql, params)


class TestRecordItemModel:
    def __init__(self):
        self.table_name = "public.test_record_items"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()

            params = {
                "task_id": kwargs.get("task_id"),
                "test_record_id": kwargs.get("test_record_id"),
                "project_number": kwargs.get("project_number"),
                "project_name": kwargs.get("project_name"),
                "tester_email": kwargs.get("tester_email"),
                "tester_name": kwargs.get("tester_name"),
                "tester_employee_number": kwargs.get("tester_employee_number"),
                "test_plan_name": kwargs.get("test_plan_name"),
                "test_plan_id": kwargs.get("test_plan_id"),
                "test_sub_plan_name": kwargs.get("test_sub_plan_name"),
                "test_sub_plan_id": kwargs.get("test_sub_plan_id"),
                "test_case_number": kwargs.get("test_case_number"),
                "test_case_name": kwargs.get("test_case_name"),
                "test_case_id": kwargs.get("test_case_id"),
                "test_time": kwargs.get("test_time"),
                "machine_number": kwargs.get("machine_number"),
                "result": kwargs.get("result"),
                "raw": json.dumps(kwargs.get("raw")),
                "create_time": now,
                "update_time": now,
            }
            if params.get("result") is None:
                params["status"] = 0
            else:
                if params.get("result"):
                    params["status"] = 1
                else:
                    params["status"] = 0

            sql = """
                INSERT INTO public.test_record_items (task_id, test_record_id, project_number, project_name, 
                test_plan_name, test_plan_id, test_sub_plan_name, test_sub_plan_id, test_case_number,
                test_case_name, test_case_id, test_time, tester_email, tester_name, tester_employee_number,
                machine_number, result, status, raw, create_time, update_time) 
                VALUES (%(task_id)s, %(test_record_id)s, %(project_number)s, %(project_name)s,
                    %(test_plan_name)s, %(test_plan_id)s, %(test_sub_plan_name)s, %(test_sub_plan_id)s,
                    %(test_case_number)s, %(test_case_name)s, %(test_case_id)s, %(test_time)s, %(tester_email)s, 
                    %(tester_name)s, %(tester_employee_number)s, %(machine_number)s, %(result)s, %(status)s, 
                    %(raw)s, %(create_time)s, %(update_time)s)
                ;
            """.format(table_name=self.table_name)

            sql_execute(sql, params)

            if kwargs.get("is_start"):
                params = {
                    "id": kwargs.get("test_record_id"),
                    "task_id": kwargs.get("task_id"),
                    "project_number": kwargs.get("project_number"),
                    "project_name": kwargs.get("project_name"),
                    "tester_email": kwargs.get("tester_email"),
                    "tester_name": kwargs.get("tester_name"),
                    "tester_employee_number": kwargs.get("tester_employee_number"),
                    "test_plan_name": kwargs.get("test_plan_name"),
                    "test_plan_id": kwargs.get("test_plan_id"),
                    "test_sub_plan_name": kwargs.get("test_sub_plan_name"),
                    "test_sub_plan_id": kwargs.get("test_sub_plan_id"),
                    "machine_number": kwargs.get("machine_number"),
                    "create_time": now,
                    "update_time": now,
                }
                sql = """
                    INSERT INTO public.test_records (id, task_id, project_number, project_name, 
                    test_plan_name, test_plan_id, test_sub_plan_name, test_sub_plan_id, 
                    tester_email, tester_name, tester_employee_number,
                    machine_number, create_time, update_time) 
                    VALUES (%(id)s, %(task_id)s, %(project_number)s, %(project_name)s,
                        %(test_plan_name)s, %(test_plan_id)s, %(test_sub_plan_name)s, %(test_sub_plan_id)s,
                        %(tester_email)s, %(tester_name)s, %(tester_employee_number)s, %(machine_number)s,
                        %(create_time)s, %(update_time)s)
                    ;
                """.format(table_name=self.table_name)
                sql_execute(sql, params)

            if kwargs.get("is_end"):
                params = {
                    "id": kwargs.get("test_record_id"),
                    "status": 1,
                    "update_time": now
                }
                sql = """
                    UPDATE public.test_records    
                        SET 
                        status = %(status)s,
                        update_time = %(update_time)s
                        WHERE id = %(id)s;
                """
                sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        test_record_id = kwargs.get("test_record_id")
        if test_record_id is not None and test_record_id != '':
            sql_where_list.append("test_record_id = %(test_record_id)s")
            params["test_record_id"] = test_record_id

        test_case_name_re = kwargs.get("test_case_name_re")
        if test_case_name_re is not None and test_case_name_re != '':
            sql_where_list.append("test_case_name ~* %(test_case_name_re)s")
            params["test_case_name_re"] = test_case_name_re

        result_list = kwargs.get("result_list", [])
        if len(result_list) > 0:
            sql_where_list2 = []
            for i in result_list:
                if i == '1':
                    sql_where_list2.append("result = True")
                elif i == '0':
                    sql_where_list2.append("result = False")
                elif i == '2':
                    sql_where_list2.append("result is Null")
            sql_where_list2 = "(" + " or ".join(sql_where_list2) + ")"
            sql_where_list.append(sql_where_list2)

        status_list = kwargs.get("status_list", [])
        if len(status_list) > 0:
            sql_where_list.append("status in %(status_list)s")
            params["status_list"] = tuple(status_list)

        search_str = kwargs.get("search_str")
        if search_str is not None and search_str != '':
            sql_where_list.append("test_case_name ~* %(search_str)s")
            params["search_str"] = search_str

        project_number = kwargs.get("project_number")
        if project_number is not None and project_number != '':
            sql_where_list.append("project_number = %(project_number)s")
            params["project_number"] = project_number

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY test_time "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, project_number, project_name, test_plan_name, test_sub_plan_name, test_case_number, 
                test_case_name, test_time, tester_name, result, status, is_false_alarm, create_time
             FROM public.test_record_items
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )
        content["results"] = sql_fetchall_dict(sql, params)

        for i in content["results"]:
            i["test_time"] = i["test_time"].strftime("%Y-%m-%d %H:%M:%S")
            i["create_time"] = i["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        SELECT id, test_plan_id, result, status, tester_email, tester_name, tester_employee_number,
        raw, is_false_alarm, false_alarm_reasons, is_manual_determinated, create_time, update_time
        FROM {table_name}
        WHERE id = %(id)s
            LIMIT 1;
    """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["raw"] = json.loads(result["raw"])
            result["create_time"] = result["create_time"].strftime("%Y-%m-%d %H:%M:%S")
            result["update_time"] = result["update_time"].strftime("%Y-%m-%d %H:%M:%S")

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
        DELETE FROM {table_name}
            WHERE id = %(id)s;
    """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, pk, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()
            params = {
                "id": pk,
                "update_time": now,
            }

            sql_set_list = []

            status = kwargs.get("status")
            if status is not None:
                sql_set_list.append("status = %(status)s")
                params["status"] = status

            if not sql_set_list:
                return

            sql_set = ", ".join(sql_set_list)

            sql = """
                UPDATE {table_name}     
                    SET 
                    {sql_set},
                    update_time = %(update_time)s
                    WHERE id = %(id)s;
            """.format(
                table_name=self.table_name,
                sql_set=sql_set,
            )
            sql_execute(sql, params)

            print(sql, params)

    def update_result(self, pk, result):
        params = {
            "id": pk,
            "result": result,
            "update_time": datetime.datetime.now()
        }
        if params["result"]:
            params["status"] = 1
        else:
            params["status"] = 0

        sql = """
            UPDATE {table_name}     
                SET 
                result = %(result)s,
                status = %(status)s,
                is_manual_determinated = true,
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
        )
        sql_execute(sql, params)

    def false_alarm(self, pk, reason):
        params = {
            "id": pk,
            "result": True,
            "false_alarm_reasons": reason,
            "update_time": datetime.datetime.now()
        }

        sql = """
            UPDATE {table_name}     
                SET 
                result = %(result)s,
                status = 1,
                is_false_alarm = true,
                false_alarm_reasons = %(false_alarm_reasons)s,
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
        )
        sql_execute(sql, params)


class TestRecordItemResourceModel:
    def __init__(self):
        self.table_name = "public.test_record_item_resources"

    def create(self, **kwargs):
        with transaction.atomic():
            now = datetime.datetime.now()
            test_record_item_id = kwargs.get("test_record_item_id")
            resource = kwargs.get("resource")

            for res in resource:
                params = {
                    "test_record_item_id": test_record_item_id,
                    "resource_name": res.get("name"),
                    "resource_path": res.get("path"),
                    "create_time": now,
                }

                sql = """
                    INSERT INTO {table_name} (test_record_item_id, resource_name, resource_path, create_time) 
                    VALUES (%(test_record_item_id)s, %(resource_name)s, %(resource_path)s, %(create_time)s)
                    ;
                """.format(table_name=self.table_name)

                sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        params = {}
        sql_where_list = []

        test_record_item_id = kwargs.get("test_record_item_id")
        if test_record_item_id is not None and test_record_item_id != '':
            sql_where_list.append("test_record_item_id = %(test_record_item_id)s")
            params["test_record_item_id"] = test_record_item_id

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT count(*)
            FROM {table_name}
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result.get("count")

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
        SELECT id
            FROM {table_name}
            {sql_where}
            LIMIT 1;
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 1000)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY create_time "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
        SELECT count(*)
            FROM {table_name} 
            {sql_where}
        ;
    """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, test_record_item_id, resource_name, resource_path, create_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )
        content["results"] = sql_fetchall_dict(sql, params)

        for i in content["results"]:
            i["create_time"] = i["create_time"].strftime("%Y-%m-%d %H:%M:%S")

        return content
