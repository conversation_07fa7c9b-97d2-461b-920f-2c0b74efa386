from django.db import models
from rest_framework import serializers


class SdVersionPlan(models.Model):
    title = models.CharField(max_length=255, verbose_name="名称")
    desc = models.TextField(verbose_name="描述", null=True, blank=True)
    begin_date = models.DateField(verbose_name="开始日期", null=True, blank=True)
    end_date = models.DateField(verbose_name="结束日期", null=True, blank=True)
    parent = models.IntegerField(default=0, verbose_name="父ID")
    status = models.CharField(max_length=255, default="pending", verbose_name="状态", null=True, blank=True)
    project_name = models.CharField(max_length=255, verbose_name="项目名称", null=True, blank=True)
    project_number = models.CharField(max_length=255, verbose_name="项目编号", null=True, blank=True)
    deleted = models.BooleanField(default=False, verbose_name="是否删除")
    creator_name = models.Char<PERSON>ield(max_length=255, verbose_name="创建人姓名", null=True, blank=True)
    creator_email = models.CharField(max_length=255, verbose_name="创建人邮箱", null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updater_name = models.CharField(max_length=255, verbose_name="更新人姓名", null=True, blank=True)
    updater_email = models.CharField(max_length=255, verbose_name="更新人邮箱", null=True, blank=True)
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'sd_version_plan'
        managed = False
        app_label = 'sd_versions'
        ordering = ['-create_time']

    def __str__(self):
        return self.title


class SdVersionPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = SdVersionPlan
        fields = '__all__'


class SdVersionPlanModel:
    def create(self, **kwargs):
        SdVersionPlan.objects.create(**kwargs)

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        objs = SdVersionPlan.objects.filter(deleted=False)

        count = objs.count()

        objs = objs[(page - 1) * pagesize: page * pagesize]

        results = SdVersionPlanSerializer(objs, many=True).data

        return {"count": count, "results": results}

    def retrieve(self, pk):

        obj = SdVersionPlan.objects.get(pk=pk)

        obj = SdVersionPlanSerializer(obj).data

        return obj

    def update(self, pk, **kwargs):
        SdVersionPlan.objects.filter(pk=pk).update(**kwargs)

    def delete(self, pk):
        SdVersionPlan.objects.filter(pk=pk).update(deleted=True)
