import traceback
import logging
import json
import datetime

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from .serializers import (
    ExceptionSubmitSerializer, SendExceptionMsgSerializer, ExceptionListSerializer,
)
from .models import (
    ProcessMonitorModel
)
from utils.fs_app import fs_app
from projects.models import ProjectModel

logger = logging.getLogger("machine")


class ExceptionsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            serializer = ExceptionListSerializer(data=request.query_params)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            r = ProcessMonitorModel().list(
                **serializer.validated_data
            )

            return Response({"err_code": 0, "msg": "ok", "data": r}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ExceptionSubmitView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = ExceptionSubmitSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user

            ProcessMonitorModel().create(**serializer.validated_data, tester_name=user.username, tester_mail=user.email)

            return Response({"err_code": 0, "msg": "ok"}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)


class SendExceptionMsgView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = SendExceptionMsgSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({"err_code": 1, "msg": str(serializer.errors)}, status.HTTP_400_BAD_REQUEST)

            user = request.user

            project_number = serializer.validated_data.get("project_number")
            test_plan_name = serializer.validated_data.get("test_plan_name")
            code = serializer.validated_data.get("code")
            name = serializer.validated_data.get("name")
            cur_time = serializer.validated_data.get("cur_time")
            app_name = serializer.validated_data.get("app_name")
            station_name = serializer.validated_data.get("station_name")

            r = ProjectModel().list(number=project_number, pagesize=1)
            if not r.get("results"):
                return Response({"err_code": 1, "msg": "项目编号对应的项目未配置"},
                                status.HTTP_400_BAD_REQUEST)
            p = r.get("results")[0]
            related_people = p.get("related_people")
            start_time = p.get("msg_effective_time_start")
            end_time = p.get("msg_effective_time_end")
            if not related_people:
                return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                status.HTTP_400_BAD_REQUEST)
            related_people = json.loads(related_people)
            if not related_people:
                return Response({"err_code": 1, "msg": "项目相关人未配置"},
                                status.HTTP_400_BAD_REQUEST)
            related_people = [i.get("email") for i in related_people]
            now = datetime.datetime.now().time()
            if start_time and now < start_time:
                return Response({"err_code": 0, "msg": "不在配置时间段内"}, status.HTTP_200_OK)
            if end_time and now > end_time:
                return Response({"err_code": 0, "msg": "不在配置时间段内"}, status.HTTP_200_OK)

            err_msg = []
            for i in related_people:
                f, data = fs_app.send_monitor_exception_msg(
                    receive_email=i, project=f"{p.get('name')}({p.get('number')})",
                    plan=test_plan_name, tester=user.username,
                    code=code, name=name, cur_time=cur_time,
                    app_name=app_name, station_name=station_name,
                )
                if not f:
                    err_msg.append(data.get("msg"))

            if err_msg:
                return Response({"err_code": 4, "msg": "发送失败", "err_msg": err_msg},
                                status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({"err_code": 0, "msg": "ok", "err_msg": err_msg}, status.HTTP_200_OK)
        except Exception:
            logger.error(traceback.format_exc())
            return Response({"err_code": 2, "msg": "服务器错误。"}, status.HTTP_500_INTERNAL_SERVER_ERROR)
