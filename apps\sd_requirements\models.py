import json

from django.db import models, transaction
from rest_framework import serializers
from utils.sql_helper import sql_fetchall_dict, sql_fetchone_dict


class SdRequirement(models.Model):
    title = models.CharField(max_length=255, verbose_name="名称")
    module = models.CharField(max_length=255, verbose_name="模块")
    plan = models.IntegerField(verbose_name="计划")
    desc = models.TextField(verbose_name="描述")
    verify = models.TextField(verbose_name="验证")
    verify_method = models.JSONField(verbose_name="验证方法")
    estimate = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="预估工时")
    pri = models.IntegerField(verbose_name="优先级")
    source = models.CharField(max_length=255, verbose_name="来源")
    source_note = models.CharField(max_length=255, verbose_name="来源备注")
    status = models.Char<PERSON><PERSON>(max_length=255, default='draft', verbose_name="状态")
    stage = models.CharField(max_length=255, default='wait', verbose_name="阶段")
    version = models.IntegerField(verbose_name="版本", default=1)
    project_name = models.CharField(max_length=255, verbose_name="项目名称")
    project_number = models.CharField(max_length=255, verbose_name="项目编号")
    close_reason = models.CharField(max_length=255, verbose_name="关闭原因")
    closer_name = models.CharField(max_length=255, verbose_name="关闭人姓名")
    closer_email = models.CharField(max_length=255, verbose_name="关闭人邮箱")
    close_time = models.DateTimeField(verbose_name="关闭时间")
    assignee_name = models.CharField(max_length=255, verbose_name="指派人姓名")
    assignee_email = models.CharField(max_length=255, verbose_name="指派人邮箱")
    assign_time = models.DateTimeField(verbose_name="指派时间")
    creator_name = models.CharField(max_length=255, verbose_name="创建人姓名")
    creator_email = models.CharField(max_length=255, verbose_name="创建人邮箱")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updater_name = models.CharField(max_length=255, verbose_name="更新人姓名")
    updater_email = models.CharField(max_length=255, verbose_name="更新人邮箱")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    deleted = models.BooleanField(default=False, verbose_name="是否删除")

    class Meta:
        db_table = 'sd_requirement'
        managed = False
        app_label = "sd_requirements"


class SdRequirementSerializer(serializers.ModelSerializer):
    class Meta:
        model = SdRequirement
        fields = '__all__'


class SdRequirementModel:
    def create(self, **kwargs):

        with transaction.atomic():
            req = SdRequirement.objects.create(**kwargs)
            create_sd_requirement_desc(req)

    def retrieve(self, pk):
        sql = """
            SELECT r.id, r.title, r.module, r.plan, r.desc, r.verify, r.verify_method, r.estimate, r.pri, 
                r.source, r.source_note, r.status, r.version, r.project_name, r.project_number, 
                r.creator_name, r.creator_email, r.create_time, r.updater_name, r.updater_email, 
                r.update_time, p.title as plan_title
            FROM public.sd_requirement as r
                left join public.sd_version_plan as p on r.plan = p.id
            WHERE r.id = %(id)s
            ;
        """
        r = sql_fetchone_dict(sql, {"id": pk})

        r["verify_method"] = json.loads(r["verify_method"])

        return r

    def update(self, pk, **kwargs):
        SdRequirement.objects.filter(id=pk).update(**kwargs)

    def change(self, pk, **kwargs):
        req = SdRequirement.objects.get(pk=pk)

        with transaction.atomic():
            req.version += 1
            req.title = kwargs.get("title")
            req.desc = kwargs.get("desc")
            req.verify = kwargs.get("verify")
            req.verify_method = kwargs.get("verify_method")
            req.updater_name = kwargs.get("updater_name")
            req.updater_email = kwargs.get("updater_email")
            req.save()

            create_sd_requirement_desc(req)

    def delete(self, pk):
        SdRequirement.objects.filter(id=pk).update(deleted=True)

    def list(self, **kwargs):
        params = {}
        sql_where_list = ["r.deleted = False"]

        plan = kwargs.get("plan")
        if plan is not None and plan != '':
            sql_where_list.append("r.plan = %(plan)s")
            params["plan"] = plan

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        sql_order_by = " ORDER BY id desc "

        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)
        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM public.sd_requirement as r
                {sql_where}
            ;
        """.format(
            sql_where=sql_where
        )
        count = sql_fetchone_dict(sql, params).get("count")

        sql = """
            SELECT r.id, r.title, r.module, r.plan, r.desc, r.verify, r.verify_method, r.estimate, r.pri, 
                r.source, r.source_note, r.status, r.version, r.project_name, r.project_number, 
                r.creator_name, r.creator_email, r.create_time, r.updater_name, r.updater_email, 
                r.update_time, p.title as plan_title
            FROM public.sd_requirement as r
                left join public.sd_version_plan as p on r.plan = p.id
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        results = sql_fetchall_dict(sql, params)

        return {"count": count, "results": results}

    def version_desc(self, requirement, version):
        obj = SdRequirementDesc.objects.get(requirement=requirement, version=version)
        return SdRequirementDescSerializer(obj).data


class SdRequirementDesc(models.Model):
    requirement = models.IntegerField(verbose_name="需求", primary_key=True)
    version = models.IntegerField(verbose_name="版本", primary_key=True)
    title = models.CharField(max_length=255, verbose_name="标题")
    desc = models.TextField(verbose_name="描述")
    verify = models.TextField(verbose_name="验证")
    verify_method = models.JSONField(verbose_name="验证方法")
    creator_name = models.CharField(max_length=255, verbose_name="创建人姓名")
    creator_email = models.CharField(max_length=255, verbose_name="创建人邮箱")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updater_name = models.CharField(max_length=255, verbose_name="更新人姓名")
    updater_email = models.CharField(max_length=255, verbose_name="更新人邮箱")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'sd_requirement_desc'
        managed = False
        app_label = "sd_requirements"


class SdRequirementDescSerializer(serializers.ModelSerializer):
    class Meta:
        model = SdRequirementDesc
        fields = '__all__'


def create_sd_requirement_desc(req):
    SdRequirementDesc.objects.create(
        requirement=req.id,
        version=req.version,
        title=req.title,
        desc=req.desc,
        verify=req.verify,
        verify_method=req.verify_method,
        creator_name=req.updater_name,
        creator_email=req.updater_email,
        updater_name=req.updater_name,
        updater_email=req.updater_email,
    )

