

import logging
import traceback
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db import transaction
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.cache import cache
from django.core.serializers import serialize
import json
import os
import shutil
import time
from pathlib import Path

logger = logging.getLogger("code_management")
from apps.code_management.grpc.host_machine_grpc import host_machine_client
from apps.code_management.grpc.git_grpc import git_client
from apps.code_management.models.project_models import ProjectInfoSerializer, CodeProjectInfo, CodeBranchInfo, BranchInfoSerializer
from apps.code_management.models.config_models import NodeTree, NodeTreeSerializer
from apps.code_management.models.code_models import CodePath
from apps.code_management.models.config_models import Level1Group, Level2MainParam, Level3SubParam
from users.models import UserFSInfo
from utils.fs_service import FSService
from utils.fs_app import fs_app


local_path = "/home/<USER>/hwcp"
config_path = "/home/<USER>/hwcp/gRPC_Server/HwcpTest"
config_url = "http://*********/python-team/HwcpTest.git"
config_branch = "main"
sdk_git_path = "http://*********/"



class SDKMapView(APIView):
    """从三张数据表提取数据，生成指定结构的思维导图数据（函数拆分优化版）"""

    def post(self, request):
        """主入口：协调各步骤生成思维导图数据"""
        logger.info("开始从数据表获取功能模块等级数据")
        try:
            # 1. 定义配置项与显示名称的映射（核心配置）
            config_mapping = self._get_config_mapping()

            # 2. 构建主结构
            mindmap_data = self._build_base_structure()

            # 3. 为每个配置项生成子节点
            for var_name, label_prefix in config_mapping.items():
                logger.info("var_name:%s", var_name)
                # 判断是否是memory
                if var_name == "Config_Memory":
                    mindmap_data["children"].append(
                        {
                            "label": "内存配置(Config_Memory)",
                            "children": [
                                {
                                    'label': 'DataFlash信息',
                                    'children': [
                                        {
                                            'label': 'Flash超时时间',
                                            'children': []
                                        },
                                        {
                                            'label': '最小可编程单元',
                                            'children': []
                                        },
                                        {
                                            'label': 'DataFlash基地址',
                                            'children': []
                                        },
                                        {
                                            'label': 'DataFlash最大地址',
                                            'children': []
                                        }
                                ]}
                            ]
                        }
                    )
                else:
                    config_node = self._build_config_node(var_name, label_prefix)
                    mindmap_data["children"].append(config_node)


            logger.info("功能模块等级数据获取完成")
            logger.info("mindmap_data: %s", mindmap_data)
            return Response({"status": 1, "msg": "数据获取成功", "mindmapData": mindmap_data})
        except Exception as e:
            logger.info("数据获取错误：%s", e)
            return Response({"status": 0, "msg": "数据获取失败"})




    def _get_config_mapping(self):
        """定义配置项与显示名称的映射关系（与目标结构严格对应）"""
        return {
            "Config_Brightness": "亮度配置",
            "Config_Memory": "内存配置",
            "Config_Temperature": "温度配置",
            "Config_Touch": "触摸配置",
            "Config_System": "系统配置",
            "Config_Power": "电源配置"
        }

    def _build_base_structure(self):
        """构建思维导图的基础结构（根节点）"""
        return {
            "label": "SDK(2.0.0)",
            "children": []
        }

    def _build_config_node(self, var_name, label_prefix):
        """构建单个配置项节点（如亮度配置、温度配置）"""
        config_node = {
            "label": f"{label_prefix} ({var_name})",
            "children": []
        }

        # 获取该配置项下的所有参数组（去重）
        level1_groups = Level1Group.objects.filter(
            var_name=var_name
        ).values_list('group', flat=True).distinct()

        # 为每个参数组生成节点
        for group_name in level1_groups:
            group_node = self._build_group_node(var_name, group_name)
            config_node["children"].append(group_node)

        return config_node

    def _build_group_node(self, var_name, group_name):
        """构建参数组节点（如亮度等级、温度检测任务参数设定）"""
        group_node = {
            "label": group_name,
            "children": []
        }

        # 获取该参数组下的所有二级参数
        level2_params = Level2MainParam.objects.filter(
            var_name=var_name,
            group=group_name
        ).order_by('id')

        # 为每个二级参数生成节点
        for param in level2_params:
            param_node = self._build_param_node(param)
            group_node["children"].append(param_node)

        return group_node

    def _build_param_node(self, param):
        """构建二级参数节点（含特殊格式处理和三级子参数）"""
        # 处理参数类型和默认值
        param_type = self._format_param_type(param)
        default_val = self._format_default_value(param)
        unit = self._get_param_unit(param.parent_param_key, param_type)

        # 基础参数节点
        param_label = f"{param.parent_param_name} ({param.parent_param_key}, {param_type}, 默认{default_val}{unit})"
        if param.parent_param_name == "输出类型" and param.parent_param_key == "DEBUG_INFO_TYPE":
            param_label = f"{param.parent_param_name} ({param.parent_param_key}, {param_type})"
        param_node = {"label": param_label}

        # 处理三级子参数
        self._add_level3_children(param_node, param.parent_param_key)

        # 特殊处理调试信息类型的子节点
        if param.parent_param_key == "DEBUG_INFO_TYPE":
            param_node["children"] = self._get_debug_info_children()

        return param_node

    def _format_param_type(self, param):
        """格式化参数类型（尤其是枚举类型）"""
        if param.type != 'enum':
            return param.type

        # 枚举类型特殊处理
        if 'EnumBacklightDirectType' in param.desc:
            return "enum: 直接/渐变/定时调光"
        elif 'EnumBacklighPWMMode' in param.desc:
            return "enum: PWM/IIC调节"
        elif 'enum_Internal_Wdg' in param.desc:
            return "enum: 内部/外部/无效, 默认内部"
        return "enum"

    def _format_default_value(self, param):
        """格式化默认值（处理空值情况）"""
        default_val = param.default_value
        if default_val in [None, 'nan', '']:
            return '无'
        return default_val

    def _get_param_unit(self, param_key, param_type):
        """根据参数键和类型自动添加单位（ms/℃等）"""
        if 'TIME' in param_key or 'INTERVAL' in param_key or 'DEBOUNCE' in param_key:
            return 'ms'
        if 'TEMP' in param_key and param_type.startswith('uint8'):
            return '℃'
        return ''

    def _add_level3_children(self, param_node, parent_param_key):
        """为参数节点添加三级子参数（从Level3SubParam表关联）"""
        level3_params = Level3SubParam.objects.filter(
            parent_param_key=parent_param_key
        )

        if level3_params.exists():
            param_node["children"] = []
            for sub_param in level3_params:
                sub_label = f"{sub_param.sub_param_name} ({sub_param.sub_param_key}, {sub_param.param_type}, 默认{sub_param.default_value})"
                param_node["children"].append({"label": sub_label})

    def _get_debug_info_children(self):
        """生成调试信息类型的固定子节点"""
        return [
            {"label": "错误信息 (0x01, 启用)"},
            {"label": "警告信息 (0x02, 启用)"},
            {"label": "一般信息打印 (0x04, 启用)"},
            {"label": "调试信息 (0x08, 启用)"},
            {"label": "定制信息 (0x10, 启用)"},
            {"label": "字段信息 (0x20, 启用)"}
        ]


