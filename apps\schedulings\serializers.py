from rest_framework import serializers


class SchedulingListSerializer(serializers.Serializer):
    time = serializers.ListField(child=serializers.DateTimeField(), min_length=0, required=False, allow_null=True)
    exception_description = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    inspectors = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    shift_personnel = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)


class SchedulingSerializer(serializers.Serializer):
    time = serializers.ListField(child=serializers.DateTimeField(), min_length=0, required=False, allow_null=True)
    exception_description = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    inspectors = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    shift_personnel = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)


class SchedulingUpdateSerializer(serializers.Serializer):
    time = serializers.ListField(child=serializers.DateTimeField(), min_length=0, required=False, allow_null=True)
    exception_description = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    inspectors = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    shift_personnel = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)